
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON>esponse, StreamingResponse
from typing import Any, AsyncGenerator
import json
import asyncio

from src.infrastructure.config.config import Config<PERSON>anager
from src.infrastructure.config.factory import create_conversation_service_from_config
from src.infrastructure.log.unified_logger import debug, info, warning, error, critical, start_session
from src.models import (
    MessageRequest, MessageResponse,
    SessionRequest, SessionResponse,
    MessagesRequest, MessagesResponse,
    DeleteRequest, DeleteResponse,
    HealthResponse, TitlesRequest, TitlesResponse,
    SessionTitle
)

# Initialize FastAPI app
app = FastAPI(
    title="Location Advice API",
    description="API for location-based advice and recommendations",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize config manager
config_manager = ConfigManager()

# Create conversation service
conversation_service = create_conversation_service_from_config()

# Add startup validation
if conversation_service is None:
    print("❌ ERROR: Failed to initialize conversation service!")
    print("This is likely due to missing environment variables or configuration issues.")
    print("Please check:")
    print("1. OPENROUTER_API_KEY is set in .env file")
    print("2. All required dependencies are installed")
    print("3. Configuration files are properly set up")
else:
    print("✅ Conversation service initialized successfully")


# Simple function to start logging session
def setup_logging_session(user_id: str, session_id: str):
    """Setup logging session for the request"""
    start_session(user_id, session_id)


@app.middleware("http")
async def log_requests(request: Request, call_next):
    # print(f"Request: {request.method} {request.url}")
    # print(f"Client: {request.client}")
    # print(f"Headers: {request.headers}")
    response = await call_next(request)
    return response


@app.post("/message")
async def process_message(request_data: MessageRequest, request: Request):
    """Process a user message and return a response"""
    # Setup logging session FIRST, before any other operations
    setup_logging_session(request_data.user_id, request_data.session_id)

    info(
        f"Processing message for user {request_data.user_id}, session {request_data.session_id}")

    # Get the flow from the request state
    request_flow = getattr(request.state, 'flow', None)

    # Validate that required parameters are present and not None
    # Note: latitude and longitude can be None (location services disabled)
    required_params = {
        "user_id": request_data.user_id,
        "session_id": request_data.session_id,
        "message": request_data.message,
        "search_radius": request_data.search_radius,
        "num_candidates": request_data.num_candidates
    }

    missing_params = [param for param,
                      value in required_params.items() if value is None]
    if missing_params:
        error_msg = f"Missing required parameters: {', '.join(missing_params)}"
        error(error_msg)
        raise HTTPException(status_code=400, detail=error_msg)

    # Log coordinate availability for debugging
    if request_data.latitude is None or request_data.longitude is None:
        info(
            f"Processing message without location data: lat={request_data.latitude}, lon={request_data.longitude}")

    try:
        # Check if conversation service is initialized
        if conversation_service is None:
            raise HTTPException(
                status_code=500, detail="Conversation service not initialized. Check server logs for configuration issues.")

        # Call the conversation service with the request data
        response = conversation_service.process_message(
            user_id=request_data.user_id,
            session_id=request_data.session_id,
            message=request_data.message,
            latitude=request_data.latitude,
            longitude=request_data.longitude,
            search_radius=request_data.search_radius,
            num_candidates=request_data.num_candidates,
            request_flow=request_flow
        )

        # Ensure response is a dictionary
        if not isinstance(response, dict):
            warning(f"Response is not a dictionary: {type(response)}")
            response = {
                "response": "I encountered an error while processing your request. Please try again.",
                "top_candidates": []
            }

        # Create a standardized response - simple and direct
        top_candidates = response.get("top_candidates", [])
        debug(f"Service returned {len(top_candidates)} candidates")

        # Build response dictionary conditionally
        # Ensure response is always a string
        response_text = response.get("response", "")
        if not isinstance(response_text, str):
            # If response is not a string, try to extract text from it
            if isinstance(response_text, dict):
                # Try common text fields
                for field in ["response", "text", "content", "message"]:
                    if field in response_text and isinstance(response_text[field], str):
                        response_text = response_text[field]
                        break
                else:
                    # If no text field found, convert to string
                    warning(
                        f"Response field is not a string, converting dict to string: {response_text}")
                    response_text = str(response_text)
            else:
                # Convert other types to string
                warning(
                    f"Response field is not a string, converting {type(response_text)} to string: {response_text}")
                response_text = str(response_text)

        message_response_dict = {
            "response": response_text,
            "top_candidates": top_candidates,
            "latitude": request_data.latitude,
            "longitude": request_data.longitude,
            "search_radius": request_data.search_radius
        }

        # Only include session_title if it has a non-empty value
        session_title = response.get("session_title", "")
        if session_title and session_title.strip():
            message_response_dict["session_title"] = session_title

        # Create MessageResponse for validation but return JSONResponse to exclude None values
        message_response: MessageResponse = MessageResponse(
            **message_response_dict)

        # Convert to dict and exclude None values for the actual response
        response_data = message_response.model_dump(exclude_none=True)

        info(f"API returning {len(top_candidates)} candidates to client")

        return JSONResponse(content=response_data)
    except Exception as e:
        error(f"Error processing message: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error processing message: {str(e)}")


@app.post("/message/stream")
async def process_message_stream(request_data: MessageRequest, request: Request):
    """Process a message with streaming response for faster user experience"""
    # Setup logging session FIRST, before any other operations
    setup_logging_session(request_data.user_id, request_data.session_id)

    if conversation_service is None:
        raise HTTPException(
            status_code=500, detail="Conversation service not initialized. Check server logs for configuration issues.")

    async def generate_stream() -> AsyncGenerator[str, None]:
        """Generate streaming response chunks"""
        try:
            # Start processing the message
            info(
                f"Starting streaming message processing for user {request_data.user_id}")

            # Send initial chunk with metadata
            initial_data = {
                "type": "metadata",
                "latitude": request_data.latitude,
                "longitude": request_data.longitude,
                "search_radius": request_data.search_radius,
                "status": "processing"
            }
            yield f"data: {json.dumps(initial_data)}\n\n"

            # Process the message with streaming enabled
            response = conversation_service.process_message(
                user_id=request_data.user_id,
                session_id=request_data.session_id,
                message=request_data.message,
                latitude=request_data.latitude,
                longitude=request_data.longitude,
                search_radius=request_data.search_radius,
                num_candidates=request_data.num_candidates,
                streaming=True
            )

            # Ensure response is a dictionary
            if not isinstance(response, dict):
                response = {
                    "response": "I encountered an error while processing your request. Please try again.",
                    "top_candidates": []
                }

            # Stream the response text in chunks (word by word for fast response)
            response_text = response.get("response", "")
            words = response_text.split()

            for i, word in enumerate(words):
                chunk_data = {
                    "type": "text_chunk",
                    "content": word + (" " if i < len(words) - 1 else ""),
                    "chunk_index": i,
                    "is_final_text_chunk": i == len(words) - 1
                }
                yield f"data: {json.dumps(chunk_data)}\n\n"
                # Small delay to simulate streaming (can be removed for faster streaming)
                await asyncio.sleep(0.05)

            # Send candidates data
            top_candidates = response.get("top_candidates", [])
            candidates_data = {
                "type": "candidates",
                "top_candidates": top_candidates,
                "total_candidates": len(top_candidates)
            }
            yield f"data: {json.dumps(candidates_data)}\n\n"

            # Send session title if available
            session_title = response.get("session_title", "")
            if session_title and session_title.strip():
                title_data = {
                    "type": "session_title",
                    "session_title": session_title
                }
                yield f"data: {json.dumps(title_data)}\n\n"

            # Send final completion signal
            final_data = {
                "type": "complete",
                "status": "success",
                "total_chunks": len(words)
            }
            yield f"data: {json.dumps(final_data)}\n\n"

            info(f"Streaming completed for user {request_data.user_id}")

        except Exception as e:
            error(f"Error in streaming response: {str(e)}")
            error_data = {
                "type": "error",
                "error": str(e),
                "status": "failed"
            }
            yield f"data: {json.dumps(error_data)}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
            "Access-Control-Allow-Methods": "*"
        }
    )


@app.post("/session", response_model=SessionResponse)
async def create_new_session(request_data: SessionRequest, request: Request) -> SessionResponse:
    """Create a new session using standalone component"""
    if conversation_service is None:
        raise HTTPException(
            status_code=500, detail="Conversation service not initialized. Check server logs for configuration issues.")
    session_id = conversation_service.create_session(request_data.user_id)
    return SessionResponse(session_id=session_id)


@app.post("/messages", response_model=MessagesResponse)
async def get_messages(request_data: MessagesRequest, request: Request) -> MessagesResponse:
    """Get the raw messages for a session using standalone component"""
    if conversation_service is None:
        raise HTTPException(
            status_code=500, detail="Conversation service not initialized. Check server logs for configuration issues.")
    messages = conversation_service.get_message_history(
        request_data.user_id, request_data.session_id)
    return MessagesResponse(messages=messages)


@app.post("/delete", response_model=DeleteResponse)
async def delete_session(request_data: DeleteRequest, request: Request) -> DeleteResponse:
    """Delete a session using standalone component"""
    result = conversation_service.delete_session(
        request_data.user_id, request_data.session_id)
    return DeleteResponse(message=result["message"])


@app.post("/titles", response_model=TitlesResponse)
async def get_session_titles(request_data: TitlesRequest, request: Request) -> TitlesResponse:
    """Get all session titles using standalone component"""
    titles = conversation_service.get_session_titles(request_data.user_id)
    session_titles = [
        SessionTitle(
            session_id=title["session_id"],
            title=title["title"],
            created_at=title["created_at"]
        )
        for title in titles
    ]
    return TitlesResponse(titles=session_titles)


@app.get("/health", response_model=HealthResponse)
async def health_check() -> HealthResponse:
    """Health check endpoint using standalone component"""
    health_data = conversation_service.check_health()
    return HealthResponse(status=health_data["status"])
