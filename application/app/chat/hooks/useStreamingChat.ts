/** @format */

import { useCallback, useRef } from 'react';

export interface StreamingChatOptions {
	onMessageUpdate: (message: string) => void;
	onTopCandidatesUpdate: (candidates: any[]) => void;
	onError: (error: string) => void;
	onComplete: () => void;
	onProcessingStart: () => void;
}

export interface StreamingResponse {
	type:
		| 'connection'
		| 'user_message'
		| 'processing_start'
		| 'llm_response'
		| 'completion'
		| 'error'
		| 'parse_error';
	status?: string;
	message?: string;
	data?: any;
	error?: string;
	timestamp: string;
}

export function useStreamingChat() {
	const eventSourceRef = useRef<EventSource | null>(null);
	const abortControllerRef = useRef<AbortController | null>(null);

	const sendStreamingMessage = useCallback(
		async (
			payload: {
				message: string;
				sessionId: string;
				userId: string;
				userLat?: number | null;
				userLng?: number | null;
			},
			options: StreamingChatOptions
		) => {
			// Clean up any existing connections
			if (eventSourceRef.current) {
				eventSourceRef.current.close();
				eventSourceRef.current = null;
			}

			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
			}

			try {
				// Create new abort controller for this request
				abortControllerRef.current = new AbortController();

				// Send the initial request to start streaming
				const response = await fetch('/api/chat/message/stream', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify(payload),
					signal: abortControllerRef.current.signal,
				});

				if (!response.ok) {
					throw new Error(`HTTP ${response.status}: ${response.statusText}`);
				}

				// Check if response is actually a stream
				const contentType = response.headers.get('content-type');
				if (!contentType?.includes('text/event-stream')) {
					throw new Error('Response is not a valid event stream');
				}

				// Create EventSource-like reader for the response stream
				const reader = response.body?.getReader();
				if (!reader) {
					throw new Error('No response body available');
				}

				const decoder = new TextDecoder();
				let buffer = '';
				let currentMessage = '';
				let currentCandidates: any[] = [];

				try {
					while (true) {
						const { done, value } = await reader.read();

						if (done) {
							break;
						}

						buffer += decoder.decode(value, { stream: true });

						// Process complete lines
						const lines = buffer.split('\n');
						buffer = lines.pop() || ''; // Keep incomplete line in buffer

						for (const line of lines) {
							if (line.trim() === '') continue;

							if (line.startsWith('data: ')) {
								const data = line.slice(6).trim();

								if (data === '[DONE]') {
									options.onComplete();
									break;
								}

								try {
									const parsed: StreamingResponse = JSON.parse(data);

									switch (parsed.type) {
										case 'connection':
											console.log('🔗 Streaming connection established');
											break;

										case 'user_message':
											console.log('👤 User message confirmed:', parsed.message);
											break;

										case 'processing_start':
											console.log('⚡ Processing started');
											options.onProcessingStart();
											break;

										case 'llm_response':
											if (parsed.data) {
												// Handle different types of LLM responses
												if (
													parsed.data.type === 'text_chunk' &&
													parsed.data.content
												) {
													// Text chunk from LLM engine
													currentMessage += parsed.data.content;
													options.onMessageUpdate(currentMessage);
													console.log(
														'📝 Text chunk received:',
														parsed.data.content,
														'Total message:',
														currentMessage
													);
												} else if (parsed.data.response) {
													// Incremental text response (fallback)
													currentMessage += parsed.data.response;
													options.onMessageUpdate(currentMessage);
												} else if (parsed.data.message_chunk) {
													// Alternative chunk format (fallback)
													currentMessage += parsed.data.message_chunk;
													options.onMessageUpdate(currentMessage);
												} else if (parsed.data.top_candidates) {
													// Location candidates
													currentCandidates = parsed.data.top_candidates;
													options.onTopCandidatesUpdate(currentCandidates);
												} else if (parsed.data.final_response) {
													// Final complete response
													currentMessage = parsed.data.final_response;
													options.onMessageUpdate(currentMessage);

													if (parsed.data.top_candidates) {
														currentCandidates = parsed.data.top_candidates;
														options.onTopCandidatesUpdate(currentCandidates);
													}
												} else if (parsed.data.type === 'metadata') {
													// Metadata from LLM engine (location info, etc.)
													console.log('📍 Metadata received:', parsed.data);
												}
											}
											break;

										case 'completion':
											console.log('✅ Streaming completed');
											options.onComplete();
											break;

										case 'error':
											console.error('❌ Streaming error:', parsed.error);
											options.onError(
												parsed.error || 'Unknown streaming error'
											);
											break;

										case 'parse_error':
											console.warn('⚠️ Parse error in stream:', parsed.error);
											// Don't stop streaming for parse errors, just log them
											break;

										default:
											console.log(
												'📦 Unknown stream message type:',
												parsed.type
											);
									}
								} catch (parseError) {
									console.error(
										'Failed to parse streaming data:',
										parseError,
										'Raw data:',
										data
									);
									// Continue processing other messages
								}
							}
						}
					}
				} finally {
					reader.releaseLock();
				}
			} catch (error) {
				if (error instanceof Error && error.name === 'AbortError') {
					console.log('🛑 Streaming request was aborted');
					return;
				}

				console.error('❌ Streaming request failed:', error);
				options.onError(
					error instanceof Error ? error.message : 'Unknown streaming error'
				);
			}
		},
		[]
	);

	const stopStreaming = useCallback(() => {
		if (eventSourceRef.current) {
			eventSourceRef.current.close();
			eventSourceRef.current = null;
		}

		if (abortControllerRef.current) {
			abortControllerRef.current.abort();
			abortControllerRef.current = null;
		}
	}, []);

	// Cleanup on unmount
	const cleanup = useCallback(() => {
		stopStreaming();
	}, [stopStreaming]);

	return {
		sendStreamingMessage,
		stopStreaming,
		cleanup,
	};
}
