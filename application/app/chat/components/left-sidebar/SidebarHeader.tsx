/** @format */

import { IconButton } from 'app/chat/components/ui';
import { colors } from 'app/colors';
import Image from 'next/image';
import React from 'react';
import { FaBars } from 'react-icons/fa';

interface SidebarHeaderProps {
	onClose: () => void;
}

const SidebarHeader: React.FC<SidebarHeaderProps> = ({ onClose }) => {
	return (
		<div
			className='w-full h-16 px-4 py-3 flex justify-between items-center border-b backdrop-blur-sm'
			style={{
				borderColor: colors.ui.gray200,
				background: `rgba(255, 255, 255, 0.8)`,
			}}>
			<div className='flex items-center gap-3'>
				<div className='w-10 h-10 relative'>
					<Image
						src='/logo/512x512.png'
						alt='Wizlop Logo'
						width={40}
						height={40}
						className='rounded-lg shadow-sm'
					/>
				</div>
				<div className='flex flex-col'>
					<h2
						className='text-lg font-semibold leading-tight tracking-tight'
						style={{
							color: colors.brand.navy,
							fontFamily:
								'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
						}}>
						Wizlop
					</h2>
					<p
						className='text-sm leading-tight font-medium'
						style={{
							color: colors.neutral.slateGray,
							fontFamily:
								'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
						}}>
						Chat History
					</p>
				</div>
			</div>
			<IconButton
				onClick={onClose}
				icon={<FaBars />}
				variant='default'
			/>
		</div>
	);
};

export default SidebarHeader;
