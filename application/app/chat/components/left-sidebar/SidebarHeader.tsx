/** @format */

import { IconButton } from 'app/chat/components/ui';
import { colors } from 'app/colors';
import Image from 'next/image';
import React from 'react';
import { FaBars } from 'react-icons/fa';

interface SidebarHeaderProps {
	onClose: () => void;
}

const SidebarHeader: React.FC<SidebarHeaderProps> = ({ onClose }) => {
	return (
		<div
			className='w-full h-16 px-4 flex justify-between items-center border-b backdrop-blur-sm'
			style={{
				borderColor: colors.ui.gray200,
				background: `rgba(255, 255, 255, 0.8)`,
			}}>
			<div className='flex items-center gap-3'>
				<div className='w-8 h-8 relative'>
					<Image
						src='/logo/512x512.png'
						alt='Wizlop Logo'
						width={32}
						height={32}
						className='rounded-lg shadow-sm'
					/>
				</div>
				<div className='flex flex-col justify-center'>
					<h2
						className='text-sm font-semibold leading-none tracking-tight'
						style={{
							color: colors.brand.navy,
							fontFamily:
								'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
							lineHeight: '1.2',
						}}>
						Wizlop
					</h2>
					<p
						className='text-xs leading-none font-medium'
						style={{
							color: colors.neutral.slateGray,
							fontFamily:
								'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
							lineHeight: '1.2',
						}}>
						Chat History
					</p>
				</div>
			</div>
			<IconButton
				onClick={onClose}
				icon={<FaBars />}
				variant='default'
			/>
		</div>
	);
};

export default SidebarHeader;
