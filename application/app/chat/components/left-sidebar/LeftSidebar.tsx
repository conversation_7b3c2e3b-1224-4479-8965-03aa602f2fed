/** @format */

import { ModalOverlay } from '@/app/chat/components/ui';
import { zIndexLayers } from '@/app/chat/styles';
import { LeftSidebarProps } from '@/app/chat/types';
import { colors } from '@/app/colors';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ChatHistorySection from './ChatHistorySection';
import SidebarHeader from './SidebarHeader';

const LeftSidebar: React.FC<LeftSidebarProps> = ({
	isOpen,
	onClose,
	sessionList,
	activeSessionId,
	dropdownIndex,
	onStartNewChat,
	onLoadOldMessages,
	onToggleDropdown,
	onDeleteSession,
}) => {
	console.log('LeftSidebar loaded');
	const [leftSidebarWidth, setLeftSidebarWidth] = useState(280); // Default width
	const [isResizing, setIsResizing] = useState(false);
	const leftSidebarRef = useRef<HTMLDivElement>(null);

	const MIN_LEFT_WIDTH = 280;
	const MAX_LEFT_WIDTH = 420;

	const handleLeftResize = useCallback(
		(e: MouseEvent) => {
			if (!isResizing || !leftSidebarRef.current) return;

			e.preventDefault();
			e.stopPropagation();

			const rect = leftSidebarRef.current.getBoundingClientRect();
			const newWidth = e.clientX - rect.left;
			const clampedWidth = Math.max(
				MIN_LEFT_WIDTH,
				Math.min(MAX_LEFT_WIDTH, newWidth)
			);
			setLeftSidebarWidth(clampedWidth);
		},
		[isResizing]
	);

	const handleLeftResizeEnd = useCallback(() => {
		setIsResizing(false);
		// Re-enable interactions
		document.body.classList.remove('resizing');
	}, []);

	const handleLeftResizeStart = useCallback(() => {
		setIsResizing(true);
		// Disable interactions during resize
		document.body.classList.add('resizing');
	}, []);

	useEffect(() => {
		if (isResizing) {
			document.addEventListener('mousemove', handleLeftResize, {
				passive: false,
			});
			document.addEventListener('mouseup', handleLeftResizeEnd);
			document.addEventListener('selectstart', (e) => e.preventDefault());
			return () => {
				document.removeEventListener('mousemove', handleLeftResize);
				document.removeEventListener('mouseup', handleLeftResizeEnd);
				document.removeEventListener('selectstart', (e) => e.preventDefault());
			};
		}
	}, [isResizing, handleLeftResize, handleLeftResizeEnd]);

	return (
		<>
			<ModalOverlay
				isOpen={isOpen}
				onClose={onClose}
			/>

			<div
				ref={leftSidebarRef}
				className={`transition-all duration-300 overflow-hidden flex flex-col ${
					zIndexLayers.sidebar
				} relative
          ${
						isOpen
							? 'fixed md:relative inset-0 md:inset-auto w-full h-full md:h-auto'
							: 'w-0 md:w-0 p-0'
					}
        `}
				style={{
					background: `rgba(255, 255, 255, 0.95)`,
					backdropFilter: 'blur(10px)',
					borderRight: `1px solid ${colors.ui.gray200}`,
					boxShadow: isOpen ? `2px 0 8px rgba(0, 0, 0, 0.1)` : 'none',
					width: isOpen ? `${leftSidebarWidth}px` : '0px',
					cursor: isResizing ? 'col-resize' : 'default',
					minWidth: isOpen ? '280px' : '0px',
					maxWidth: isOpen ? '420px' : '0px',
				}}>
				{isOpen && (
					<>
						<SidebarHeader onClose={onClose} />
						<ChatHistorySection
							sessionList={sessionList}
							activeSessionId={activeSessionId}
							dropdownIndex={dropdownIndex}
							onStartNewChat={onStartNewChat}
							onLoadOldMessages={onLoadOldMessages}
							onToggleDropdown={onToggleDropdown}
							onDeleteSession={onDeleteSession}
						/>

						{/* Resize Handle */}
						<div
							className='resize-handle absolute top-0 right-0 w-3 h-full cursor-col-resize transition-all duration-200 group'
							onMouseDown={handleLeftResizeStart}
							title='Resize sidebar'
							style={{
								backgroundColor: isResizing ? colors.brand.blue : 'transparent',
							}}>
							<div
								className='w-full h-full transition-all duration-200'
								style={{
									backgroundColor: isResizing
										? colors.brand.blue
										: 'transparent',
								}}
								onMouseEnter={(e) => {
									if (!isResizing) {
										e.currentTarget.style.backgroundColor = `${colors.brand.blue}40`;
									}
								}}
								onMouseLeave={(e) => {
									if (!isResizing) {
										e.currentTarget.style.backgroundColor = 'transparent';
									}
								}}
							/>
						</div>
					</>
				)}
			</div>
		</>
	);
};

export default LeftSidebar;
