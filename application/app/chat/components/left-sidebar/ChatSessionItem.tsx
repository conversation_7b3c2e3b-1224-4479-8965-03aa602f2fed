/** @format */

import { zIndexLayers } from 'app/chat/styles';
import { Session } from 'app/chat/types';
import { colors } from 'app/colors';
import React, { useEffect, useRef } from 'react';
import { FaEllipsisV, FaTrash } from 'react-icons/fa';

interface ChatSessionItemProps {
	session: Session;
	isActive: boolean;
	isDropdownOpen: boolean;
	onSessionClick: () => void;
	onToggleDropdown: () => void;
	onDeleteSession: () => void;
	onCloseDropdown: () => void;
}

const ChatSessionItem: React.FC<ChatSessionItemProps> = ({
	session,
	isActive,
	isDropdownOpen,
	onSessionClick,
	onToggleDropdown,
	onDeleteSession,
	onCloseDropdown,
}) => {
	const dropdownRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				onCloseDropdown();
			}
		};

		if (isDropdownOpen) {
			document.addEventListener('mousedown', handleClickOutside);
			return () =>
				document.removeEventListener('mousedown', handleClickOutside);
		}
	}, [isDropdownOpen, onCloseDropdown]);

	return (
		<div className='relative'>
			<div
				className='flex items-center justify-between p-3 md:p-4 rounded-xl cursor-pointer transition-all duration-200 group hover:shadow-sm'
				style={{
					backgroundColor: isActive
						? colors.brand.blue
						: 'rgba(255, 255, 255, 0.8)',
					color: isActive ? 'white' : colors.neutral.textBlack,
					border: `1px solid ${
						isActive ? colors.brand.blue : colors.ui.gray200
					}`,
					backdropFilter: 'blur(5px)',
				}}
				onClick={onSessionClick}>
				<div className='flex-1 min-w-0'>
					<div
						className='text-sm md:text-base font-medium truncate'
						style={{
							fontFamily:
								'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
						}}>
						{session.title}
					</div>
				</div>

				<button
					onClick={(e) => {
						e.stopPropagation();
						onToggleDropdown();
					}}
					className='p-1.5 md:p-2 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-black/10'
					style={{ color: isActive ? 'white' : colors.neutral.slateGray }}>
					<FaEllipsisV size={12} />
				</button>
			</div>

			{isDropdownOpen && (
				<div
					ref={dropdownRef}
					className={`absolute right-0 top-full mt-2 w-36 md:w-40 rounded-xl shadow-xl border ${zIndexLayers.sidebarDropdown}`}
					style={{
						backgroundColor: 'rgba(255, 255, 255, 0.95)',
						borderColor: colors.ui.gray200,
						backdropFilter: 'blur(10px)',
						boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',
					}}>
					<button
						onClick={(e) => {
							e.stopPropagation();
							onDeleteSession();
							onCloseDropdown();
						}}
						className='w-full px-4 py-3 text-left text-sm md:text-base transition-all duration-200 flex items-center gap-3 rounded-xl hover:shadow-sm'
						style={{ color: colors.utility.error }}
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = `${colors.utility.errorLight}`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = 'transparent';
						}}>
						<FaTrash size={12} />
						<span>Delete</span>
					</button>
				</div>
			)}
		</div>
	);
};

export default ChatSessionItem;
