/** @format */

import { IconButton } from '@/app/chat/components/ui';
import { Session } from '@/app/chat/types';
import { colors } from '@/app/colors';
import React, { useState } from 'react';
import { FaPlus, FaSearch } from 'react-icons/fa';
import ChatSessionItem from './ChatSessionItem';
import SearchOverlay from './SearchOverlay';

interface ChatHistorySectionProps {
	sessionList: Session[];
	activeSessionId: string | null;
	dropdownIndex: number | null;
	onStartNewChat: () => void;
	onLoadOldMessages: (id: string) => void;
	onToggleDropdown: (index: number) => void;
	onDeleteSession: (id: string) => void;
}

const ChatHistorySection: React.FC<ChatHistorySectionProps> = ({
	sessionList,
	activeSessionId,
	dropdownIndex,
	onStartNewChat,
	onLoadOldMessages,
	onToggleDropdown,
	onDeleteSession,
}) => {
	const [isSearchOpen, setIsSearchOpen] = useState(false);

	const handleCloseDropdown = () => {
		onToggleDropdown(-1);
	};

	return (
		<>
			<div className='w-full flex-1 p-4 md:p-6 flex flex-col'>
				<div className='flex-1 flex flex-col'>
					<div className='flex items-center justify-between mb-4 md:mb-6'>
						<h3
							className='text-sm md:text-base font-semibold uppercase tracking-wide'
							style={{
								color: colors.brand.navy,
								fontFamily:
									'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
							}}>
							Recent Chats
						</h3>
						<div className='flex items-center gap-2 md:gap-3'>
							<IconButton
								onClick={onStartNewChat}
								icon={<FaPlus className='w-3 h-3 md:w-4 md:h-4' />}
								title='New chat'
								variant='default'
							/>
							<IconButton
								onClick={() => setIsSearchOpen(true)}
								icon={<FaSearch className='w-3 h-3 md:w-4 md:h-4' />}
								title='Search chats'
								variant='default'
							/>
						</div>
					</div>

					{/* Chat List */}
					<div className='flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent'>
						{sessionList.length === 0 ? (
							<div className='text-center py-8 md:py-12 px-4'>
								<div
									className='text-4xl md:text-5xl mb-4'
									style={{ color: colors.brand.blue }}>
									💬
								</div>
								<div
									className='text-sm md:text-base font-medium mb-2'
									style={{
										color: colors.neutral.textBlack,
										fontFamily:
											'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
									}}>
									No chat history yet
								</div>
								<div
									className='text-xs md:text-sm font-normal'
									style={{
										color: colors.neutral.slateGray,
										fontFamily:
											'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
									}}>
									Start a new conversation to begin exploring!
								</div>
							</div>
						) : (
							<div className='space-y-2 md:space-y-3'>
								{sessionList
									.filter((session) => session.id && session.title)
									.map((session, index) => (
										<ChatSessionItem
											key={session.id}
											session={session}
											isActive={session.id === activeSessionId}
											isDropdownOpen={dropdownIndex === index}
											onSessionClick={() => {
												if (session.id !== activeSessionId) {
													onLoadOldMessages(session.id);
												}
											}}
											onToggleDropdown={() => onToggleDropdown(index)}
											onDeleteSession={() => onDeleteSession(session.id)}
											onCloseDropdown={handleCloseDropdown}
										/>
									))}
							</div>
						)}
					</div>
				</div>
			</div>

			<SearchOverlay
				isOpen={isSearchOpen}
				setIsOpen={setIsSearchOpen}
				sessionList={sessionList}
				loadOldMessages={onLoadOldMessages}
				currentSessionId={activeSessionId || ''}
			/>
		</>
	);
};

export default ChatHistorySection;
