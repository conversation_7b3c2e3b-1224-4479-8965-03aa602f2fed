/** @format */

import { colors } from 'app/colors';
import React from 'react';

interface ScrollToBottomButtonProps {
	isVisible: boolean;
	onClick: () => void;
}

const ScrollToBottomButton: React.FC<ScrollToBottomButtonProps> = ({
	isVisible,
	onClick,
}) => {
	if (!isVisible) return null;

	return (
		<button
			onClick={onClick}
			className='fixed bottom-24 md:bottom-28 right-4 md:right-8 text-white p-3 md:p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50 text-lg md:text-xl font-bold'
			style={{
				backgroundColor: colors.brand.blue,
				backdropFilter: 'blur(10px)',
				border: `2px solid rgba(255, 255, 255, 0.2)`,
			}}
			onMouseEnter={(e) => {
				e.currentTarget.style.backgroundColor = colors.supporting.lightBlue;
				e.currentTarget.style.transform = 'scale(1.1)';
			}}
			onMouseLeave={(e) => {
				e.currentTarget.style.backgroundColor = colors.brand.blue;
				e.currentTarget.style.transform = 'scale(1)';
			}}>
			↓
		</button>
	);
};

export default ScrollToBottomButton;
