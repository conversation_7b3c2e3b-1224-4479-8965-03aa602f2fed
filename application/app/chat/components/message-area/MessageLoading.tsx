/** @format */

import { colors } from 'app/colors';
import React from 'react';

const MessageLoading: React.FC = () => {
	return (
		<div className='flex justify-start mb-4 md:mb-6'>
			<div
				className='p-4 md:p-5 lg:p-6 rounded-2xl md:rounded-3xl max-w-xs sm:max-w-sm md:max-w-md lg:max-w-2xl shadow-sm'
				style={{
					backgroundColor: `rgba(255, 255, 255, 0.95)`,
					border: `1px solid ${colors.ui.gray200}`,
					boxShadow: '0 4px 12px 0 rgba(0, 0, 0, 0.08)',
					backdropFilter: 'blur(5px)',
				}}>
				<div className='flex items-center space-x-3'>
					{/* Animated dots with wave effect */}
					<div className='flex space-x-1'>
						{[0, 1, 2].map((i) => (
							<div
								key={i}
								className='w-2.5 h-2.5 rounded-full'
								style={{
									backgroundColor: colors.brand.blue,
									animation: `bounce 1.4s ease-in-out ${
										i * 0.16
									}s infinite both`,
								}}
							/>
						))}
					</div>

					{/* Animated text with subtle fade */}
					<span
						className='text-sm font-medium'
						style={{
							color: colors.neutral.slateGray,
							animation: 'fade 2s ease-in-out infinite',
						}}>
						Thinking...
					</span>

					{/* Animated typing indicator */}
					<div className='flex space-x-0.5'>
						{[0, 1, 2].map((i) => (
							<div
								key={i}
								className='w-1 h-4 rounded-full'
								style={{
									backgroundColor: colors.supporting.lightBlue,
									animation: `typing 1.2s ease-in-out ${i * 0.1}s infinite`,
								}}
							/>
						))}
					</div>
				</div>

				{/* Add custom keyframes via style tag */}
				<style jsx>{`
					@keyframes bounce {
						0%,
						80%,
						100% {
							transform: scale(0.8);
							opacity: 0.5;
						}
						40% {
							transform: scale(1.2);
							opacity: 1;
						}
					}

					@keyframes fade {
						0%,
						100% {
							opacity: 0.7;
						}
						50% {
							opacity: 1;
						}
					}

					@keyframes typing {
						0%,
						60%,
						100% {
							transform: scaleY(0.6);
							opacity: 0.4;
						}
						30% {
							transform: scaleY(1);
							opacity: 1;
						}
					}
				`}</style>
			</div>
		</div>
	);
};

export default MessageLoading;
