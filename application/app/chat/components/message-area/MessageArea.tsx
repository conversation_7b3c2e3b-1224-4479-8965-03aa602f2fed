/** @format */

import { MessageAreaProps } from 'app/chat/types';
import React from 'react';
import MessageList from './MessageList';
import ScrollToBottomButton from './ScrollToBottomButton';

const MessageArea: React.FC<MessageAreaProps> = ({
	messages,
	showScrollButton,
	scrollToBottom,
	messagesContainerRef,
	messagesEndRef,
	onShowMap,
	extractedLocations,
}) => {
	return (
		<div
			className='flex-1 overflow-y-auto flex justify-center bg-transparent'
			style={{
				background: 'transparent',
			}}>
			{/* Responsive Container */}
			<div className='w-full max-w-4xl px-4 md:px-6 lg:px-8'>
				<MessageList
					messages={messages}
					messagesContainerRef={messagesContainerRef}
					messagesEndRef={messagesEndRef}
					onShowMap={onShowMap}
					extractedLocations={extractedLocations}
				/>
				<ScrollToBottomButton
					isVisible={showScrollButton}
					onClick={scrollToBottom}
				/>
			</div>
		</div>
	);
};

export default MessageArea;
