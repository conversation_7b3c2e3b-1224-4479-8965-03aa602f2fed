/** @format */

import { Message, TopCandidates } from 'app/chat/types';
import React from 'react';
import MessageItem from './MessageItem';

interface MessageListProps {
	messages: Message[];
	messagesContainerRef: React.RefObject<HTMLDivElement>;
	messagesEndRef: React.RefObject<HTMLDivElement>;
	onShowMap: () => void;
	extractedLocations?: TopCandidates | null;
}

const MessageList: React.FC<MessageListProps> = ({
	messages,
	messagesContainerRef,
	messagesEndRef,
	onShowMap,
	extractedLocations,
}) => {
	return (
		<div
			className='flex-1 overflow-y-auto flex flex-col gap-4 md:gap-6 py-6 md:py-8 lg:py-12 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent'
			ref={messagesContainerRef}>
			{messages.map((message, index) => (
				<MessageItem
					key={index}
					message={message}
					onShowMap={onShowMap}
					extractedLocations={extractedLocations}
				/>
			))}
			<div ref={messagesEndRef} />
		</div>
	);
};

export default MessageList;
