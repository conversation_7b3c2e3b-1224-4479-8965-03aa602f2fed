/** @format */

'use client';

import {
	InputArea,
	LeftSidebar,
	MessageArea,
	RightSidebar,
	TopBar,
} from 'app/chat/components';
import { useStreamingChat } from 'app/chat/hooks/useStreamingChat';
import { ChatContextValue } from 'app/chat/types';
import {
	useChatMessages,
	useChatState,
	useChatUI,
} from 'app/chat/useChatHooks';
import {
	LocationSetup,
	useLocationManager,
	useLocationSetup,
} from 'app/shared/locationManager';
import { useSession } from 'next-auth/react';
import React, { createContext, useContext, useMemo } from 'react';

// ===== CONTEXT =====

const ChatContext = createContext<ChatContextValue | null>(null);

export function useChatContext() {
	const context = useContext(ChatContext);
	if (!context) {
		throw new Error('useChatContext must be used within a ChatPageProvider');
	}
	return context;
}

// ===== MAIN CHAT LAYOUT COMPONENT =====

const ChatLayout: React.FC = () => {
	const {
		chatState,
		uiState,
		locationState,
		actions,
		inputRef,
		messagesEndRef,
		messagesContainerRef,
		setUIState,
		showLocationSetup,
		handleLocationSetupComplete,
	} = useChatContext();

	// Memoize extractedLocations to avoid unnecessary re-renders
	const memoizedExtractedLocations = useMemo(
		() => chatState.allExtractedLocations || [],
		[chatState.allExtractedLocations]
	);

	// Debug: Log sidebar open states on each render
	console.log('ChatLayout render:', {
		isLeftOpen: uiState.isLeftOpen,
		isRightOpen: uiState.isRightOpen,
	});

	return (
		<>
			{/* Location Setup Modal */}
			<LocationSetup
				isOpen={showLocationSetup}
				onComplete={handleLocationSetupComplete}
				pageContext='chat'
				isModal={true}
				title='Location Setup Required'
				subtitle='To provide better location-based responses, we need to know your location or you can set it manually.'
			/>

			{/* Main Chat Interface - Responsive Layout */}
			<div className='h-screen w-full overflow-hidden flex flex-row relative bg-transparent'>
				{/* Left Sidebar */}
				<LeftSidebar
					isOpen={uiState.isLeftOpen}
					onClose={() => setUIState((prev) => ({ ...prev, isLeftOpen: false }))}
					sessionList={chatState.sessionList}
					activeSessionId={chatState.sessionId}
					dropdownIndex={uiState.dropdownIndex}
					onStartNewChat={actions.startNewChat}
					onLoadOldMessages={actions.loadOldMessages}
					onToggleDropdown={(index) =>
						setUIState((prev) => ({ ...prev, dropdownIndex: index }))
					}
					onDeleteSession={actions.handleDeleteSession}
				/>

				{/* Main Content Column - Responsive */}
				<div className='flex-1 flex flex-col min-w-0 w-full px-4 md:px-6 lg:px-12'>
					{/* Top Bar */}
					<TopBar
						isLeftOpen={uiState.isLeftOpen}
						isRightOpen={uiState.isRightOpen}
						setIsLeftOpen={(open) => {
							console.log('setIsLeftOpen called with:', open);
							setUIState((prev) => ({ ...prev, isLeftOpen: open }));
						}}
						setIsRightOpen={(open) => {
							console.log('setIsRightOpen called with:', open);
							setUIState((prev) => ({ ...prev, isRightOpen: open }));
						}}
						startNewChat={actions.startNewChat}
						userLocation={locationState.userLocation}
						locationError={locationState.locationError}
						locationLoading={locationState.locationLoading}
						requestAutoLocation={locationState.requestAutoLocation}
					/>

					{/* Message Area */}
					<MessageArea
						messages={chatState.messages}
						showScrollButton={uiState.showScrollButton}
						scrollToBottom={actions.scrollToBottom}
						messagesContainerRef={messagesContainerRef}
						messagesEndRef={messagesEndRef}
						onShowMap={() =>
							setUIState((prev) => ({ ...prev, isRightOpen: true }))
						}
						extractedLocations={chatState.topCandidates}
					/>

					{/* Input Area */}
					<InputArea
						newMessage={chatState.newMessage}
						handleInputChange={actions.handleInputChange}
						handleKeyDown={actions.handleKeyDown}
						inputRef={inputRef}
						isSending={chatState.isSending}
						hasMessages={chatState.messages.length > 0}
					/>
				</div>

				{/* Right Sidebar */}
				<RightSidebar
					isOpen={uiState.isRightOpen}
					onClose={() =>
						setUIState((prev) => ({ ...prev, isRightOpen: false }))
					}
					userLocation={locationState.userLocation}
					extractedLocations={memoizedExtractedLocations}
				/>
			</div>
		</>
	);
};

// ===== MAIN PROVIDER COMPONENT =====

interface ChatPageProviderProps {
	children: React.ReactNode;
}

export function ChatPageProvider({ children }: ChatPageProviderProps) {
	// Core state hooks (single chatState object)
	const {
		chatState,
		setChatState,
		setMessages,
		setNewMessage,
		setSessionId,
		setSessionList,
		setTopCandidates,
		setAllExtractedLocations,
		addExtractedLocations,
		setIsSending,
	} = useChatState();

	const {
		uiState,
		setUIState,
		setIsLeftOpen,
		setIsRightOpen,
		setDropdownIndex,
		setShowScrollButton,
		setIsGlobeTransitioning,
	} = useChatUI();

	// Location management
	const locationManager = useLocationManager();

	// Map useLocationManager return to LocationState interface
	const locationState = {
		userLocation: locationManager.location
			? {
					lat: locationManager.location.latitude,
					lng: locationManager.location.longitude,
			  }
			: null,
		locationError: locationManager.error,
		locationLoading: locationManager.isLoading,
		requestAutoLocation: locationManager.requestAutoLocation,
		markSetupComplete: locationManager.markSetupComplete,
		needsLocationSetup: locationManager.needsLocationSetup,
		isInitialized: locationManager.isInitialized,
	};

	// Location setup modal
	const { showLocationSetup, handleLocationSetupComplete } = useLocationSetup();

	// Session management
	const handleDeleteSession = async (sessionIdToDelete: string) => {
		if (!session?.user?.id) return;

		try {
			const response = await fetch('/api/chat/session', {
				method: 'DELETE',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					userId: session.user.id,
					sessionId: sessionIdToDelete,
				}),
			});

			if (response.ok) {
				setSessionList((prev) =>
					prev.filter((session) => session.id !== sessionIdToDelete)
				);

				if (chatState.sessionId === sessionIdToDelete) {
					setMessages([]);
					setSessionId(null);
					setTopCandidates(null);
				}
			}
		} catch (error) {
			console.error('Error deleting session:', error);
		}
	};

	// Auto-scroll, input focus, scroll-to-bottom button
	const {
		inputRef,
		messagesEndRef,
		messagesContainerRef,
		scrollToBottom,
		handleInputChange: autoResizeInputChange,
	} = useChatMessages({
		messages: chatState.messages,
		isSending: chatState.isSending,
		setShowScrollButton,
	});

	// Input change handler: only update newMessage
	const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
		setNewMessage(e.target.value);
		autoResizeInputChange();
	};

	const startNewChat = async () => {
		setMessages([]);
		setNewMessage('');
		setSessionId(null);
	};

	// Streaming chat functionality
	const { sendStreamingMessage } = useStreamingChat();

	// Enhanced UI state with computed properties
	const enhancedUIState = useMemo(
		() => ({
			...uiState,
			// Add any computed properties here if needed
		}),
		[uiState]
	);

	// Session data for Next.js
	const { data: session } = useSession();

	// Main message sending handler
	const handleSendMessage = async () => {
		if (!chatState.newMessage.trim() || chatState.isSending) return;

		const messageText = chatState.newMessage.trim();
		setNewMessage('');
		setIsSending(true);

		try {
			await sendStreamingMessage(
				{
					message: messageText,
					userId: session?.user?.id || 'anonymous',
					sessionId: chatState.sessionId || '',
					userLat: locationState.userLocation?.lat || null,
					userLng: locationState.userLocation?.lng || null,
				},
				{
					onMessageUpdate: (content: string) => {
						// Update the current assistant message
						setMessages((prev) => {
							const newMessages = [...prev];
							const lastMessage = newMessages[newMessages.length - 1];

							if (lastMessage && !lastMessage.isUser) {
								// Update existing assistant message
								lastMessage.text = content;
							} else {
								// Add new assistant message
								newMessages.push({
									text: content,
									isUser: false,
								});
							}
							return newMessages;
						});
					},
					onTopCandidatesUpdate: (candidates: any[]) => {
						const topCandidates = { locations: candidates };
						setTopCandidates(topCandidates);
						addExtractedLocations(candidates, chatState.messages.length);
					},
					onError: (error: string) => {
						console.error('Streaming error:', error);
						// Add error message
						setMessages((prev) => [
							...prev,
							{
								text: `Error: ${error}`,
								isUser: false,
							},
						]);
					},
					onComplete: () => {
						console.log('Streaming completed');
					},
					onProcessingStart: () => {
						// Add user message and placeholder assistant message
						setMessages((prev) => [
							...prev,
							{
								text: messageText,
								isUser: true,
							},
							{
								text: 'Thinking...',
								isUser: false,
								isLoading: true,
							},
						]);
					},
				}
			);
		} catch (error) {
			console.error('Failed to send message:', error);
		} finally {
			setIsSending(false);
		}
	};

	// Key down handler for input
	const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
		if (e.key === 'Enter' && !e.shiftKey) {
			e.preventDefault();
			handleSendMessage();
		}
	};

	// Load old messages handler
	const loadOldMessages = async (sessionId: string) => {
		// Implementation for loading old messages
		console.log('Loading old messages for session:', sessionId);
	};

	// Actions object
	const actions = {
		sendMessage: handleSendMessage,
		handleInputChange,
		handleKeyDown,
		startNewChat,
		scrollToBottom,
		loadOldMessages,
		handleDeleteSession,
		toggleDropdown: (index: number) => {
			setUIState((prev) => ({ ...prev, dropdownIndex: index }));
		},
		handleGlobeTransition: () => {
			setIsGlobeTransitioning(true);
			setTimeout(() => setIsGlobeTransitioning(false), 1000);
		},
	};

	// Context value
	const contextValue: ChatContextValue = {
		chatState,
		uiState: enhancedUIState,
		locationState,
		actions,
		inputRef,
		messagesEndRef,
		messagesContainerRef,
		setChatState,
		setUIState,
		showLocationSetup,
		handleLocationSetupComplete,
	};

	return (
		<ChatContext.Provider value={contextValue}>{children}</ChatContext.Provider>
	);
}

// ===== MAIN EXPORT COMPONENT =====

const ChatPageContent: React.FC = () => {
	return (
		<ChatPageProvider>
			<ChatLayout />
		</ChatPageProvider>
	);
};

export default ChatPageContent;
