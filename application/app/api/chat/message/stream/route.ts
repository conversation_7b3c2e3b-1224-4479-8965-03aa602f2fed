/** @format */

import { config } from '@/lib/config';
import { handleApiError, ValidationError } from '@/lib/errors';
import { logger } from '@/lib/logger';
import { sanitizeObject, validateMessage } from '@/lib/security-headers';
import { resolveToUsername } from '@/lib/username-helpers';
import { NextRequest } from 'next/server';
import { v4 as uuidv4 } from 'uuid';

/**
 * Streaming chat message handler
 * Implements Server-Sent Events (SSE) for real-time message streaming
 */
async function handleStreamingChatMessage(request: NextRequest) {
	const requestId = uuidv4();

	try {
		const body = await request.json();

		// Sanitize all input
		const sanitizedBody = sanitizeObject(body);
		const { sessionId, message, userId, userLat, userLng } = sanitizedBody;

		// Enhanced validation
		if (!sessionId || !message || !userId) {
			throw new ValidationError(
				'Session ID, message, and user ID are required'
			);
		}

		// Enhanced message validation
		const messageValidation = validateMessage(message);
		if (!messageValidation.isValid) {
			throw new ValidationError(messageValidation.error!);
		}

		// Resolve userId to username for the LLM request
		const resolvedUsername = await resolveToUsername(userId);
		if (!resolvedUsername) {
			throw new ValidationError('User not found');
		}

		logger.info('Streaming chat message request', {
			sessionId,
			userId: resolvedUsername,
			messageLength: message.length,
			hasLocation: !!(userLat && userLng),
			requestId,
		});

		// Default search parameters
		const searchRadius = 1000; // 1km default
		const numCandidates = 5; // Default number of POI candidates

		// Create streaming response
		const encoder = new TextEncoder();

		const stream = new ReadableStream({
			async start(controller) {
				try {
					// Send initial connection confirmation
					const initialData = JSON.stringify({
						type: 'connection',
						status: 'connected',
						requestId,
						timestamp: new Date().toISOString(),
					});
					controller.enqueue(encoder.encode(`data: ${initialData}\n\n`));

					// Send user message confirmation
					const userMessageData = JSON.stringify({
						type: 'user_message',
						message: message,
						timestamp: new Date().toISOString(),
					});
					controller.enqueue(encoder.encode(`data: ${userMessageData}\n\n`));

					// Send processing start indicator
					const processingData = JSON.stringify({
						type: 'processing_start',
						status: 'Processing your request...',
						timestamp: new Date().toISOString(),
					});
					controller.enqueue(encoder.encode(`data: ${processingData}\n\n`));

					// Make request to LLM engine streaming endpoint
					const llmResponse = await fetch(
						`${config.llmEngine.url}/message/stream`,
						{
							method: 'POST',
							headers: {
								'Content-Type': 'application/json',
								...(config.llmEngine.apiKey && {
									Authorization: `Bearer ${config.llmEngine.apiKey}`,
								}),
							},
							body: JSON.stringify({
								user_id: resolvedUsername,
								session_id: sessionId,
								message: message,
								latitude: userLat || null,
								longitude: userLng || null,
								search_radius: searchRadius,
								num_candidates: numCandidates,
							}),
						}
					);

					if (!llmResponse.ok) {
						throw new Error(
							`LLM Engine error: ${llmResponse.status} ${llmResponse.statusText}`
						);
					}

					// Process the streaming response from LLM engine
					const reader = llmResponse.body?.getReader();
					if (!reader) {
						throw new Error('No response body from LLM engine');
					}

					const decoder = new TextDecoder();
					let buffer = '';

					try {
						while (true) {
							const { done, value } = await reader.read();

							if (done) {
								break;
							}

							buffer += decoder.decode(value, { stream: true });

							// Process complete lines from buffer
							const lines = buffer.split('\n');
							buffer = lines.pop() || ''; // Keep incomplete line in buffer

							for (const line of lines) {
								if (line.trim() === '') continue;

								if (line.startsWith('data: ')) {
									const data = line.slice(6);

									if (data === '[DONE]') {
										// Send completion signal
										const completionData = JSON.stringify({
											type: 'completion',
											status: 'completed',
											timestamp: new Date().toISOString(),
										});
										controller.enqueue(
											encoder.encode(`data: ${completionData}\n\n`)
										);
										break;
									}

									try {
										const parsedData = JSON.parse(data);

										// Forward the parsed data to client
										const forwardData = JSON.stringify({
											type: 'llm_response',
											data: parsedData,
											timestamp: new Date().toISOString(),
										});
										controller.enqueue(
											encoder.encode(`data: ${forwardData}\n\n`)
										);
									} catch (parseError) {
										console.error('Error parsing LLM response:', parseError);
										// Send error but continue processing
										const errorData = JSON.stringify({
											type: 'parse_error',
											error: 'Failed to parse response chunk',
											timestamp: new Date().toISOString(),
										});
										controller.enqueue(
											encoder.encode(`data: ${errorData}\n\n`)
										);
									}
								}
							}
						}
					} finally {
						reader.releaseLock();
					}
				} catch (error) {
					console.error('Streaming error:', error);

					// Send error to client
					const errorData = JSON.stringify({
						type: 'error',
						error:
							error instanceof Error
								? error.message
								: 'Unknown streaming error',
						timestamp: new Date().toISOString(),
					});
					controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
				} finally {
					// Always close the stream
					controller.close();
				}
			},
		});

		return new Response(stream, {
			headers: {
				'Content-Type': 'text/event-stream',
				'Cache-Control': 'no-cache',
				Connection: 'keep-alive',
				'Access-Control-Allow-Origin': '*',
				'Access-Control-Allow-Headers': '*',
				'Access-Control-Allow-Methods': '*',
			},
		});
	} catch (error) {
		logger.error('Streaming chat message processing failed', {
			error: error instanceof Error ? error.message : 'Unknown error',
			requestId,
		});

		return handleApiError(error, requestId);
	}
}

export async function POST(request: NextRequest) {
	try {
		return await handleStreamingChatMessage(request);
	} catch (error) {
		return handleApiError(error, uuidv4());
	}
}
