{"extends": "./tsconfig.json", "compilerOptions": {"noEmit": false, "jsx": "react-jsx", "skipLibCheck": true, "outDir": "./dist", "declaration": false, "sourceMap": true, "removeComments": false, "target": "es2020", "module": "esnext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true}, "include": ["app/**/*", "lib/**/*", "middleware.ts", "next-env.d.ts"], "exclude": ["node_modules", ".next", "dist", "out", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"]}