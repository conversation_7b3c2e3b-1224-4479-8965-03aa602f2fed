{"name": "wiz<PERSON>", "version": "0.1.0", "private": true, "dependencies": {"@auth/pg-adapter": "^1.9.1", "@types/bcryptjs": "^2.4.6", "@types/leaflet": "^1.9.18", "@types/maplibre-gl": "^1.13.2", "@types/node": "^16.18.126", "@types/pg": "^8.10.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@types/uuid": "^9.0.8", "bcryptjs": "^2.4.3", "jest": "^30.0.4", "leaflet": "^1.9.4", "maplibre-gl": "^5.6.0", "next": "^15.1.0", "next-auth": "^4.24.11", "pg": "^8.16.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-globe.gl": "^2.33.2", "react-icons": "^4.12.0", "react-leaflet": "^4.2.1", "react-map-gl": "^8.0.4", "sharp": "^0.34.2", "three": "^0.176.0", "uuid": "^9.0.1", "webpack": "^5.100.1"}, "scripts": {"dev": "next dev", "build": "next build", "build:next": "next build", "build:webpack": "webpack --config config/webpack/webpack.dev.js", "build:webpack-prod": "webpack --config config/webpack/webpack.prod.js", "build:all-errors": "webpack --config config/webpack/webpack.dev.js --stats=errors-warnings", "check-types": "tsc --noEmit --pretty", "check-types:dev": "tsc --noEmit --pretty --project config/development/tsconfig.dev.json", "check-types:prod": "tsc --noEmit --pretty --project config/production/tsconfig.prod.json", "check-all": "npm run check-types && npm run build:all-errors", "start": "next start", "start:prod": "NODE_ENV=production next start -c config/next.config.js", "lint": "eslint \"app/**/*.{ts,tsx}\" \"lib/**/*.{ts,tsx}\" \"middleware.ts\" -c config/eslint.config.js", "lint:fix": "eslint \"app/**/*.{ts,tsx}\" \"lib/**/*.{ts,tsx}\" \"middleware.ts\" -c config/eslint.config.js --fix", "lint:webpack": "npm run build:all-errors", "clean": "rm -rf .next dist out node_modules/.cache", "clean:all": "rm -rf .next dist out node_modules", "clean:configs": "rm -rf .next dist out", "test:credits": "node scripts/test-credits.js", "test": "jest", "test:watch": "jest --watch", "analyze": "npm run build:next && npx @next/bundle-analyzer", "setup:dev": "npm run clean && npm install", "setup:prod": "npm run clean && npm ci --only=production"}, "devDependencies": {"@eslint/js": "^9.30.1", "@next/bundle-analyzer": "^15.1.0", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "autoprefixer": "^10.4.21", "css-loader": "^7.1.2", "eslint": "^9.30.1", "postcss": "^8.5.3", "postcss-loader": "^8.1.1", "style-loader": "^4.0.0", "tailwindcss": "^3.4.17", "ts-loader": "^9.5.1", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0", "webpack-cli": "^6.0.1"}}