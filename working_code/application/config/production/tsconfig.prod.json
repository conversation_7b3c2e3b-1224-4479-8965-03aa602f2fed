{"extends": "../../tsconfig.json", "compilerOptions": {"sourceMap": false, "removeComments": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "declaration": false, "declarationMap": false}, "include": ["../../app/**/*", "../../lib/**/*", "../../middleware.ts", "../../next-env.d.ts"], "exclude": ["../../**/*.test.ts", "../../**/*.test.tsx", "../../**/*.spec.ts", "../../**/*.spec.tsx", "../../tools/**/*", "../../scripts/**/*"]}