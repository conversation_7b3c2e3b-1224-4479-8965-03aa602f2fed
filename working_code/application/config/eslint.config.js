/** @format */

// eslint.config.js
import tseslint from 'typescript-eslint';

export default [
	// Global ignores - these apply to all configurations
	{
		ignores: [
			'.next/**',
			'node_modules/**',
			'dist/**',
			'build/**',
			'out/**',
			'*.config.js',
			'*.config.ts',
			'**/*.d.ts',
			'coverage/**',
			'.turbo/**',
			'**/*.test.tsx',
			'**/*.test.ts',
			'**/*.spec.tsx',
			'**/*.spec.ts',
		],
	},
	// TypeScript configuration
	...tseslint.configs.recommended,
	{
		files: ['**/*.{ts,tsx}'],
		rules: {
			// Add any custom rules here
		},
	},
];
