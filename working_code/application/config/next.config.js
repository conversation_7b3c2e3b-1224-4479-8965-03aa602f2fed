/** @format */

const isDev = process.env.NODE_ENV === 'development';

/** @type {import('next').NextConfig} */
const nextConfig = {
	images: {
		domains: [
			'lh3.googleusercontent.com',
			'avatars.githubusercontent.com',
			'ui-avatars.com',
		],
	},
	// Enable strict mode for better development
	reactStrictMode: true,
	// Production optimizations
	compress: !isDev,
	// Configure webpack to handle server-side modules
	webpack: (config, { isServer }) => {
		if (!isServer) {
			// Don't resolve 'fs', 'net', 'tls' modules on the client-side
			config.resolve.fallback = {
				...config.resolve.fallback,
				fs: false,
				net: false,
				tls: false,
				crypto: false,
			};
		}

		// Development-specific webpack config
		if (isDev) {
			config.devtool = 'eval-source-map';
		}

		return config;
	},
	// Environment-specific settings
	...(isDev
		? {
				// Development settings
				typescript: {
					ignoreBuildErrors: false,
				},
				eslint: {
					ignoreDuringBuilds: false,
				},
		  }
		: {
				// Production settings
				typescript: {
					ignoreBuildErrors: false,
				},
				eslint: {
					ignoreDuringBuilds: false,
				},
				output: 'standalone',
		  }),
};

module.exports = nextConfig;
