RULES:
__________
User prefers detailed code analysis with specific file and line number references.

Track progress in tree.txt.

Always test the system after each refactoring phase.

Always check TODO.txt before declaring any task complete or proceeding further.

After fixing issues, sleep 30 seconds in terminal, then check TODO.txt for new errors. Follow this pattern consistently.

Regularly monitor TODO.txt — the user may add notes or instructions at any time.

If TODO.txt is empty, continue as usual.

If it contains content, read carefully and handle issues immediately. Prioritize the top item, then continue.

After reviewing an item, mark it with ✅ to confirm it's been acknowledged.

Items don’t need to be done in order, but every entry must be seen and considered.
__________
PROBLEMS:
__________
