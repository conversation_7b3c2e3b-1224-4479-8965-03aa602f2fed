# AWS ECS Environment Configuration Guide

## Overview

Your application uses a centralized configuration system (`lib/config.ts`) that reads environment variables. This is perfect for AWS ECS deployment where secrets are managed through the task definition.

## Configuration Architecture

### ✅ Centralized Config System
- **File**: `application/lib/config.ts`
- **Benefits**: Type safety, validation, defaults, perfect for containerized deployments
- **Usage**: Import `config` object throughout your application

### Environment Variable Flow
```
AWS ECS Task Definition → Container Environment → config.ts → Application
```

## AWS ECS Task Definition Example

### Environment Variables Section
```json
{
  "environment": [
    {
      "name": "NODE_ENV",
      "value": "production"
    },
    {
      "name": "PORT",
      "value": "3000"
    },
    {
      "name": "NEXTAUTH_URL",
      "value": "https://yourdomain.com"
    }
  ],
  "secrets": [
    {
      "name": "NEXTAUTH_SECRET",
      "valueFrom": "arn:aws:secretsmanager:region:account:secret:wizlop/nextauth-secret"
    },
    {
      "name": "DB_PASSWORD",
      "valueFrom": "arn:aws:secretsmanager:region:account:secret:wizlop/db-password"
    },
    {
      "name": "GOOGLE_CLIENT_SECRET",
      "valueFrom": "arn:aws:secretsmanager:region:account:secret:wizlop/google-oauth"
    }
  ]
}
```

## AWS Secrets Manager Setup

### 1. Create Secrets
```bash
# NextAuth Secret
aws secretsmanager create-secret \
  --name "wizlop/nextauth-secret" \
  --description "NextAuth secret for Wizlop" \
  --secret-string "$(node -e "console.log(require('crypto').randomBytes(32).toString('hex'))")"

# Database Password
aws secretsmanager create-secret \
  --name "wizlop/db-password" \
  --description "Database password for Wizlop" \
  --secret-string "your-secure-db-password"

# Google OAuth Secret
aws secretsmanager create-secret \
  --name "wizlop/google-oauth" \
  --description "Google OAuth secret for Wizlop" \
  --secret-string "your-google-client-secret"
```

### 2. IAM Role for ECS Task
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "secretsmanager:GetSecretValue"
      ],
      "Resource": [
        "arn:aws:secretsmanager:region:account:secret:wizlop/*"
      ]
    }
  ]
}
```

## Complete ECS Task Definition

### Non-Sensitive Environment Variables
Set these directly in the task definition:
- `NODE_ENV=production`
- `PORT=3000`
- `NEXTAUTH_URL=https://yourdomain.com`
- `DB_HOST=your-rds-endpoint.region.rds.amazonaws.com`
- `DB_PORT=5432`
- `DB_NAME=wizlop_prod`
- `DB_USER=wizlop_prod_user`
- `DATABASE_SSL=true`
- `LLM_ENGINE_URL=https://your-llm-engine.yourdomain.com`
- `ENABLE_REGISTRATION=true`
- `LOG_LEVEL=warn`

### Sensitive Variables (Use Secrets Manager)
- `NEXTAUTH_SECRET`
- `DB_PASSWORD`
- `GOOGLE_CLIENT_SECRET`
- `LLM_ENGINE_API_KEY`

## Deployment Steps

### 1. Build and Push Docker Image
```bash
# Build
docker build -t wizlop:latest .

# Tag for ECR
docker tag wizlop:latest your-account.dkr.ecr.region.amazonaws.com/wizlop:latest

# Push
docker push your-account.dkr.ecr.region.amazonaws.com/wizlop:latest
```

### 2. Update Task Definition
```bash
aws ecs register-task-definition --cli-input-json file://task-definition.json
```

### 3. Update Service
```bash
aws ecs update-service \
  --cluster your-cluster \
  --service wizlop-service \
  --task-definition wizlop:REVISION
```

## Configuration Validation

Your `config.ts` includes validation that will catch missing production variables:

```typescript
// This will throw errors if required production variables are missing
export function validateConfig() {
  if (config.app.environment === 'production') {
    if (!config.app.secret || config.app.secret === 'development-secret-key') {
      errors.push('NEXTAUTH_SECRET must be set in production')
    }
    // ... other validations
  }
}
```

## Benefits of This Approach

1. **Security**: Secrets stored in AWS Secrets Manager, not in code
2. **Flexibility**: Easy to change environment variables without code changes
3. **Type Safety**: Centralized config with TypeScript validation
4. **Defaults**: Fallback values for development
5. **Validation**: Runtime checks for required production variables

## Domain Setup Checklist

When you get your domain:

1. **Update NEXTAUTH_URL** in ECS task definition
2. **Update Google OAuth** authorized origins and redirect URIs
3. **Update CORS_ORIGINS** to include your domain
4. **Set up SSL certificate** (AWS Certificate Manager)
5. **Configure ALB** to use HTTPS
6. **Update DNS** to point to your load balancer
