# 🚀 Wizlop Setup Guide

Complete development setup for the Wizlop global exploration chat application.

## 📋 Prerequisites

- **Node.js** 18+ 
- **PostgreSQL** 12+
- **Git**

## ⚡ Quick Setup (15 minutes)

### 1. Environment Setup
```bash
# Clone and navigate
git clone <your-repo>
cd wizlop/application

# Install dependencies
npm install

# Create environment file
cp .env.local.example .env.local  # or create manually
```

### 2. Configure Environment
Edit `.env.local` with your settings:
```env
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=wizlop_db
DB_USER=wizlop_user
DB_PASSWORD=your_password

# NextAuth (required)
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# LLM Engine
LLM_ENGINE_URL=http://localhost:8000
```

### 3. Database Setup
```bash
# Create database and user
createdb wizlop_db
createuser wizlop_user

# Run migrations (if available)
npm run db:migrate
```

### 4. Start Development
```bash
# Start the application
npm run dev

# Open browser
open http://localhost:3000
```

## 🔧 Common Issues

### Database Connection Fails
```bash
# Check PostgreSQL status
brew services list | grep postgresql  # macOS
sudo service postgresql status        # Ubuntu

# Start PostgreSQL
brew services start postgresql        # macOS
sudo service postgresql start         # Ubuntu
```

### Application Won't Start
```bash
# Check if port 3000 is in use
lsof -i :3000

# Clear Next.js cache
rm -rf .next
npm run dev
```

### Authentication Issues
- Verify `NEXTAUTH_SECRET` is set in `.env.local`
- Check database connection
- Ensure database tables exist

## ✅ Verification Checklist

- [ ] Application starts without errors (`npm run dev`)
- [ ] Database connection works
- [ ] Can access `http://localhost:3000`
- [ ] No critical console errors
- [ ] Environment variables are set

## 📚 Next Steps

- **Environment Details**: See `ENVIRONMENT_SETUP.md`
- **Database Reference**: See `CURRENT_DATABASE_DOCUMENTATION.md`
- **Troubleshooting**: See `TROUBLESHOOTING.md`
