<!-- @format -->

# Build Systems Documentation

This document explains the dual build system setup for development and production environments.

## 🏗️ Build Systems Overview

### 1. **Next.js Build System** (Primary - Production)

- **Purpose**: Production-ready React application
- **Command**: `npm run build`
- **Output**: `.next/` directory
- **Use Case**: Production deployment, optimized builds

### 2. **Webpack Build System** (Secondary - Development/Debug)

- **Purpose**: Development debugging and error analysis
- **Command**: `npm run build:webpack`
- **Output**: `dist/` directory
- **Use Case**: TypeScript error debugging, development analysis

## 📁 Project Structure

```
application/
├── config/                    # All configuration files
│   ├── development/           # Development-specific configs
│   │   └── tsconfig.dev.json
│   ├── production/            # Production-specific configs
│   │   └── tsconfig.prod.json
│   ├── webpack/               # Webpack configurations
│   │   ├── webpack.dev.js
│   │   └── webpack.prod.js
│   ├── eslint.config.js
│   ├── next.config.js
│   ├── postcss.config.js
│   └── tailwind.config.js
├── scripts/                   # Build scripts
│   ├── build-dev.sh
│   └── build-prod.sh
├── tools/                     # Development tools
│   ├── development/
│   │   └── debug-build.js
│   └── production/
├── .env-templates/            # Environment templates
│   ├── .env.development.template
│   └── .env.production.template
├── app/                       # Next.js app directory
├── lib/                       # Shared libraries
├── public/                    # Static assets
└── docs/                      # Documentation
```

## 🚀 Quick Start Commands

### Development

```bash
# Start development server
npm run dev

# Debug build issues
npm run build:all-errors

# Run development build script
./scripts/build-dev.sh

# Debug tool
node tools/development/debug-build.js
```

### Production

```bash
# Production build
npm run build

# Production build script
./scripts/build-prod.sh

# Start production server
npm run start:prod
```

## 🔧 Available Scripts

### Core Build Scripts

- `npm run build` - Next.js production build
- `npm run build:webpack` - Webpack development build
- `npm run build:webpack-prod` - Webpack production build
- `npm run build:all-errors` - Webpack build with detailed error reporting

### Type Checking

- `npm run check-types` - Basic TypeScript checking
- `npm run check-types:dev` - Development TypeScript checking
- `npm run check-types:prod` - Production TypeScript checking (strict)

### Linting

- `npm run lint` - ESLint code quality check
- `npm run lint:fix` - Auto-fix ESLint issues

### Cleanup

- `npm run clean` - Clean build artifacts
- `npm run clean:all` - Clean everything including node_modules
- `npm run clean:configs` - Clean only build outputs

### Environment Setup

- `npm run setup:dev` - Setup development environment
- `npm run setup:prod` - Setup production environment

## 🔍 Debugging Build Issues

### 1. Use the Debug Tool

```bash
node tools/development/debug-build.js
```

### 2. Check TypeScript Errors

```bash
npm run check-types:dev
```

### 3. Check ESLint Issues

```bash
npm run lint
```

### 4. Detailed Webpack Errors

```bash
npm run build:all-errors
```

## 🌍 Environment Configuration

### Development Setup

1. Copy environment template:
   ```bash
   cp .env-templates/.env.development.template .env.local
   ```
2. Fill in your development values
3. Run: `npm run setup:dev`

### Production Setup

1. Set environment variables from `.env-templates/.env.production.template`
2. Run: `npm run setup:prod`
3. Deploy with: `npm run build`

## 📊 Build Outputs

### Next.js Build (`.next/`)

- Optimized for production
- Server-side rendering ready
- Automatic code splitting
- Image optimization

### Webpack Build (`dist/`)

- Development debugging
- Source maps for debugging
- Detailed error reporting
- TypeScript compilation analysis

## 🔧 Configuration Files

### TypeScript Configurations

- `tsconfig.json` - Base configuration
- `config/development/tsconfig.dev.json` - Development settings
- `config/production/tsconfig.prod.json` - Production settings (strict)

### Webpack Configurations

- `config/webpack/webpack.dev.js` - Development webpack
- `config/webpack/webpack.prod.js` - Production webpack

### Next.js Configuration

- `config/next.config.js` - Environment-aware Next.js config

## 🚨 Troubleshooting

### Common Issues

1. **TypeScript Errors**

   - Run: `npm run check-types:dev`
   - Check: Type imports and exports
   - Fix: Missing type definitions

2. **Build Failures**

   - Run: `npm run build:all-errors`
   - Check: Webpack error details
   - Fix: Import paths and dependencies

3. **ESLint Errors**
   - Run: `npm run lint:fix`
   - Check: Code quality issues
   - Fix: Follow ESLint suggestions

### Getting Help

1. Run the debug tool: `node tools/development/debug-build.js`
2. Check this documentation
3. Review error logs in detail
4. Ensure environment variables are set correctly

## 📈 Performance Tips

### Development

- Use `npm run dev` for hot reloading
- Use `npm run build:all-errors` for debugging
- Keep development dependencies separate

### Production

- Use `npm run build` for optimized builds
- Enable bundle analysis: `ANALYZE_BUNDLE=true npm run build`
- Use production environment variables
- Consider CDN for static assets

## 🧹 Cleanup Commands

### Clean Build Artifacts

```bash
npm run clean          # Clean .next, dist, out, cache
npm run clean:all       # Clean everything including node_modules
npm run clean:configs   # Clean only build outputs
```

### Environment Setup

```bash
npm run setup:dev       # Setup development environment
npm run setup:prod      # Setup production environment
```

## 📋 Project Structure Summary

The application is now organized with:

- **Centralized Configuration**: All configs in `config/` directory
- **Environment Separation**: Dev/prod specific configurations
- **Build Scripts**: Automated build processes in `scripts/`
- **Development Tools**: Debug utilities in `tools/`
- **Documentation**: Comprehensive guides in `docs/`
- **Clean Root**: Minimal files in application root

This structure provides:

- ✅ **Production Ready**: Optimized builds and configurations
- ✅ **Development Friendly**: Debug tools and detailed error reporting
- ✅ **Maintainable**: Organized structure with clear separation
- ✅ **Scalable**: Easy to add new configurations and tools
