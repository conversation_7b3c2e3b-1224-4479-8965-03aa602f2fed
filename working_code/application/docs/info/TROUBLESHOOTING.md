<!-- @format -->

# 🔧 Troubleshooting Guide

Quick fixes for common Wizlop development issues.

## 🚨 Common Issues

### Application Won't Start

```bash
# Check port 3000
lsof -i :3000
kill -9 <PID>

# Clear cache
rm -rf .next
npm run dev
```

**Check:**

- `.env.local` exists with correct values
- Node.js version 18+
- `npm install` completed

### Database Connection Failed

```bash
# Start PostgreSQL
brew services start postgresql        # macOS
sudo service postgresql start         # Ubuntu

# Test connection
psql -h localhost -U wizlop_user -d wizlop_db
```

**Check:**

- Database credentials in `.env.local`
- Database `wizlop_db` exists
- PostgreSQL service running

### Authentication Issues

```bash
# Generate new secret
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

**Check:**

- `NEXTAUTH_SECRET` set in `.env.local`
- Database tables exist
- Browser console for errors

### LLM Engine Not Responding

```bash
# Test connection
curl http://localhost:8000/health

# Check port
lsof -i :8000
```

**Check:**

- LLM engine is running
- `LLM_ENGINE_URL` in `.env.local`
- API endpoint format

## 🔍 Debugging Steps

1. **Check Application Logs** - Terminal where `npm run dev` is running
2. **Check Browser Console** - F12 → Console tab for errors
3. **Test Database** - `psql -h localhost -U wizlop_user -d wizlop_db`
4. **Test API** - `curl http://localhost:3000/api/health`

## 🛠️ Common Error Messages

- **"Module not found"** → Run `npm install`
- **"Database query failed"** → Check database connection
- **"Authentication failed"** → Check `NEXTAUTH_SECRET`
- **"LLM engine timeout"** → Check LLM engine is running

## 🔄 Reset Procedures

### Reset Application

```bash
rm -rf .next node_modules
npm install
npm run dev
```

### Reset Database

```bash
# Recreate database
dropdb wizlop_db && createdb wizlop_db
# Run migrations if available
npm run db:migrate
```

## 📚 Additional Help

- **Setup Guide**: `SETUP.md`
- **Environment Config**: `ENVIRONMENT_SETUP.md`
- **Database Reference**: `CURRENT_DATABASE_DOCUMENTATION.md`
- **NextAuth Config**: `NEXTAUTH_SETUP.md`
