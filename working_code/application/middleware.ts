import { NextRequest, NextResponse } from 'next/server'

// Define protected routes
const PROTECTED_ROUTES = ['/admin', '/agent']

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Check if this is a protected route
  const protectedRoute = PROTECTED_ROUTES.find(route => pathname.startsWith(route))
  if (!protectedRoute) {
    return NextResponse.next()
  }

  // Check for session token (NextAuth default cookie names)
  // For production, you may need to check both cookie names
  const sessionToken =
    request.cookies.get('next-auth.session-token')?.value ||
    request.cookies.get('__Secure-next-auth.session-token')?.value

  if (!sessionToken) {
    // Redirect to login if not authenticated
    const loginUrl = new URL('/auth/signin', request.url)
    loginUrl.searchParams.set('callbackUrl', pathname)
    loginUrl.searchParams.set('error', 'authentication_required')
    return NextResponse.redirect(loginUrl)
  }

  // Authenticated: let request proceed (role checks are done in the page)
  return NextResponse.next()
}

export const config = {
  matcher: ['/admin/:path*', '/agent/:path*'],
}
