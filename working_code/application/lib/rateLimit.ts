/** @format */

import { NextRequest, NextResponse } from 'next/server';
import { config } from './config';
import { logger } from './logger';

interface RateLimitEntry {
	count: number;
	resetTime: number;
}

// In-memory store (in production, use Redis or similar)
const rateLimitStore = new Map<string, RateLimitEntry>();

// Cleanup old entries every 5 minutes
setInterval(() => {
	const now = Date.now();
	for (const [key, entry] of rateLimitStore.entries()) {
		if (entry.resetTime < now) {
			rateLimitStore.delete(key);
		}
	}
}, 5 * 60 * 1000);

export interface RateLimitConfig {
	windowMs?: number;
	maxRequests?: number;
	keyGenerator?: (request: NextRequest) => string;
}

export function createRateLimit(options: RateLimitConfig = {}) {
	const {
		windowMs = config.security.rateLimitWindow,
		maxRequests = config.security.rateLimitMax,
		keyGenerator = (req) => getClientIP(req),
	} = options;

	return async function rateLimit(
		request: NextRequest
	): Promise<NextResponse | null> {
		const key = keyGenerator(request);
		const now = Date.now();
		const resetTime = now + windowMs;

		let entry = rateLimitStore.get(key);

		if (!entry || entry.resetTime < now) {
			// Create new entry or reset expired entry
			entry = { count: 0, resetTime };
			rateLimitStore.set(key, entry);
		}

		entry.count++;

		if (entry.count > maxRequests) {
			logger.securityEvent('Rate limit exceeded', {
				key,
				count: entry.count,
				maxRequests,
				windowMs,
				userAgent: request.headers.get('user-agent'),
				path: request.nextUrl.pathname,
			});

			return NextResponse.json(
				{
					error: 'Too many requests',
					message: 'Rate limit exceeded. Please try again later.',
					retryAfter: Math.ceil((entry.resetTime - now) / 1000),
				},
				{
					status: 429,
					headers: {
						'X-RateLimit-Limit': maxRequests.toString(),
						'X-RateLimit-Remaining': '0',
						'X-RateLimit-Reset': entry.resetTime.toString(),
						'Retry-After': Math.ceil((entry.resetTime - now) / 1000).toString(),
					},
				}
			);
		}

		// Add rate limit headers to successful responses
		const remaining = Math.max(0, maxRequests - entry.count);

		// Store rate limit info for adding to response headers later
		(
			request as unknown as {
				rateLimit?: { limit: number; remaining: number; reset: number };
			}
		).rateLimit = {
			limit: maxRequests,
			remaining,
			reset: entry.resetTime,
		};

		return null; // Continue to next middleware/handler
	};
}

// Helper function to get client IP
function getClientIP(request: NextRequest): string {
	// Check various headers for the real IP
	const forwarded = request.headers.get('x-forwarded-for');
	const realIP = request.headers.get('x-real-ip');
	const cfConnectingIP = request.headers.get('cf-connecting-ip');

	if (forwarded) {
		return forwarded.split(',')[0].trim();
	}

	if (realIP) {
		return realIP;
	}

	if (cfConnectingIP) {
		return cfConnectingIP;
	}

	// Fallback to unknown if no IP headers found
	return 'unknown';
}

// Predefined rate limiters for different endpoints
export const authRateLimit = createRateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	maxRequests: 5, // 5 attempts per 15 minutes
});

export const chatRateLimit = createRateLimit({
	windowMs: 60 * 1000, // 1 minute
	maxRequests: 30, // 30 messages per minute
});

export const apiRateLimit = createRateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	maxRequests: 100, // 100 requests per 15 minutes
});

// Middleware wrapper for API routes
export function withRateLimit(
	handler: (request: NextRequest) => Promise<NextResponse>,
	rateLimiter = apiRateLimit
) {
	return async function (request: NextRequest): Promise<NextResponse> {
		// Apply rate limiting
		const rateLimitResponse = await rateLimiter(request);
		if (rateLimitResponse) {
			return rateLimitResponse;
		}

		// Continue to handler
		const response = await handler(request);

		// Add rate limit headers if available
		const rateLimit = (
			request as unknown as {
				rateLimit?: { limit: number; remaining: number; reset: number };
			}
		).rateLimit;
		if (rateLimit) {
			response.headers.set('X-RateLimit-Limit', rateLimit.limit.toString());
			response.headers.set(
				'X-RateLimit-Remaining',
				rateLimit.remaining.toString()
			);
			response.headers.set('X-RateLimit-Reset', rateLimit.reset.toString());
		}

		return response;
	};
}
