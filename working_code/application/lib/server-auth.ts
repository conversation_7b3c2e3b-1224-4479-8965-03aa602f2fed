/** @format */

// Server-only auth helpers that use direct database operations
// This file should ONLY be imported by API routes, never by client-side code

import { db, table } from './database';

export interface UserProfile {
	id: string;
	username: string;
	name: string;
	age: number;
	avatar_url?: string;
	profile_completed: boolean;
	created_at: string;
	updated_at: string;
}

// Server-side auth helper functions that use direct database operations
export const serverAuthHelpers = {
	// Get user profile directly from database (no HTTP request)
	async getProfile(
		userId: string
	): Promise<{ data: UserProfile | null; error: { message: string } | null }> {
		try {
			const profile = await db.getOne(
				`SELECT id, username, name, age, avatar_url, profile_completed, created_at, updated_at FROM ${table(
					'nextauth_users'
				)} WHERE id = $1`,
				[userId]
			);
			return {
				data: profile,
				error: null,
			};
		} catch (error) {
			console.error('Get profile error:', error);
			return {
				data: null,
				error: { message: 'Failed to get profile' },
			};
		}
	},

	// Get user profile by username
	async getProfileByUsername(
		username: string
	): Promise<{ data: UserProfile | null; error: { message: string } | null }> {
		try {
			const user = await db.getOne(
				`SELECT id, username, name, age, avatar_url, profile_completed, created_at, updated_at FROM ${table(
					'nextauth_users'
				)} WHERE username = $1`,
				[username]
			);
			return {
				data: user,
				error: null,
			};
		} catch (error) {
			console.error('Get profile by username error:', error);
			return {
				data: null,
				error: { message: 'Failed to get profile' },
			};
		}
	},

	// Update user profile directly in database
	async updateProfile(
		userId: string,
		profile: Partial<UserProfile>
	): Promise<{ data: UserProfile | null; error: { message: string } | null }> {
		try {
			// Update nextauth_users directly
			const updatedProfile = await db.update(table('nextauth_users'), userId, {
				username: profile.username,
				name: profile.name,
				age: profile.age,
				avatar_url: profile.avatar_url,
				profile_completed:
					profile.profile_completed !== undefined
						? profile.profile_completed
						: true,
			});
			return {
				data: updatedProfile,
				error: null,
			};
		} catch (error) {
			console.error('Update profile error:', error);
			return {
				data: null,
				error: { message: 'Failed to update profile' },
			};
		}
	},

	// Get user by ID directly from database
	async getUserById(userId: string): Promise<{
		data: Record<string, unknown> | null;
		error: { message: string } | null;
	}> {
		try {
			const user = await db.getOne(
				`SELECT id, email, username FROM ${table(
					'nextauth_users'
				)} WHERE id = $1`,
				[userId]
			);
			return {
				data: user,
				error: null,
			};
		} catch (error) {
			console.error('Get user by ID error:', error);
			return {
				data: null,
				error: { message: 'Failed to get user' },
			};
		}
	},

	// Get user by username directly from database
	async getUserByUsername(username: string): Promise<{
		data: Record<string, unknown> | null;
		error: { message: string } | null;
	}> {
		try {
			const user = await db.getOne(
				`SELECT id, email, username FROM ${table(
					'nextauth_users'
				)} WHERE username = $1`,
				[username]
			);
			return {
				data: user,
				error: null,
			};
		} catch (error) {
			console.error('Get user by username error:', error);
			return {
				data: null,
				error: { message: 'Failed to get user' },
			};
		}
	},
};
