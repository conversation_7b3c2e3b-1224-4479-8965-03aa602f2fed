import { db, table } from './database'

/**
 * Helper functions for converting between username and user ID
 * These functions are used to maintain backward compatibility while
 * transitioning to username-based requests
 */

export interface UserLookupResult {
  id: string
  username: string
  email: string
}

/**
 * Get user ID from username
 * @param username - The username to look up
 * @returns User ID if found, null if not found
 */
export async function getUserIdFromUsername(username: string): Promise<string | null> {
  try {
    const user = await db.getOne(
      `SELECT id FROM ${table('nextauth_users')} WHERE username = $1`,
      [username.toLowerCase()]
    )
    return user?.id || null
  } catch (error) {
    console.error('Error getting user ID from username:', error)
    return null
  }
}

/**
 * Get username from user ID
 * @param userId - The user ID to look up
 * @returns Username if found, null if not found
 */
export async function getUsernameFromUserId(userId: string): Promise<string | null> {
  try {
    const user = await db.getOne(
      `SELECT username FROM ${table('nextauth_users')} WHERE id = $1`,
      [userId]
    )
    return user?.username || null
  } catch (error) {
    console.error('Error getting username from user ID:', error)
    return null
  }
}

/**
 * Get full user info from username
 * @param username - The username to look up
 * @returns User info if found, null if not found
 */
export async function getUserFromUsername(username: string): Promise<UserLookupResult | null> {
  try {
    const user = await db.getOne(
      `SELECT id, username, email FROM ${table('nextauth_users')} WHERE username = $1`,
      [username.toLowerCase()]
    )
    return user || null
  } catch (error) {
    console.error('Error getting user from username:', error)
    return null
  }
}

/**
 * Get full user info from user ID
 * @param userId - The user ID to look up
 * @returns User info if found, null if not found
 */
export async function getUserFromUserId(userId: string): Promise<UserLookupResult | null> {
  try {
    const user = await db.getOne(
      `SELECT id, username, email FROM ${table('nextauth_users')} WHERE id = $1`,
      [userId]
    )
    return user || null
  } catch (error) {
    console.error('Error getting user from user ID:', error)
    return null
  }
}

/**
 * Check if a string is a valid UUID (user ID) or username
 * @param identifier - The string to check
 * @returns 'uuid' if it's a UUID, 'username' if it's a username
 */
export function identifyUserIdentifier(identifier: string): 'uuid' | 'username' {
  // UUID regex pattern
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  
  if (uuidRegex.test(identifier)) {
    return 'uuid'
  }
  return 'username'
}

/**
 * Resolve user identifier to user ID
 * Accepts either username or user ID and returns the user ID
 * @param identifier - Username or user ID
 * @returns User ID if found, null if not found
 */
export async function resolveToUserId(identifier: string): Promise<string | null> {
  const type = identifyUserIdentifier(identifier)
  
  if (type === 'uuid') {
    // It's already a user ID, just verify it exists
    const user = await getUserFromUserId(identifier)
    return user?.id || null
  } else {
    // It's a username, convert to user ID
    return await getUserIdFromUsername(identifier)
  }
}

/**
 * Resolve user identifier to username
 * Accepts either username or user ID and returns the username
 * @param identifier - Username or user ID
 * @returns Username if found, null if not found
 */
export async function resolveToUsername(identifier: string): Promise<string | null> {
  const type = identifyUserIdentifier(identifier)
  
  if (type === 'username') {
    // It's already a username, just verify it exists
    const user = await getUserFromUsername(identifier)
    return user?.username || null
  } else {
    // It's a user ID, convert to username
    return await getUsernameFromUserId(identifier)
  }
}
