/** @format */

import { NextResponse } from 'next/server';
import { config } from './config';
import { logger } from './logger';

// Custom error classes
export class AppError extends Error {
	public readonly statusCode: number;
	public readonly isOperational: boolean;
	public readonly code?: string;

	constructor(
		message: string,
		statusCode: number = 500,
		isOperational: boolean = true,
		code?: string
	) {
		super(message);
		this.statusCode = statusCode;
		this.isOperational = isOperational;
		this.code = code;

		Error.captureStackTrace(this, this.constructor);
	}
}

export class ValidationError extends AppError {
	constructor(message: string) {
		super(message, 400, true, 'VALIDATION_ERROR');
		this.name = 'ValidationError';
	}
}

export class AuthenticationError extends AppError {
	constructor(message: string = 'Authentication required') {
		super(message, 401, true, 'AUTHENTICATION_ERROR');
		this.name = 'AuthenticationError';
	}
}

export class AuthorizationError extends AppError {
	constructor(message: string = 'Insufficient permissions') {
		super(message, 403, true, 'AUTHORIZATION_ERROR');
		this.name = 'AuthorizationError';
	}
}

export class NotFoundError extends AppError {
	constructor(message: string = 'Resource not found') {
		super(message, 404, true, 'NOT_FOUND_ERROR');
		this.name = 'NotFoundError';
	}
}

export class ConflictError extends AppError {
	constructor(message: string = 'Resource conflict') {
		super(message, 409, true, 'CONFLICT_ERROR');
		this.name = 'ConflictError';
	}
}

export class RateLimitError extends AppError {
	constructor(message: string = 'Rate limit exceeded') {
		super(message, 429, true, 'RATE_LIMIT_ERROR');
		this.name = 'RateLimitError';
	}
}

export class ExternalServiceError extends AppError {
	constructor(
		service: string,
		message: string = 'External service unavailable'
	) {
		super(`${service}: ${message}`, 503, true, 'EXTERNAL_SERVICE_ERROR');
		this.name = 'ExternalServiceError';
	}
}

// Error response interface
interface ErrorResponse {
	error: {
		message: string;
		code?: string;
		details?: unknown;
		timestamp: string;
		requestId?: string;
	};
}

// Error handler for API routes
export function handleApiError(
	error: unknown,
	requestId?: string,
	userId?: string
): NextResponse<ErrorResponse> {
	let statusCode = 500;
	let message = 'Internal server error';
	let code: string | undefined;
	let details: unknown;

	if (error instanceof AppError) {
		statusCode = error.statusCode;
		message = error.message;
		code = error.code;

		// Log operational errors as warnings, programming errors as errors
		if (error.isOperational) {
			logger.warn(
				`API Error: ${message}`,
				{
					code,
					statusCode,
					stack:
						config.app.environment === 'development' ? error.stack : undefined,
				},
				{ userId, requestId }
			);
		} else {
			logger.error(
				`Programming Error: ${message}`,
				{
					code,
					statusCode,
					stack: error.stack,
				},
				{ userId, requestId }
			);
		}
	} else if (error instanceof Error) {
		// Unexpected errors
		logger.error(
			`Unexpected Error: ${error.message}`,
			{
				stack: error.stack,
			},
			{ userId, requestId }
		);

		// Don't expose internal error details in production
		if (config.app.environment === 'development') {
			message = error.message;
			details = { stack: error.stack };
		}
	} else {
		// Unknown error type
		logger.error('Unknown Error', { error }, { userId, requestId });
	}

	const errorResponse: ErrorResponse = {
		error: {
			message,
			code,
			details: config.app.environment === 'development' ? details : undefined,
			timestamp: new Date().toISOString(),
			requestId,
		},
	};

	return NextResponse.json(errorResponse, { status: statusCode });
}

// Async error wrapper for API routes
export function asyncHandler<T extends unknown[], R>(
	fn: (...args: T) => Promise<R>
) {
	return (...args: T): Promise<R> => {
		return Promise.resolve(fn(...args)).catch((error: unknown) => {
			throw error;
		});
	};
}

// Database error handler
export function handleDatabaseError(error: unknown): AppError {
	if (typeof error === 'object' && error !== null && 'code' in error) {
		const err = error as { code?: string; message?: string; stack?: string };
		if (err.code === '23505') {
			// Unique constraint violation
			return new ConflictError('Resource already exists');
		}
		if (err.code === '23503') {
			// Foreign key constraint violation
			return new ValidationError('Invalid reference');
		}
		if (err.code === '23502') {
			// Not null constraint violation
			return new ValidationError('Required field missing');
		}
		if (err.code === 'ECONNREFUSED') {
			return new ExternalServiceError('Database', 'Connection refused');
		}
		// Generic database error
		logger.error('Database Error', {
			code: err.code,
			message: err.message,
			stack: err.stack,
		});
		return new AppError('Database operation failed', 500, false);
	}
	return new AppError('Unknown database error', 500, false);
}

// LLM Engine error handler
export function handleLLMEngineError(error: unknown): AppError {
	if (typeof error === 'object' && error !== null) {
		const err = error as { code?: string; status?: number; message?: string };
		if (err.code === 'ECONNREFUSED') {
			return new ExternalServiceError('LLM Engine', 'Service unavailable');
		}
		if (err.code === 'ETIMEDOUT') {
			return new ExternalServiceError('LLM Engine', 'Request timeout');
		}
		if (err.status === 429) {
			return new RateLimitError('LLM Engine rate limit exceeded');
		}
		if (err.status && err.status >= 400 && err.status < 500) {
			return new ValidationError(
				`LLM Engine: ${err.message || 'Invalid request'}`
			);
		}
		return new ExternalServiceError(
			'LLM Engine',
			err.message || 'Unknown error'
		);
	}
	return new ExternalServiceError('LLM Engine', 'Unknown error');
}

// Global error handler for unhandled rejections
export function setupGlobalErrorHandlers() {
	if (typeof window === 'undefined') {
		// Server-side only
		process.on('unhandledRejection', (reason) => {
			logger.error('Unhandled Rejection', {
				reason: reason instanceof Error ? reason.message : reason,
				stack: reason instanceof Error ? reason.stack : undefined,
			});
		});

		process.on('uncaughtException', (error) => {
			logger.error('Uncaught Exception', {
				message: error.message,
				stack: error.stack,
			});

			// Graceful shutdown
			process.exit(1);
		});
	}
}
