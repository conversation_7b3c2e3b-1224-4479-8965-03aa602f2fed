/** @format */

// Production-ready configuration management
// This centralized config is perfect for AWS ECS deployment
export const config = {
	// Database Configuration
	database: {
		host: process.env.DB_HOST || 'localhost',
		port: parseInt(process.env.DB_PORT || '5432'),
		name: process.env.DB_NAME || 'wizlop_db',
		user: process.env.DB_USER || 'postgres',
		password: process.env.DB_PASSWORD || 'password',
		url: process.env.DATABASE_URL,
		ssl:
			process.env.DATABASE_SSL === 'true'
				? {
						rejectUnauthorized: false,
						requestCert: false,
						agent: false,
				  }
				: false,
		maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
		idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
	},

	// LLM Engine Configuration
	llmEngine: {
		url: process.env.LLM_ENGINE_URL || 'http://localhost:8000',
		timeout: parseInt(process.env.LLM_ENGINE_TIMEOUT || '120000'), // 2 minutes
		retries: parseInt(process.env.LLM_ENGINE_RETRIES || '3'),
		apiKey: process.env.LLM_ENGINE_API_KEY,
	},

	// Application Configuration
	app: {
		name: 'Wizlop',
		version: process.env.npm_package_version || '1.0.0',
		environment: process.env.NODE_ENV || 'development',
		port: parseInt(process.env.PORT || '3000'),
		url: process.env.NEXTAUTH_URL || 'http://localhost:3000',
		secret: process.env.NEXTAUTH_SECRET || 'development-secret-key',
	},

	// OAuth Configuration
	oauth: {
		google: {
			clientId: process.env.GOOGLE_CLIENT_ID,
			clientSecret: process.env.GOOGLE_CLIENT_SECRET,
		},
	},

	// Security Configuration
	security: {
		bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),
		sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '86400000'), // 24 hours
		rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW || '900000'), // 15 minutes
		rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '100'),
		corsOrigins: process.env.CORS_ORIGINS?.split(',') || [
			'http://localhost:3000',
		],
	},

	// Feature Flags
	features: {
		enableRegistration: process.env.ENABLE_REGISTRATION !== 'false',
		enableChat: process.env.ENABLE_CHAT !== 'false',
		enableAnalytics: process.env.ENABLE_ANALYTICS === 'true',
		enableMaps: process.env.ENABLE_MAPS === 'true',
		maintenanceMode: process.env.MAINTENANCE_MODE === 'true',
	},

	// Logging Configuration
	logging: {
		level: process.env.LOG_LEVEL || 'info',
		enableConsole: process.env.LOG_CONSOLE !== 'false',
		enableFile: process.env.LOG_FILE === 'true',
		filePath: process.env.LOG_FILE_PATH || './logs/app.log',
		enablePerformanceLogs: process.env.ENABLE_PERFORMANCE_LOGS === 'true',
		performanceLogThreshold: parseInt(
			process.env.PERFORMANCE_LOG_THRESHOLD || '100'
		), // Only log queries slower than this (ms)
	},

	// Chat Configuration
	chat: {
		maxMessageLength: parseInt(process.env.CHAT_MAX_MESSAGE_LENGTH || '2000'),
		maxMessagesPerSession: parseInt(process.env.CHAT_MAX_MESSAGES || '100'),
		sessionTimeout: parseInt(process.env.CHAT_SESSION_TIMEOUT || '3600000'), // 1 hour
		enableHistory: process.env.CHAT_ENABLE_HISTORY !== 'false',
	},
};

// Validation function - perfect for AWS ECS health checks
export function validateConfig() {
	const errors: string[] = [];

	// Required environment variables for production
	if (config.app.environment === 'production') {
		// Critical security requirements
		if (!config.app.secret || config.app.secret === 'development-secret-key') {
			errors.push('NEXTAUTH_SECRET must be set in production');
		}

		if (!config.app.url || config.app.url.includes('localhost')) {
			errors.push('NEXTAUTH_URL must be set to production domain');
		}

		// Database requirements
		if (!config.database.url && !config.database.password) {
			errors.push('Database configuration is incomplete for production');
		}

		if (!config.llmEngine.url || config.llmEngine.url.includes('localhost')) {
			errors.push('LLM_ENGINE_URL must be set to production endpoint');
		}

		// OAuth requirements (if Google OAuth is enabled)
		if (!config.oauth.google.clientId || !config.oauth.google.clientSecret) {
			console.warn(
				'Google OAuth credentials not set - Google sign-in will be disabled'
			);
		}
	}

	// Validate numeric values
	if (config.security.bcryptRounds < 10 || config.security.bcryptRounds > 15) {
		errors.push('BCRYPT_ROUNDS should be between 10 and 15');
	}

	if (
		config.chat.maxMessageLength < 100 ||
		config.chat.maxMessageLength > 10000
	) {
		errors.push('CHAT_MAX_MESSAGE_LENGTH should be between 100 and 10000');
	}

	if (errors.length > 0) {
		console.error('Configuration validation failed:');
		errors.forEach((error) => console.error(`  - ${error}`));
		throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
	}

	console.log(
		`✅ Configuration validated for ${config.app.environment} environment`
	);

	return {
		isValid: true,
		errors: [],
	};
}

// Helper functions for environment and configuration
export const isDevelopment = () => config.app.environment === 'development';
export const isProduction = () => config.app.environment === 'production';
export const isMaintenanceMode = () => config.features.maintenanceMode;

// Helper function to get environment-specific database URL
export function getDatabaseUrl(): string {
	if (config.database.url) {
		return config.database.url;
	}

	const sslMode = config.database.ssl ? 'require' : 'disable';
	return `postgresql://${config.database.user}:${config.database.password}@${config.database.host}:${config.database.port}/${config.database.name}?sslmode=${sslMode}`;
}

// Helper to get OAuth configuration
export function getOAuthConfig() {
	return {
		google: {
			enabled: !!(
				config.oauth.google.clientId && config.oauth.google.clientSecret
			),
			clientId: config.oauth.google.clientId,
			clientSecret: config.oauth.google.clientSecret,
		},
	};
}
