/** @format */

import bcrypt from 'bcryptjs';
import { NextAuthOptions } from 'next-auth';
import { VerificationToken } from 'next-auth/adapters';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';
import { Pool } from 'pg';
import { table } from './database';

// Database connection for NextAuth
const pool = new Pool({
	host: process.env.DB_HOST || 'localhost',
	port: parseInt(process.env.DB_PORT || '5432'),
	database: process.env.DB_NAME || 'wizlop_db',
	user: process.env.DB_USER || 'postgres',
	password: process.env.DB_PASSWORD || 'password',
	ssl:
		process.env.DATABASE_SSL === 'true' ? { rejectUnauthorized: false } : false,
});

// Custom database adapter for NextAuth to use backend_schema
const customAdapter = {
	async createUser(data: Record<string, unknown>) {
		const result = await pool.query(
			`INSERT INTO ${table(
				'nextauth_users'
			)} (id, name, email, "emailVerified", image) VALUES ($1, $2, $3, $4, $5) RETURNING *`,
			[data.id, data.name, data.email, data.emailVerified, data.image]
		);
		return result.rows[0];
	},
	async getUser(id: string) {
		const result = await pool.query(
			`SELECT * FROM ${table('nextauth_users')} WHERE id = $1`,
			[id]
		);
		return result.rows[0] || null;
	},
	async getUserByEmail(email: string) {
		const result = await pool.query(
			`SELECT * FROM ${table('nextauth_users')} WHERE email = $1`,
			[email]
		);
		return result.rows[0] || null;
	},
	async getUserByAccount({
		provider,
		providerAccountId,
	}: {
		provider: string;
		providerAccountId: string;
	}) {
		const result = await pool.query(
			`SELECT u.* FROM ${table('nextauth_users')} u 
       JOIN ${table('nextauth_accounts')} a ON u.id = a."userId" 
       WHERE a.provider = $1 AND a."providerAccountId" = $2`,
			[provider, providerAccountId]
		);
		return result.rows[0] || null;
	},
	async updateUser(data: Record<string, unknown>) {
		const result = await pool.query(
			`UPDATE ${table(
				'nextauth_users'
			)} SET name = $2, email = $3, "emailVerified" = $4, image = $5 WHERE id = $1 RETURNING *`,
			[data.id, data.name, data.email, data.emailVerified, data.image]
		);
		return result.rows[0];
	},
	async deleteUser(userId: string) {
		await pool.query(`DELETE FROM ${table('nextauth_users')} WHERE id = $1`, [
			userId,
		]);
	},
	async linkAccount(data: Record<string, unknown>) {
		await pool.query(
			`INSERT INTO ${table('nextauth_accounts')} 
       (id, "userId", type, provider, "providerAccountId", refresh_token, access_token, expires_at, token_type, scope, id_token, session_state) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)`,
			[
				data.id,
				data.userId,
				data.type,
				data.provider,
				data.providerAccountId,
				data.refresh_token,
				data.access_token,
				data.expires_at,
				data.token_type,
				data.scope,
				data.id_token,
				data.session_state,
			]
		);
	},
	async unlinkAccount({
		provider,
		providerAccountId,
	}: {
		provider: string;
		providerAccountId: string;
	}) {
		await pool.query(
			`DELETE FROM ${table(
				'nextauth_accounts'
			)} WHERE provider = $1 AND "providerAccountId" = $2`,
			[provider, providerAccountId]
		);
	},
	async createSession(data: Record<string, unknown>) {
		const result = await pool.query(
			`INSERT INTO ${table(
				'nextauth_sessions'
			)} (id, "sessionToken", "userId", expires) VALUES ($1, $2, $3, $4) RETURNING *`,
			[data.id, data.sessionToken, data.userId, data.expires]
		);
		return result.rows[0];
	},
	async getSessionAndUser(sessionToken: string) {
		const result = await pool.query(
			`SELECT s.*, u.* FROM ${table('nextauth_sessions')} s 
       JOIN ${table('nextauth_users')} u ON s."userId" = u.id 
       WHERE s."sessionToken" = $1`,
			[sessionToken]
		);
		if (result.rows.length === 0) return null;

		const row = result.rows[0];
		// Create user object with all user fields, excluding session-specific fields
		const user = {
			id: row.id,
			name: row.name,
			email: row.email,
			emailVerified: row.emailVerified,
			image: row.image,
			username: row.username,
			role: row.role,
			permissions: row.permissions,
			profile_picture_url: row.profile_picture_url,
		};

		return {
			session: {
				sessionToken,
				userId: row.userId,
				expires: row.expires,
			},
			user,
		};
	},
	async updateSession(data: Record<string, unknown>) {
		const result = await pool.query(
			`UPDATE ${table(
				'nextauth_sessions'
			)} SET "sessionToken" = $2, "userId" = $3, expires = $4 WHERE id = $1 RETURNING *`,
			[data.id, data.sessionToken, data.userId, data.expires]
		);
		return result.rows[0];
	},
	async createVerificationToken(
		data: VerificationToken
	): Promise<VerificationToken> {
		const result = await pool.query(
			`INSERT INTO ${table(
				'nextauth_verification_tokens'
			)} (identifier, token, expires) VALUES ($1, $2, $3) RETURNING *`,
			[data.identifier, data.token, data.expires]
		);
		return result.rows[0];
	},
	async useVerificationToken({
		identifier,
		token,
	}: {
		identifier: string;
		token: string;
	}): Promise<VerificationToken | null> {
		const result = await pool.query(
			`DELETE FROM ${table(
				'nextauth_verification_tokens'
			)} WHERE identifier = $1 AND token = $2 RETURNING *`,
			[identifier, token]
		);
		return result.rows[0] || null;
	},
};

export const authOptions: NextAuthOptions = {
	adapter: customAdapter,
	providers: [
		GoogleProvider({
			clientId: process.env.GOOGLE_CLIENT_ID!,
			clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
		}),
		CredentialsProvider({
			name: 'credentials',
			credentials: {
				email: { label: 'Email', type: 'email' },
				password: { label: 'Password', type: 'password' },
			},
			async authorize(credentials) {
				if (!credentials?.email || !credentials?.password) {
					return null;
				}

				try {
					// Find user by email in nextauth_users table
					const result = await pool.query(
						`SELECT id, email, name, username, password_hash, role, permissions 
             FROM ${table('nextauth_users')} 
             WHERE email = $1`,
						[credentials.email]
					);

					const user = result.rows[0];

					if (!user) {
						return null;
					}

					// Verify password
					const isValidPassword = await bcrypt.compare(
						credentials.password,
						user.password_hash
					);

					if (!isValidPassword) {
						return null;
					}

					// Ensure role is one of the allowed literals
					const allowedRoles = ['user', 'agent', 'superuser'];
					const role = allowedRoles.includes(user.role) ? user.role : 'user';
					const permissions = Array.isArray(user.permissions)
						? user.permissions
						: [];

					return {
						id: user.id,
						email: user.email,
						name: user.name,
						username: user.username || user.name,
						role: role,
						permissions: permissions,
					};
				} catch (error) {
					console.error('Auth error:', error);
					return null;
				}
			},
		}),
	],
	session: {
		strategy: 'jwt',
		maxAge: 30 * 24 * 60 * 60, // 30 days
		updateAge: 24 * 60 * 60, // 24 hours - refresh session daily
	},
	cookies: {
		sessionToken: {
			name: `next-auth.session-token`,
			options: {
				httpOnly: true,
				sameSite: 'lax',
				path: '/',
				secure: process.env.NODE_ENV === 'production',
				maxAge: 30 * 24 * 60 * 60, // 30 days
			},
		},
	},
	callbacks: {
		async jwt({ token, user }) {
			if (user) {
				token.id = user.id;
				token.username = user.username;
			}

			// Always fetch fresh data from DB to ensure profile picture is up to date
			if (token.id) {
				const result = await pool.query(
					`SELECT role, permissions, profile_picture_url, image FROM ${table(
						'nextauth_users'
					)} WHERE id = $1`,
					[token.id]
				);
				const dbUser = result.rows[0];
				token.role = dbUser?.role || 'user';
				token.permissions = dbUser?.permissions || [];
				token.profile_picture_url =
					dbUser?.profile_picture_url || dbUser?.image;
			}

			return token;
		},
		async session({ session, token }) {
			if (token) {
				session.user.id = token.id as string;
				session.user.username = token.username as string;
				session.user.role = token.role as 'user' | 'agent' | 'superuser';
				session.user.permissions = token.permissions as string[];
				session.user.profile_picture_url = token.profile_picture_url as string;
				// Also set image for backward compatibility
				session.user.image = token.profile_picture_url as string;
			}
			return session;
		},
	},
	pages: {
		signIn: '/auth',
	},
	secret: process.env.NEXTAUTH_SECRET,
};
