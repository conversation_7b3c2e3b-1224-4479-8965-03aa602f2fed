/** @format */

/**
 * Centralized credit configuration for all POI actions and interactions
 * All credit values are read from environment variables for easy configuration
 * Daily limits removed for interactions (likes, visits, favorites, shares)
 */
export const creditConfig = {
	poi: {
		add: parseInt(process.env.CREDIT_POI_ADD || '5'), // Upon agent approval of new POI submission
		update: parseInt(process.env.CREDIT_POI_UPDATE || '5'), // Upon agent approval of POI info update
		delete: parseInt(process.env.CREDIT_POI_DELETE || '5'), // Upon agent approval of POI closure/deletion request
	},
	chat: {
		request: parseInt(process.env.CREDIT_CHAT_REQUEST || '-1'), // Deducts 1 credit per chat request
	},
	// NOTE: All daily limits removed - no limits for any actions
} as const;

/**
 * Credit reasons for transaction tracking
 */
export const creditReasons = {
	poi: {
		add: 'poi_submission_approved',
		update: 'poi_update_approved',
		delete: 'poi_deletion_approved',
	},
	chat: {
		request: 'chat_request',
	},
} as const;

/**
 * Credit descriptions for user-friendly transaction history
 */
export const creditDescriptions = {
	poi: {
		add: (poiName: string) => `POI submission approved: ${poiName}`,
		update: (poiName: string) => `POI update approved: ${poiName}`,
		delete: (poiName: string) => `POI deletion approved: ${poiName}`,
	},
	chat: {
		request: 'Chat request processed',
	},
} as const;
