➜  application git:(said-test) npm run build:webpack

> wizlop@0.1.0 build:webpack
> webpack --config config/webpack/webpack.dev.js

asset bundle.js 4.25 MiB [compared for emit] [big] (name: main)
runtime modules 1.04 KiB 5 modules
modules by path ./node_modules/ 1.48 MiB 109 modules
modules by path ./app/ 50.2 KiB
  modules by path ./app/landing/components/ 45.7 KiB
    ./app/landing/components/index.ts 439 bytes [built] [code generated]
    ./app/landing/components/LandingPage.tsx 1.08 KiB [built] [code generated]
    ./app/landing/components/CreditsSection.tsx 5.47 KiB [built] [code generated]
    ./app/landing/components/CTASection.tsx 4.73 KiB [built] [code generated]
    ./app/landing/components/FeatureSection.tsx 12.3 KiB [built] [code generated]
    ./app/landing/components/Footer.tsx 5.12 KiB [built] [code generated]
    ./app/landing/components/HeroSection.tsx 11 KiB [built] [code generated]
    ./app/landing/components/HowItWorksSection.tsx 5.59 KiB [built] [code generated]
  ./app/page.tsx 1.13 KiB [built] [code generated]
  ./app/colors.ts 3.4 KiB [built] [code generated]

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/cards/components/POICard.tsx
324:10-29
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/cards/components/POICard.tsx(324,11)
      TS18048: 'window.__loggedPOIs' is possibly 'undefined'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/cards/components/POICard.tsx(324,11)
      TS18048: 'window.__loggedPOIs' is possibly 'undefined'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/cards/components/POICard.tsx
335:9-28
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/cards/components/POICard.tsx(335,10)
      TS18048: 'window.__loggedPOIs' is possibly 'undefined'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/cards/components/POICard.tsx(335,10)
      TS18048: 'window.__loggedPOIs' is possibly 'undefined'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx
159:47-68
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(159,48)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(159,48)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx
160:10-31
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(160,11)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(160,11)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx
164:46-67
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(164,47)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(164,47)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx
165:25-46
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(165,26)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(165,26)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx
175:35-56
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(175,36)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(175,36)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx
296:49-70
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(296,50)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(296,50)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx
297:13-34
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(297,14)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(297,14)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx
327:48-69
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(327,49)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(327,49)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx
328:27-48
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(328,28)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(328,28)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx
329:38-52
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(329,39)
      TS2454: Variable 'loadingPromise' is used before being assigned.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(329,39)
      TS2454: Variable 'loadingPromise' is used before being assigned.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx
330:13-34
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(330,14)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(330,14)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx
340:46-67
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(340,47)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(340,47)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx
341:10-31
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(341,11)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/hooks/useInteractions.tsx(341,11)
      TS2339: Property '__poiInteractionCache' does not exist on type 'Window & typeof globalThis'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

webpack 5.100.1 compiled with 15 er