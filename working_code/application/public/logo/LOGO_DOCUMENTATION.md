<!-- @format -->

# Logo Documentation

This document provides a comprehensive overview of logo usage throughout the Wizlop application, including file locations, usage contexts, and specific line references.

## 📁 Logo File Structure

### Current Clean Structure (Centralized in /logo/)

```
application/public/
└── logo/
    ├── favicon.ico            # Browser favicon (16x16)
    ├── 32x32.png             # Browser icon metadata
    ├── 192x192.png           # PWA/Apple touch icon
    └── 512x512.png           # High-resolution logo for all uses
```

### Removed Duplicates

- ❌ `application/logo/` (entire directory removed)
- ❌ `application/public/16x16.ico` (duplicate of favicon.ico)
- ❌ `application/public/40x40.png` (no longer used)
- ❌ `application/public/logo.svg` (not used)
- ❌ `application/public/logo/16x16.ico` (duplicate)
- ❌ `application/public/logo/32x32.png` (duplicate)
- ❌ `application/public/logo/192x192.png` (duplicate)
- ❌ `application/public/logo/40x40.png` (no longer used)
- ❌ `application/public/logo/logo.svg` (not used)

## 🎯 Logo Usage by Context

### 1. Browser Metadata & PWA Icons

#### File: `application/app/layout.tsx`

**Purpose**: Browser tab icons and PWA metadata

```typescript
// Lines 11-25
export const metadata: Metadata = {
	title: 'Wizlop - Global Exploration Through AI',
	description:
		'AI-powered chat application for exploring the world with real-time geolocation',
	icons: {
		icon: [
			{ url: '/logo/favicon.ico', sizes: '16x16', type: 'image/x-icon' }, // Line 13
			{ url: '/logo/32x32.png', sizes: '32x32', type: 'image/png' }, // Line 14
		],
		apple: [
			{ url: '/logo/192x192.png', sizes: '192x192', type: 'image/png' }, // Line 17
		],
		other: [
			{ url: '/logo/192x192.png', sizes: '192x192', type: 'image/png' }, // Line 20
			{ url: '/logo/512x512.png', sizes: '512x512', type: 'image/png' }, // Line 21
		],
	},
	manifest: '/manifest.json',
};
```

**Files Used**:

- `/logo/favicon.ico` → `application/public/logo/favicon.ico`
- `/logo/32x32.png` → `application/public/logo/32x32.png`
- `/logo/192x192.png` → `application/public/logo/192x192.png`
- `/logo/512x512.png` → `application/public/logo/512x512.png`

#### File: `application/public/manifest.json`

**Purpose**: Progressive Web App configuration

```json
// Lines 9-20
"icons": [
  {
    "src": "/logo/192x192.png",        // Line 11
    "sizes": "192x192",
    "type": "image/png"
  },
  {
    "src": "/logo/512x512.png",        // Line 16
    "sizes": "512x512",
    "type": "image/png"
  }
]
```

**Files Used**:

- `/logo/192x192.png` → `application/public/logo/192x192.png`
- `/logo/512x512.png` → `application/public/logo/512x512.png`

### 2. UI Component Logos

#### File: `application/app/landing/components/Header.tsx`

**Purpose**: Landing page header logo

```typescript
// Lines 70-81
{
	/* Logo */
}
<div className='flex items-center'>
	<div className='w-16 h-16 relative'>
		<Image
			src='/logo/512x512.png' // Line 74
			alt='Logo' // Line 75
			width={64} // Line 76
			height={64} // Line 77
			className='rounded-xl' // Line 78
		/>
	</div>
</div>;
```

**Files Used**:

- `/logo/512x512.png` → `application/public/logo/512x512.png`

**Display**: 64x64 pixels with rounded corners

#### File: `application/app/chat/ChatPage.tsx`

**Purpose**: Chat page top bar logo

```typescript
// Lines 1510-1522
<div className='flex-1 flex justify-center'>
	<div className='flex items-center'>
		<div className='w-10 h-10 relative'>
			<Image
				src='/logo/512x512.png' // Line 1514
				alt='Logo' // Line 1515
				width={40} // Line 1516
				height={40} // Line 1517
				className='rounded-lg' // Line 1518
			/>
		</div>
	</div>
</div>
```

**Files Used**:

- `/logo/512x512.png` → `application/public/logo/512x512.png`

**Display**: 40x40 pixels with rounded corners

#### File: `application/app/chat/ChatPage.tsx`

**Purpose**: Chat page mobile header logo

```typescript
// Lines 1795-1805
<div className='flex items-center'>
	<div className='w-10 h-10 relative'>
		<Image
			src='/logo/512x512.png' // Line 1798
			alt='Logo' // Line 1799
			width={40} // Line 1800
			height={40} // Line 1801
			className='rounded-lg' // Line 1802
		/>
	</div>
</div>
```

**Files Used**:

- `/logo/512x512.png` → `application/public/logo/512x512.png`

**Display**: 40x40 pixels with rounded corners

## 📊 Logo Usage Summary

| Context            | File Path                       | Logo Source         | Display Size | Line Reference |
| ------------------ | ------------------------------- | ------------------- | ------------ | -------------- |
| Browser Favicon    | `app/layout.tsx`                | `/logo/favicon.ico` | 16x16        | Line 13        |
| Browser Icon       | `app/layout.tsx`                | `/logo/32x32.png`   | 32x32        | Line 14        |
| Apple Touch Icon   | `app/layout.tsx`                | `/logo/192x192.png` | 192x192      | Line 17        |
| PWA Large Icon     | `app/layout.tsx`                | `/logo/512x512.png` | 512x512      | Line 21        |
| PWA Manifest Small | `public/manifest.json`          | `/logo/192x192.png` | 192x192      | Line 11        |
| PWA Manifest Large | `public/manifest.json`          | `/logo/512x512.png` | 512x512      | Line 16        |
| Landing Header     | `landing/components/Header.tsx` | `/logo/512x512.png` | 64x64        | Line 74        |
| Chat Top Bar       | `chat/ChatPage.tsx`             | `/logo/512x512.png` | 40x40        | Line 1514      |
| Chat Mobile Header | `chat/ChatPage.tsx`             | `/logo/512x512.png` | 40x40        | Line 1798      |

## 🔧 Technical Implementation

### Image Component Usage

All UI logos use Next.js `Image` component for optimization:

```typescript
import Image from 'next/image';

<Image
	src='/logo/512x512.png'
	alt='Logo'
	width={40}
	height={40}
	className='rounded-lg'
/>;
```

### Benefits of Current Structure

1. **High Resolution**: All UI components use 512x512 source for crisp display
2. **Optimized Loading**: Next.js Image component provides automatic optimization
3. **Consistent Branding**: Single high-quality source file for all UI usage
4. **Clean Structure**: No duplicate files, clear separation of concerns
5. **PWA Ready**: Proper icon sizes for Progressive Web App installation

### File Size Optimization

- UI components display at small sizes (40-48px) but use high-resolution source
- Next.js automatically optimizes and serves appropriate sizes
- Browser caches optimized versions for performance

## 📝 Maintenance Notes

### Adding New Logo Usage

1. Use `/logo/512x512.png` for all new UI components
2. Use appropriate files from `/public/` root for metadata/PWA needs
3. Always use Next.js `Image` component for UI logos
4. Update this documentation with new usage

### Updating Logo Files

1. Replace files in their respective locations
2. Maintain same filenames to avoid breaking references
3. Ensure 512x512.png remains high quality for UI scaling
4. Test all contexts after updates

### File Naming Convention

- **Metadata files**: Direct in `/public/` (e.g., `favicon.ico`, `32x32.png`)
- **UI component files**: In `/public/logo/` subdirectory
- **Naming**: Use dimensions in filename (e.g., `512x512.png`)

---

**Last Updated**: December 2024  
**Maintained By**: Development Team  
**Next Review**: When adding new logo usage or updating brand assets
