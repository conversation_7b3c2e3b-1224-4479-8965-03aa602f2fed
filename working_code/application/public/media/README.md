# Media Storage Directory

This directory contains user-generated media files organized in a structured hierarchy.

## Directory Structure

```
media/
├── user-{user-id}/
│   ├── profile/
│   │   ├── profile-picture.jpg
│   │   └── thumbnails/
│   │       ├── profile-thumb-small.jpg
│   │       └── profile-thumb-medium.jpg
│   └── posts/
│       └── poi-{poi-id}/
│           ├── post-media.jpg
│           └── thumbnails/
│               ├── post-thumb-small.jpg
│               └── post-thumb-medium.jpg
└── poi-{poi-id}/
    └── profile/
        └── poi-profile.jpg
```

## File Naming Convention

- Profile pictures: `profile-{timestamp}-{uuid}.{ext}`
- Post media: `post-{timestamp}-{uuid}.{ext}`
- Thumbnails: `{original-name}-thumb-{size}.{ext}`

## Security Features

- File type validation
- Size limits enforcement
- Security scanning
- Rate limiting
- Automatic cleanup of empty directories

## Storage Limits

- Profile pictures: 5MB max
- Post media: 20MB max
- Total per user: 100MB max

## Supported Formats

### Images
- JPEG (.jpg, .jpeg)
- PNG (.png)
- WebP (.webp)
- GIF (.gif)

### Videos
- MP4 (.mp4)
- WebM (.webm)
- MOV (.mov)
- AVI (.avi)

## Maintenance

The system automatically:
- Cleans up empty directories
- Validates file integrity
- Monitors storage usage
- Enforces security policies

## Future Migration

This local storage system is designed for easy migration to AWS S3 CDN.
All file operations are abstracted through the MediaManager service.
