# =============================================================================
# DEVELOPMENT ENVIRONMENT CONFIGURATION
# =============================================================================
# This file is for local development only.
# For production, use environment variables in your deployment platform.

# =============================================================================
# APPLICATION CORE
# =============================================================================
NODE_ENV=development
PORT=3000

# =============================================================================
# NEXTAUTH CONFIGURATION
# =============================================================================
NEXTAUTH_URL=http://localhost:3000
# Generate a secure secret: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
NEXTAUTH_SECRET=ca7b1cff4915b75a92e681cc18d3f84ae8a0ea71b195c272d326a608babaddec

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=wizlop_db
DB_SCHEMA=backend_schema
DB_USER=wizlop_user
DB_PASSWORD=wizlop_pass
DB_MAX_CONNECTIONS=20
DB_IDLE_TIMEOUT=30000
DATABASE_SSL=false

# Alternative database URL format (used by some ORMs)
DATABASE_URL=postgresql://wizlop_user:wizlop_pass@localhost:5432/wizlop_db?sslmode=disable

# =============================================================================
# OAUTH PROVIDERS
# =============================================================================
# Google OAuth (optional - for Google sign-in)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# =============================================================================
# LLM ENGINE CONFIGURATION
# =============================================================================
LLM_ENGINE_URL=http://localhost:8000
LLM_ENGINE_TIMEOUT=30000
LLM_ENGINE_RETRIES=3
LLM_ENGINE_API_KEY=

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
BCRYPT_ROUNDS=12
SESSION_TIMEOUT=86400000
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
CORS_ORIGINS=http://localhost:3000

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_REGISTRATION=true
ENABLE_CHAT=true
ENABLE_ANALYTICS=false
ENABLE_MAPS=true
MAINTENANCE_MODE=false

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_CONSOLE=true
LOG_FILE=false
LOG_FILE_PATH=./logs/app.log
# Performance logging (set to true to enable verbose DB query logs)
ENABLE_PERFORMANCE_LOGS=false
# Only log queries slower than this threshold (in milliseconds)
PERFORMANCE_LOG_THRESHOLD=100

# =============================================================================
# CHAT CONFIGURATION
# =============================================================================
CHAT_MAX_MESSAGE_LENGTH=2000
CHAT_MAX_MESSAGES=100
CHAT_SESSION_TIMEOUT=3600000
CHAT_ENABLE_HISTORY=true

# =============================================================================
# CREDIT SYSTEM CONFIGURATION
# =============================================================================
# POI Credits (awarded upon agent approval)
CREDIT_POI_ADD=5
CREDIT_POI_UPDATE=5
CREDIT_POI_DELETE=5

# Chat Credits (deducted per request)
CREDIT_CHAT_REQUEST=-1

# NOTE: All daily limits removed - no limits for any actions

# =============================================================================
# PUBLIC ENVIRONMENT VARIABLES (accessible in browser)
# =============================================================================
NEXT_PUBLIC_MAINTENANCE_MODE=false
NEXT_PUBLIC_MAINTENANCE_MESSAGE=We're performing scheduled maintenance
NEXT_PUBLIC_MAINTENANCE_ETA=We'll be back shortly
