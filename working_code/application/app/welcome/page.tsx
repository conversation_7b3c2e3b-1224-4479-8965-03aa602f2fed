/** @format */

'use client';

import {
	LocationSetup,
	useLocationManager,
} from '@/app/shared/locationManager';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

const WelcomePage: React.FC = () => {
	const router = useRouter();
	const { status } = useSession();
	const { needsLocationSetup } = useLocationManager();
	const [showLocationSetup, setShowLocationSetup] = useState(false);
	const [isReady, setIsReady] = useState(false);

	useEffect(() => {
		if (status === 'authenticated') {
			// Show location setup if needed
			if (needsLocationSetup()) {
				setShowLocationSetup(true);
			}

			setIsReady(true);
		} else if (status === 'unauthenticated') {
			router.push('/auth');
		}
	}, [status, needsLocationSetup, router]);

	const handleLocationSetupComplete = (choice: 'auto' | 'manual' | 'none') => {
		setShowLocationSetup(false);

		// Handle navigation based on choice
		if (choice === 'manual') {
			// Redirect to settings for manual location entry
			router.push(
				'/settings?tab=location&return=' + encodeURIComponent('/chat')
			);
		} else {
			// For 'auto' and 'none', redirect to chat
			setTimeout(() => {
				router.push('/chat');
			}, 500);
		}
	};

	const handleSkipToChat = () => {
		router.push('/chat');
	};

	if (status === 'loading' || !isReady) {
		return (
			<div className='min-h-screen bg-gradient-to-br from-wizlop-10 to-wizlop-25 flex items-center justify-center'>
				<div className='text-center'>
					<div className='w-8 h-8 border-2 border-wizlop-500 border-t-transparent rounded-full animate-spin mx-auto mb-4'></div>
					<p className='text-wizlop-600'>Loading...</p>
				</div>
			</div>
		);
	}

	return (
		<>
			<LocationSetup
				isOpen={showLocationSetup}
				onComplete={handleLocationSetupComplete}
				title='Welcome to Wizlop!'
				subtitle="Let's set up your location preferences to provide you with the best experience"
				showCloseButton={false}
				redirectAfterSetup='/chat'
			/>

			<div className='min-h-screen bg-gradient-to-br from-wizlop-10 to-wizlop-25 flex items-center justify-center p-4'>
				<div className='max-w-md w-full'>
					<div className='bg-white/95 backdrop-blur-sm rounded-2xl p-8 shadow-2xl text-center'>
						<div className='text-6xl mb-6'>🎉</div>
						<h1 className='text-3xl font-bold text-wizlop-800 mb-4'>
							Welcome to Wizlop!
						</h1>
						<p className='text-wizlop-600 mb-8'>
							Your account has been created successfully. You're ready to
							explore the world with AI-powered location insights.
						</p>

						{!showLocationSetup && (
							<div className='space-y-4'>
								<button
									onClick={handleSkipToChat}
									className='w-full bg-wizlop-500 hover:bg-wizlop-600 text-white font-medium py-3 px-6 rounded-xl transition-colors'>
									Start Exploring
								</button>

								<p className='text-sm text-wizlop-500'>
									You can set up location preferences later in Settings
								</p>
							</div>
						)}
					</div>
				</div>
			</div>
		</>
	);
};

export default WelcomePage;
