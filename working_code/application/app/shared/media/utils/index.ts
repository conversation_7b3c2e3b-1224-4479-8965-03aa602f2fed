/**
 * Media Utilities
 *
 * Shared utilities for media processing, validation, and management
 */

// Core utilities
export {
  MEDIA_CONFIG,
  MediaPathHelper,
  DirectoryManager,
  FileOperations
} from './mediaUtils'

// File validation
export {
  FileValidator,
  SecurityUtils,
  type FileValidationResult,
  type ValidationOptions
} from './fileValidation'

// Image processing
export {
  ImageProcessor,
  type ImageProcessingOptions,
  type ThumbnailOptions,
  type ProcessingResult
} from './imageProcessing'

// Security service
export {
  MediaSecurityService,
  type SecurityScanResult
} from './securityService'

// Directory setup and management
export {
  MediaDirectorySetup,
  type DirectorySetupResult,
  type DirectoryHealthCheck
} from './directorySetup'

// Media manager (main interface)
export { MediaManager } from './MediaManager'

// Database service
export {
  MediaDatabaseService,
  type MediaRecord,
  type MediaWithPOI,
  type MediaQueryOptions
} from './mediaService'

