/**
 * Media Storage Utilities
 * Handles file operations, directory management, and path generation for user/POI media
 *
 * @format
 */

import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Media storage configuration
export const MEDIA_CONFIG = {
	// Base media directory (relative to public)
	BASE_DIR: 'media',

	// Directory structure
	DIRECTORIES: {
		USER_PROFILE: 'profile',
		USER_POSTS: 'posts',
		POI_PROFILE: 'profile',
		THUMBNAILS: 'thumbnails',
	},

	// File size limits (in bytes)
	SIZE_LIMITS: {
		PROFILE_PICTURE: 5 * 1024 * 1024, // 5MB
		POST_MEDIA: 20 * 1024 * 1024, // 20MB
		THUMBNAIL: 1 * 1024 * 1024, // 1MB
	},

	// Supported file types
	ALLOWED_TYPES: {
		IMAGES: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'],
		VIDEOS: ['video/mp4', 'video/webm', 'video/mov', 'video/avi'],
		ALL_MEDIA: [] as string[], // Will be populated below
	},

	// File extensions
	ALLOWED_EXTENSIONS: [
		'.jpg',
		'.jpeg',
		'.png',
		'.webp',
		'.gif',
		'.mp4',
		'.webm',
		'.mov',
		'.avi',
	],

	// Image quality settings
	IMAGE_QUALITY: {
		PROFILE_PICTURE: 85,
		POST_MEDIA: 80,
		THUMBNAIL: 70,
	},

	// Thumbnail sizes
	THUMBNAIL_SIZES: {
		PROFILE_SMALL: { width: 150, height: 150 },
		PROFILE_MEDIUM: { width: 300, height: 300 },
		POST_SMALL: { width: 200, height: 200 },
		POST_MEDIUM: { width: 400, height: 400 },
	},
};

// Populate ALL_MEDIA array
MEDIA_CONFIG.ALLOWED_TYPES.ALL_MEDIA = [
	...MEDIA_CONFIG.ALLOWED_TYPES.IMAGES,
	...MEDIA_CONFIG.ALLOWED_TYPES.VIDEOS,
];

/**
 * Media path generation utilities
 */
export class MediaPathHelper {
	/**
	 * Get the absolute path to the media directory
	 */
	static getMediaRoot(): string {
		return path.join(process.cwd(), 'public', MEDIA_CONFIG.BASE_DIR);
	}

	/**
	 * Generate user base directory path
	 */
	static getUserDir(userId: string): string {
		return path.join(this.getMediaRoot(), `user-${userId}`);
	}

	/**
	 * Generate user profile picture directory path
	 */
	static getUserProfileDir(userId: string): string {
		return path.join(
			this.getUserDir(userId),
			MEDIA_CONFIG.DIRECTORIES.USER_PROFILE
		);
	}

	/**
	 * Generate user posts directory path for a specific POI
	 */
	static getUserPostsDir(userId: string, poiId?: string): string {
		const basePath = path.join(
			this.getUserDir(userId),
			MEDIA_CONFIG.DIRECTORIES.USER_POSTS
		);

		return poiId ? path.join(basePath, `poi-${poiId}`) : basePath;
	}

	/**
	 * Generate POI profile picture directory path
	 */
	static getPOIProfileDir(poiId: string): string {
		return path.join(
			this.getMediaRoot(),
			`poi-${poiId}`,
			MEDIA_CONFIG.DIRECTORIES.POI_PROFILE
		);
	}

	/**
	 * Generate thumbnails directory path
	 */
	static getThumbnailsDir(baseDir: string): string {
		return path.join(baseDir, MEDIA_CONFIG.DIRECTORIES.THUMBNAILS);
	}

	/**
	 * Generate unique filename with timestamp and UUID
	 */
	static generateFileName(originalName: string, prefix?: string): string {
		const ext = path.extname(originalName).toLowerCase();
		const timestamp = Date.now();
		const uuid = uuidv4().split('-')[0]; // Use first part of UUID for shorter names
		const prefixStr = prefix ? `${prefix}-` : '';

		return `${prefixStr}${timestamp}-${uuid}${ext}`;
	}

	/**
	 * Generate thumbnail filename from original filename
	 */
	static generateThumbnailName(originalFileName: string, size: string): string {
		const ext = path.extname(originalFileName);
		const nameWithoutExt = path.basename(originalFileName, ext);
		return `${nameWithoutExt}-thumb-${size}${ext}`;
	}

	/**
	 * Get public URL for media file
	 */
	static getPublicUrl(filePath: string): string {
		// Convert absolute path to relative path from public directory
		const publicDir = path.join(process.cwd(), 'public');
		const relativePath = path.relative(publicDir, filePath);

		// Convert to URL format (forward slashes)
		return '/' + relativePath.replace(/\\/g, '/');
	}

	/**
	 * Get media type from file extension
	 */
	static getMediaType(fileName: string): 'image' | 'video' | 'unknown' {
		const ext = path.extname(fileName).toLowerCase();

		if (['.jpg', '.jpeg', '.png', '.webp', '.gif'].includes(ext)) {
			return 'image';
		}

		if (['.mp4', '.webm', '.mov', '.avi'].includes(ext)) {
			return 'video';
		}

		return 'unknown';
	}
}

/**
 * Directory management utilities
 */
export class DirectoryManager {
	/**
	 * Ensure directory exists, create if it doesn't
	 */
	static async ensureDirectory(dirPath: string): Promise<void> {
		try {
			await fs.promises.access(dirPath);
		} catch {
			await fs.promises.mkdir(dirPath, { recursive: true });
		}
	}

	/**
	 * Create user media directory structure
	 */
	static async createUserDirectories(userId: string): Promise<{
		profileDir: string;
		postsDir: string;
	}> {
		const profileDir = MediaPathHelper.getUserProfileDir(userId);
		const postsDir = MediaPathHelper.getUserPostsDir(userId);

		await this.ensureDirectory(profileDir);
		await this.ensureDirectory(postsDir);

		return { profileDir, postsDir };
	}

	/**
	 * Create POI-specific user posts directory
	 */
	static async createUserPOIPostsDir(
		userId: string,
		poiId: string
	): Promise<string> {
		const poiPostsDir = MediaPathHelper.getUserPostsDir(userId, poiId);
		const thumbnailsDir = MediaPathHelper.getThumbnailsDir(poiPostsDir);

		await this.ensureDirectory(poiPostsDir);
		await this.ensureDirectory(thumbnailsDir);

		return poiPostsDir;
	}

	/**
	 * Create POI profile directory
	 */
	static async createPOIProfileDir(poiId: string): Promise<string> {
		const poiProfileDir = MediaPathHelper.getPOIProfileDir(poiId);
		await this.ensureDirectory(poiProfileDir);
		return poiProfileDir;
	}

	/**
	 * Clean up empty directories
	 */
	static async cleanupEmptyDirectories(dirPath: string): Promise<void> {
		try {
			const files = await fs.promises.readdir(dirPath);

			if (files.length === 0) {
				await fs.promises.rmdir(dirPath);

				// Recursively clean up parent directories if they're empty
				const parentDir = path.dirname(dirPath);
				const mediaRoot = MediaPathHelper.getMediaRoot();

				if (parentDir !== mediaRoot && parentDir !== path.dirname(mediaRoot)) {
					await this.cleanupEmptyDirectories(parentDir);
				}
			}
		} catch {
			// Directory doesn't exist or other error - ignore
		}
	}

	/**
	 * Get all files in a directory (non-recursive)
	 */
	static async getDirectoryFiles(dirPath: string): Promise<string[]> {
		try {
			const files = await fs.promises.readdir(dirPath);
			const filePaths: string[] = [];

			for (const file of files) {
				const filePath = path.join(dirPath, file);
				const stats = await fs.promises.stat(filePath);

				if (stats.isFile()) {
					filePaths.push(filePath);
				}
			}

			return filePaths;
		} catch {
			return [];
		}
	}

	/**
	 * Check if directory contains any files (recursively)
	 */
	static async hasAnyFiles(dirPath: string): Promise<boolean> {
		try {
			const items = await fs.promises.readdir(dirPath);

			for (const item of items) {
				const itemPath = path.join(dirPath, item);
				const stats = await fs.promises.stat(itemPath);

				if (stats.isFile()) {
					return true; // Found a file
				} else if (stats.isDirectory()) {
					// Recursively check subdirectories
					const hasFiles = await this.hasAnyFiles(itemPath);
					if (hasFiles) {
						return true;
					}
				}
			}

			return false; // No files found
		} catch {
			return false; // Directory doesn't exist or can't be read
		}
	}

	/**
	 * Clean up user directory if it's empty (no files, only empty folders)
	 */
	static async cleanupUserDirectoryIfEmpty(userId: string): Promise<{
		cleaned: boolean;
		removedDirectories: string[];
	}> {
		try {
			const userDir = MediaPathHelper.getUserDir(userId);
			const removedDirectories: string[] = [];

			// Check if user directory has any files
			const hasFiles = await this.hasAnyFiles(userDir);

			if (!hasFiles) {
				// No files found, safe to remove the entire user directory
				await this.removeDirectoryRecursive(userDir);
				removedDirectories.push(userDir);

				return {
					cleaned: true,
					removedDirectories,
				};
			}

			return {
				cleaned: false,
				removedDirectories,
			};
		} catch (error) {
			console.warn('Error during user directory cleanup:', error);
			return {
				cleaned: false,
				removedDirectories: [],
			};
		}
	}

	/**
	 * Remove directory and all its contents recursively
	 */
	static async removeDirectoryRecursive(dirPath: string): Promise<void> {
		try {
			const items = await fs.promises.readdir(dirPath);

			for (const item of items) {
				const itemPath = path.join(dirPath, item);
				const stats = await fs.promises.stat(itemPath);

				if (stats.isDirectory()) {
					await this.removeDirectoryRecursive(itemPath);
				} else {
					await fs.promises.unlink(itemPath);
				}
			}

			await fs.promises.rmdir(dirPath);
		} catch {
			// Ignore errors - directory might not exist or already be removed
		}
	}

	/**
	 * Get directory size in bytes
	 */
	static async getDirectorySize(dirPath: string): Promise<number> {
		try {
			const files = await fs.promises.readdir(dirPath);
			let totalSize = 0;

			for (const file of files) {
				const filePath = path.join(dirPath, file);
				const stats = await fs.promises.stat(filePath);

				if (stats.isDirectory()) {
					totalSize += await this.getDirectorySize(filePath);
				} else {
					totalSize += stats.size;
				}
			}

			return totalSize;
		} catch {
			return 0;
		}
	}
}

/**
 * File operation utilities
 */
export class FileOperations {
	/**
	 * Save uploaded file to specified directory
	 */
	static async saveFile(
		file: File,
		targetDir: string,
		fileName?: string
	): Promise<{ filePath: string; fileName: string; publicUrl: string }> {
		await DirectoryManager.ensureDirectory(targetDir);

		const finalFileName =
			fileName || MediaPathHelper.generateFileName(file.name);
		const filePath = path.join(targetDir, finalFileName);

		// Convert File to Buffer
		const arrayBuffer = await file.arrayBuffer();
		const buffer = Buffer.from(arrayBuffer);

		// Write file
		await fs.promises.writeFile(filePath, buffer);

		return {
			filePath,
			fileName: finalFileName,
			publicUrl: MediaPathHelper.getPublicUrl(filePath),
		};
	}

	/**
	 * Delete file and cleanup empty directories
	 */
	static async deleteFile(filePath: string): Promise<void> {
		try {
			await fs.promises.unlink(filePath);

			// Clean up empty parent directories
			const parentDir = path.dirname(filePath);
			await DirectoryManager.cleanupEmptyDirectories(parentDir);
		} catch (error) {
			// File doesn't exist or other error - log but don't throw
			console.warn('Failed to delete file:', filePath, error);
		}
	}

	/**
	 * Move file to new location
	 */
	static async moveFile(sourcePath: string, targetPath: string): Promise<void> {
		await DirectoryManager.ensureDirectory(path.dirname(targetPath));
		await fs.promises.rename(sourcePath, targetPath);
	}

	/**
	 * Copy file to new location
	 */
	static async copyFile(sourcePath: string, targetPath: string): Promise<void> {
		await DirectoryManager.ensureDirectory(path.dirname(targetPath));
		await fs.promises.copyFile(sourcePath, targetPath);
	}

	/**
	 * Check if file exists
	 */
	static async fileExists(filePath: string): Promise<boolean> {
		try {
			await fs.promises.access(filePath);
			return true;
		} catch {
			return false;
		}
	}

	/**
	 * Get file stats
	 */
	static async getFileStats(filePath: string): Promise<fs.Stats | null> {
		try {
			return await fs.promises.stat(filePath);
		} catch {
			return null;
		}
	}
}
