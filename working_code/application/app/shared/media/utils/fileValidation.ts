/**
 * File Validation and Security Utilities
 * Handles file type validation, size limits, security checks, and sanitization
 *
 * @format
 */

import path from 'path';
import { MEDIA_CONFIG } from './mediaUtils';

/**
 * File validation result interface
 */
export interface FileValidationResult {
	isValid: boolean;
	errors: string[];
	warnings: string[];
	fileInfo: {
		name: string;
		size: number;
		type: string;
		extension: string;
		mediaType: 'image' | 'video' | 'unknown';
	};
}

/**
 * File validation options
 */
export interface ValidationOptions {
	maxSize?: number;
	allowedTypes?: string[];
	allowedExtensions?: string[];
	requireImageDimensions?: boolean;
	maxDimensions?: { width: number; height: number };
	minDimensions?: { width: number; height: number };
}

/**
 * File validation and security utilities
 */
export class FileValidator {
	/**
	 * Validate uploaded file for profile picture
	 */
	static validateProfilePicture(file: File): FileValidationResult {
		return this.validateFile(file, {
			maxSize: MEDIA_CONFIG.SIZE_LIMITS.PROFILE_PICTURE,
			allowedTypes: MEDIA_CONFIG.ALLOWED_TYPES.IMAGES,
			allowedExtensions: ['.jpg', '.jpeg', '.png', '.webp'],
			requireImageDimensions: true,
			minDimensions: { width: 100, height: 100 },
			maxDimensions: { width: 4000, height: 4000 },
		});
	}

	/**
	 * Validate uploaded file for post media
	 */
	static validatePostMedia(file: File): FileValidationResult {
		return this.validateFile(file, {
			maxSize: MEDIA_CONFIG.SIZE_LIMITS.POST_MEDIA,
			allowedTypes: MEDIA_CONFIG.ALLOWED_TYPES.ALL_MEDIA,
			allowedExtensions: MEDIA_CONFIG.ALLOWED_EXTENSIONS,
		});
	}

	/**
	 * General file validation
	 */
	static validateFile(
		file: File,
		options: ValidationOptions = {}
	): FileValidationResult {
		const errors: string[] = [];
		const warnings: string[] = [];

		// Extract file information
		const fileInfo = {
			name: file.name,
			size: file.size,
			type: file.type,
			extension: path.extname(file.name).toLowerCase(),
			mediaType: this.getMediaTypeFromFile(file),
		};

		// Validate file name
		if (!this.isValidFileName(file.name)) {
			errors.push('Invalid file name. File name contains unsafe characters.');
		}

		// Validate file size
		const maxSize = options.maxSize || MEDIA_CONFIG.SIZE_LIMITS.POST_MEDIA;
		if (file.size > maxSize) {
			errors.push(
				`File size (${this.formatFileSize(
					file.size
				)}) exceeds maximum allowed size (${this.formatFileSize(maxSize)}).`
			);
		}

		if (file.size === 0) {
			errors.push('File is empty.');
		}

		// Validate file type
		const allowedTypes =
			options.allowedTypes || MEDIA_CONFIG.ALLOWED_TYPES.ALL_MEDIA;
		if (!allowedTypes.includes(file.type)) {
			errors.push(
				`File type '${
					file.type
				}' is not allowed. Allowed types: ${allowedTypes.join(', ')}.`
			);
		}

		// Validate file extension
		const allowedExtensions =
			options.allowedExtensions || MEDIA_CONFIG.ALLOWED_EXTENSIONS;
		if (!allowedExtensions.includes(fileInfo.extension)) {
			errors.push(
				`File extension '${
					fileInfo.extension
				}' is not allowed. Allowed extensions: ${allowedExtensions.join(', ')}.`
			);
		}

		// Check for suspicious file patterns
		const suspiciousPatterns = this.checkSuspiciousPatterns(file.name);
		if (suspiciousPatterns.length > 0) {
			errors.push(
				`File name contains suspicious patterns: ${suspiciousPatterns.join(
					', '
				)}.`
			);
		}

		// Validate MIME type consistency
		if (!this.isMimeTypeConsistent(file.type, fileInfo.extension)) {
			warnings.push(
				'File extension does not match MIME type. This may indicate a renamed file.'
			);
		}

		// Additional validation for images
		if (fileInfo.mediaType === 'image' && options.requireImageDimensions) {
			// Note: Actual dimension validation would require reading the image
			// This is a placeholder for future implementation with image processing library
			warnings.push('Image dimension validation not yet implemented.');
		}

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
			fileInfo,
		};
	}

	/**
	 * Validate file name for security
	 */
	static isValidFileName(fileName: string): boolean {
		// Check for null bytes
		if (fileName.includes('\0')) {
			return false;
		}

		// Check for path traversal attempts
		if (
			fileName.includes('..') ||
			fileName.includes('/') ||
			fileName.includes('\\')
		) {
			return false;
		}

		// Check for reserved names (Windows)
		const reservedNames = [
			'CON',
			'PRN',
			'AUX',
			'NUL',
			'COM1',
			'COM2',
			'COM3',
			'COM4',
			'COM5',
			'COM6',
			'COM7',
			'COM8',
			'COM9',
			'LPT1',
			'LPT2',
			'LPT3',
			'LPT4',
			'LPT5',
			'LPT6',
			'LPT7',
			'LPT8',
			'LPT9',
		];
		const nameWithoutExt = path
			.basename(fileName, path.extname(fileName))
			.toUpperCase();
		if (reservedNames.includes(nameWithoutExt)) {
			return false;
		}

		// Check for invalid characters
		const invalidChars = /[<>:"|?*\x00-\x1f]/;
		if (invalidChars.test(fileName)) {
			return false;
		}

		// Check length
		if (fileName.length > 255) {
			return false;
		}

		return true;
	}

	/**
	 * Check for suspicious file patterns
	 */
	static checkSuspiciousPatterns(fileName: string): string[] {
		const suspicious: string[] = [];

		// Multiple extensions - improved logic to avoid false positives
		// Only flag if there are multiple actual file extensions (not just dots in filename)
		const parts = fileName.split('.');
		if (parts.length > 2) {
			// Check if the second-to-last part looks like a file extension (3-4 chars, all letters)
			const potentialExt = parts[parts.length - 2];
			if (
				potentialExt &&
				potentialExt.length <= 4 &&
				/^[a-zA-Z]+$/.test(potentialExt)
			) {
				suspicious.push('multiple extensions');
			}
		}

		// Executable extensions hidden
		const executableExtensions = [
			'.exe',
			'.bat',
			'.cmd',
			'.com',
			'.scr',
			'.pif',
			'.vbs',
			'.js',
			'.jar',
			'.php',
			'.asp',
			'.jsp',
		];
		for (const ext of executableExtensions) {
			if (fileName.toLowerCase().includes(ext)) {
				suspicious.push('executable extension');
				break;
			}
		}

		// Very long names (potential buffer overflow)
		if (fileName.length > 200) {
			suspicious.push('unusually long filename');
		}

		// Unicode control characters
		if (/[\u0000-\u001f\u007f-\u009f]/.test(fileName)) {
			suspicious.push('control characters');
		}

		return suspicious;
	}

	/**
	 * Check if MIME type is consistent with file extension
	 */
	static isMimeTypeConsistent(mimeType: string, extension: string): boolean {
		const mimeExtensionMap: { [key: string]: string[] } = {
			'image/jpeg': ['.jpg', '.jpeg'],
			'image/png': ['.png'],
			'image/webp': ['.webp'],
			'image/gif': ['.gif'],
			'video/mp4': ['.mp4'],
			'video/webm': ['.webm'],
			'video/quicktime': ['.mov'],
			'video/x-msvideo': ['.avi'],
		};

		const expectedExtensions = mimeExtensionMap[mimeType];
		return expectedExtensions ? expectedExtensions.includes(extension) : false;
	}

	/**
	 * Get media type from file
	 */
	static getMediaTypeFromFile(file: File): 'image' | 'video' | 'unknown' {
		if (file.type.startsWith('image/')) {
			return 'image';
		}
		if (file.type.startsWith('video/')) {
			return 'video';
		}
		return 'unknown';
	}

	/**
	 * Format file size for human reading
	 */
	static formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';

		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));

		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}

	/**
	 * Sanitize file name
	 */
	static sanitizeFileName(fileName: string): string {
		// Remove path components
		let sanitized = path.basename(fileName);

		// Replace invalid characters with underscores
		sanitized = sanitized.replace(/[<>:"|?*\x00-\x1f]/g, '_');

		// Remove multiple dots (except the last one for extension)
		const parts = sanitized.split('.');
		if (parts.length > 2) {
			const extension = parts.pop();
			const nameWithoutExt = parts.join('_');
			sanitized = `${nameWithoutExt}.${extension}`;
		}

		// Limit length
		if (sanitized.length > 200) {
			const extension = path.extname(sanitized);
			const nameWithoutExt = path.basename(sanitized, extension);
			sanitized =
				nameWithoutExt.substring(0, 200 - extension.length) + extension;
		}

		// Ensure it doesn't start or end with dots or spaces
		sanitized = sanitized.replace(/^[.\s]+|[.\s]+$/g, '');

		// If empty after sanitization, generate a default name
		if (!sanitized) {
			sanitized = 'file';
		}

		return sanitized;
	}
}

/**
 * Security utilities for file handling
 */
export class SecurityUtils {
	/**
	 * Generate secure random filename
	 */
	static generateSecureFileName(originalName: string): string {
		const extension = path.extname(originalName).toLowerCase();
		const timestamp = Date.now();
		const random = Math.random().toString(36).substring(2, 15);

		return `${timestamp}_${random}${extension}`;
	}

	/**
	 * Check if file content matches its declared type (basic check)
	 */
	static async validateFileSignature(file: File): Promise<boolean> {
		// Read first few bytes to check file signature
		const buffer = await file.slice(0, 16).arrayBuffer();
		const bytes = new Uint8Array(buffer);

		// Common file signatures
		const signatures: { [key: string]: number[][] } = {
			'image/jpeg': [[0xff, 0xd8, 0xff]],
			'image/png': [[0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a]],
			'image/gif': [
				[0x47, 0x49, 0x46, 0x38, 0x37, 0x61],
				[0x47, 0x49, 0x46, 0x38, 0x39, 0x61],
			],
			'image/webp': [[0x52, 0x49, 0x46, 0x46]], // RIFF header, need to check WEBP at offset 8
			'video/mp4': [
				[0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70],
				[0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70],
			],
		};

		const fileSignatures = signatures[file.type];
		if (!fileSignatures) {
			// Unknown type, allow it for now
			return true;
		}

		return fileSignatures.some((signature) =>
			signature.every((byte, index) => bytes[index] === byte)
		);
	}

	/**
	 * Rate limiting check for file uploads
	 */
	static checkUploadRateLimit(
		_userId: string,
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		_maxUploadsPerHour: number = 50
	): boolean {
		// This would typically use Redis or a database to track upload counts
		// For now, return true (no rate limiting)
		// TODO: Implement proper rate limiting
		return true;
	}

	/**
	 * Check total storage quota for user
	 */
	static async checkStorageQuota(
		_userId: string,
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		_maxStorageBytes: number = 100 * 1024 * 1024
	): Promise<boolean> {
		// This would calculate total storage used by user
		// For now, return true (no quota enforcement)
		// TODO: Implement storage quota checking
		return true;
	}
}
