/**
 * Database Media Service
 * Handles all database operations related to media storage and retrieval
 *
 * @format
 */

import { db } from '@/lib/database';
import { logger } from '@/lib/logger';

/**
 * Media metadata interface
 */
export interface MediaMetadata {
	originalFileName?: string;
	fileSize?: number;
	mimeType?: string;
	width?: number;
	height?: number;
	duration?: number;
	format?: string;
	poiName?: string;
	poiLocation?: string;
	uploadedAt?: string;
	warnings?: string[];
	[key: string]: unknown;
}

/**
 * Media database record interface
 */
export interface MediaRecord {
	id: string;
	poi_id: string;
	user_id: string;
	media_type: 'photo' | 'video';
	media_url: string;
	thumbnail_url?: string;
	caption?: string;
	metadata?: MediaMetadata;
	is_verified: boolean;
	created_at: string;
	updated_at?: string;
}

/**
 * Media with POI information
 */
export interface MediaWithPOI extends MediaRecord {
	poi_name?: string;
	poi_city?: string;
	poi_district?: string;
	poi_category?: string;
	poi_subcategory?: string;
	like_count: number;
	favorite_count: number;
}

/**
 * Media query options
 */
export interface MediaQueryOptions {
	userId?: string;
	poiId?: string;
	mediaType?: 'photo' | 'video';
	isVerified?: boolean;
	limit?: number;
	offset?: number;
	sortBy?: 'created_at' | 'like_count' | 'favorite_count';
	sortOrder?: 'ASC' | 'DESC';
}

/**
 * Database service for media operations
 */
export class MediaDatabaseService {
	/**
	 * Create a new media record
	 */
	static async createMedia(data: {
		poiId: string;
		userId: string;
		mediaType: 'photo' | 'video';
		mediaUrl: string;
		thumbnailUrl?: string;
		caption?: string;
		metadata?: MediaMetadata;
		isVerified?: boolean;
	}): Promise<MediaRecord> {
		try {
			const query = `
        INSERT INTO spatial_schema.poi_media (
          poi_id, user_id, media_type, media_url, thumbnail_url, 
          caption, metadata, is_verified, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
        RETURNING *
      `;

			const values = [
				data.poiId,
				data.userId,
				data.mediaType,
				data.mediaUrl,
				data.thumbnailUrl || null,
				data.caption || null,
				data.metadata ? JSON.stringify(data.metadata) : null,
				data.isVerified || false,
			];

			const result = await db.query(query, values);

			logger.info('Media record created', {
				mediaId: result.rows[0].id,
				userId: data.userId,
				poiId: data.poiId,
			});

			return result.rows[0];
		} catch (error) {
			logger.error('Error creating media record', { error, data });
			throw error;
		}
	}

	/**
	 * Get media records with optional filtering
	 */
	static async getMedia(options: MediaQueryOptions = {}): Promise<{
		media: MediaWithPOI[];
		total: number;
	}> {
		try {
			const {
				userId,
				poiId,
				mediaType,
				isVerified,
				limit = 20,
				offset = 0,
				sortBy = 'created_at',
				sortOrder = 'DESC',
			} = options;

			// Build the main query
			let query = `
        SELECT 
          pm.*,
          p.name as poi_name,
          p.city as poi_city,
          p.district as poi_district,
          p.category as poi_category,
          p.subcategory as poi_subcategory,
          COALESCE(like_counts.like_count, 0) as like_count,
          COALESCE(fav_counts.favorite_count, 0) as favorite_count
        FROM spatial_schema.poi_media pm
        LEFT JOIN spatial_schema.pois p ON pm.poi_id = p.id
        LEFT JOIN (
          SELECT media_id, COUNT(*) as like_count
          FROM spatial_schema.poi_media_likes
          GROUP BY media_id
        ) like_counts ON pm.id = like_counts.media_id
        LEFT JOIN (
          SELECT media_id, COUNT(*) as favorite_count
          FROM spatial_schema.poi_media_favorites
          GROUP BY media_id
        ) fav_counts ON pm.id = fav_counts.media_id
        WHERE 1=1
      `;

			const params: unknown[] = [];
			let paramIndex = 1;

			// Add filters
			if (userId) {
				query += ` AND pm.user_id = $${paramIndex++}`;
				params.push(userId);
			}

			if (poiId) {
				query += ` AND pm.poi_id = $${paramIndex++}`;
				params.push(poiId);
			}

			if (mediaType) {
				query += ` AND pm.media_type = $${paramIndex++}`;
				params.push(mediaType);
			}

			if (isVerified !== undefined) {
				query += ` AND pm.is_verified = $${paramIndex++}`;
				params.push(isVerified);
			}

			// Add sorting
			const validSortColumns = ['created_at', 'like_count', 'favorite_count'];
			const validSortOrders = ['ASC', 'DESC'];

			if (
				validSortColumns.includes(sortBy) &&
				validSortOrders.includes(sortOrder)
			) {
				if (sortBy === 'like_count' || sortBy === 'favorite_count') {
					query += ` ORDER BY ${sortBy} ${sortOrder}, pm.created_at DESC`;
				} else {
					query += ` ORDER BY pm.${sortBy} ${sortOrder}`;
				}
			} else {
				query += ` ORDER BY pm.created_at DESC`;
			}

			// Add pagination
			query += ` LIMIT $${paramIndex++} OFFSET $${paramIndex++}`;
			params.push(limit, offset);

			// Execute main query
			const result = await db.query(query, params);

			// Get total count
			let countQuery = `
        SELECT COUNT(*) as total
        FROM spatial_schema.poi_media pm
        WHERE 1=1
      `;
			const countParams: unknown[] = [];
			let countParamIndex = 1;

			if (userId) {
				countQuery += ` AND pm.user_id = $${countParamIndex++}`;
				countParams.push(userId);
			}

			if (poiId) {
				countQuery += ` AND pm.poi_id = $${countParamIndex++}`;
				countParams.push(poiId);
			}

			if (mediaType) {
				countQuery += ` AND pm.media_type = $${countParamIndex++}`;
				countParams.push(mediaType);
			}

			if (isVerified !== undefined) {
				countQuery += ` AND pm.is_verified = $${countParamIndex++}`;
				countParams.push(isVerified);
			}

			const countResult = await db.query(countQuery, countParams);
			const total = parseInt(countResult.rows[0]?.total || '0');

			logger.info('Retrieved media records', {
				count: result.rows.length,
				total,
				options,
			});

			return {
				media: result.rows,
				total,
			};
		} catch (error) {
			logger.error('Error retrieving media records', { error, options });
			throw error;
		}
	}

	/**
	 * Get a single media record by ID
	 */
	static async getMediaById(
		mediaId: string,
		userId?: string
	): Promise<MediaWithPOI | null> {
		try {
			let query = `
        SELECT 
          pm.*,
          p.name as poi_name,
          p.city as poi_city,
          p.district as poi_district,
          p.category as poi_category,
          p.subcategory as poi_subcategory,
          COALESCE(like_counts.like_count, 0) as like_count,
          COALESCE(fav_counts.favorite_count, 0) as favorite_count
        FROM spatial_schema.poi_media pm
        LEFT JOIN spatial_schema.pois p ON pm.poi_id = p.id
        LEFT JOIN (
          SELECT media_id, COUNT(*) as like_count
          FROM spatial_schema.poi_media_likes
          GROUP BY media_id
        ) like_counts ON pm.id = like_counts.media_id
        LEFT JOIN (
          SELECT media_id, COUNT(*) as favorite_count
          FROM spatial_schema.poi_media_favorites
          GROUP BY media_id
        ) fav_counts ON pm.id = fav_counts.media_id
        WHERE pm.id = $1
      `;

			const params = [mediaId];

			if (userId) {
				query += ` AND pm.user_id = $2`;
				params.push(userId);
			}

			const result = await db.query(query, params);
			return result.rows[0] || null;
		} catch (error) {
			logger.error('Error retrieving media by ID', { error, mediaId, userId });
			throw error;
		}
	}

	/**
	 * Update media record
	 */
	static async updateMedia(
		mediaId: string,
		userId: string,
		updates: {
			caption?: string;
			metadata?: MediaMetadata;
			isVerified?: boolean;
		}
	): Promise<MediaRecord | null> {
		try {
			const updateFields: string[] = [];
			const params: unknown[] = [];
			let paramIndex = 1;

			if (updates.caption !== undefined) {
				updateFields.push(`caption = $${paramIndex++}`);
				params.push(updates.caption);
			}

			if (updates.metadata !== undefined) {
				updateFields.push(`metadata = $${paramIndex++}`);
				params.push(JSON.stringify(updates.metadata));
			}

			if (updates.isVerified !== undefined) {
				updateFields.push(`is_verified = $${paramIndex++}`);
				params.push(updates.isVerified);
			}

			if (updateFields.length === 0) {
				throw new Error('No fields to update');
			}

			updateFields.push(`updated_at = NOW()`);

			const query = `
        UPDATE spatial_schema.poi_media 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex++} AND user_id = $${paramIndex++}
        RETURNING *
      `;

			params.push(mediaId, userId);

			const result = await db.query(query, params);

			if (result.rows.length === 0) {
				return null;
			}

			logger.info('Media record updated', {
				mediaId,
				userId,
				updates,
			});

			return result.rows[0];
		} catch (error) {
			logger.error('Error updating media record', {
				error,
				mediaId,
				userId,
				updates,
			});
			throw error;
		}
	}

	/**
	 * Delete media record
	 */
	static async deleteMedia(mediaId: string, userId: string): Promise<boolean> {
		try {
			const query = `
        DELETE FROM spatial_schema.poi_media 
        WHERE id = $1 AND user_id = $2
        RETURNING id
      `;

			const result = await db.query(query, [mediaId, userId]);

			if (result.rows.length === 0) {
				return false;
			}

			logger.info('Media record deleted', {
				mediaId,
				userId,
			});

			return true;
		} catch (error) {
			logger.error('Error deleting media record', { error, mediaId, userId });
			throw error;
		}
	}

	/**
	 * Get user's media statistics
	 */
	static async getUserMediaStats(userId: string): Promise<{
		totalMedia: number;
		photoCount: number;
		videoCount: number;
		totalLikes: number;
		totalFavorites: number;
		verifiedCount: number;
	}> {
		try {
			const query = `
        SELECT 
          COUNT(*) as total_media,
          COUNT(CASE WHEN media_type = 'photo' THEN 1 END) as photo_count,
          COUNT(CASE WHEN media_type = 'video' THEN 1 END) as video_count,
          COUNT(CASE WHEN is_verified = true THEN 1 END) as verified_count,
          COALESCE(SUM(like_counts.like_count), 0) as total_likes,
          COALESCE(SUM(fav_counts.favorite_count), 0) as total_favorites
        FROM spatial_schema.poi_media pm
        LEFT JOIN (
          SELECT media_id, COUNT(*) as like_count
          FROM spatial_schema.poi_media_likes
          GROUP BY media_id
        ) like_counts ON pm.id = like_counts.media_id
        LEFT JOIN (
          SELECT media_id, COUNT(*) as favorite_count
          FROM spatial_schema.poi_media_favorites
          GROUP BY media_id
        ) fav_counts ON pm.id = fav_counts.media_id
        WHERE pm.user_id = $1
      `;

			const result = await db.query(query, [userId]);
			const stats = result.rows[0];

			return {
				totalMedia: parseInt(stats.total_media),
				photoCount: parseInt(stats.photo_count),
				videoCount: parseInt(stats.video_count),
				totalLikes: parseInt(stats.total_likes),
				totalFavorites: parseInt(stats.total_favorites),
				verifiedCount: parseInt(stats.verified_count),
			};
		} catch (error) {
			logger.error('Error getting user media stats', { error, userId });
			throw error;
		}
	}

	/**
	 * Get POI's media statistics
	 */
	static async getPOIMediaStats(poiId: string): Promise<{
		totalMedia: number;
		photoCount: number;
		videoCount: number;
		uniqueContributors: number;
		verifiedCount: number;
	}> {
		try {
			const query = `
        SELECT 
          COUNT(*) as total_media,
          COUNT(CASE WHEN media_type = 'photo' THEN 1 END) as photo_count,
          COUNT(CASE WHEN media_type = 'video' THEN 1 END) as video_count,
          COUNT(DISTINCT user_id) as unique_contributors,
          COUNT(CASE WHEN is_verified = true THEN 1 END) as verified_count
        FROM spatial_schema.poi_media
        WHERE poi_id = $1
      `;

			const result = await db.query(query, [poiId]);
			const stats = result.rows[0];

			return {
				totalMedia: parseInt(stats.total_media),
				photoCount: parseInt(stats.photo_count),
				videoCount: parseInt(stats.video_count),
				uniqueContributors: parseInt(stats.unique_contributors),
				verifiedCount: parseInt(stats.verified_count),
			};
		} catch (error) {
			logger.error('Error getting POI media stats', { error, poiId });
			throw error;
		}
	}
}
