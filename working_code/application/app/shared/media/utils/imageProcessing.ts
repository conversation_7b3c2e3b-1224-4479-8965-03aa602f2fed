/**
 * Image Processing Utilities
 * Handles image resizing, thumbnail generation, and optimization using Sharp
 */

import sharp from 'sharp'
import path from 'path'
import { MEDIA_CONFIG, MediaPathHelper } from './mediaUtils'

/**
 * Image processing options
 */
export interface ImageProcessingOptions {
  width?: number
  height?: number
  quality?: number
  format?: 'jpeg' | 'png' | 'webp'
  fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside'
  background?: string
  withoutEnlargement?: boolean
}

/**
 * Thumbnail generation options
 */
export interface ThumbnailOptions {
  sizes: Array<{
    name: string
    width: number
    height: number
    quality?: number
  }>
  format?: 'jpeg' | 'png' | 'webp'
  fit?: 'cover' | 'contain'
}

/**
 * Image processing result
 */
export interface ProcessingResult {
  success: boolean
  outputPath: string
  publicUrl: string
  metadata: {
    width: number
    height: number
    format: string
    size: number
  }
  error?: string
}

/**
 * Image processing utilities using Sharp
 */
export class ImageProcessor {
  /**
   * Process profile picture - resize and optimize
   */
  static async processProfilePicture(
    inputPath: string,
    outputDir: string,
    fileName: string
  ): Promise<ProcessingResult> {
    try {
      const outputPath = path.join(outputDir, fileName)
      
      const processed = await sharp(inputPath)
        .resize(
          MEDIA_CONFIG.THUMBNAIL_SIZES.PROFILE_MEDIUM.width,
          MEDIA_CONFIG.THUMBNAIL_SIZES.PROFILE_MEDIUM.height,
          {
            fit: 'cover',
            position: 'center'
          }
        )
        .jpeg({
          quality: MEDIA_CONFIG.IMAGE_QUALITY.PROFILE_PICTURE,
          progressive: true
        })
        .toFile(outputPath)

      return {
        success: true,
        outputPath,
        publicUrl: MediaPathHelper.getPublicUrl(outputPath),
        metadata: {
          width: processed.width,
          height: processed.height,
          format: 'jpeg',
          size: processed.size
        }
      }
    } catch (error) {
      return {
        success: false,
        outputPath: '',
        publicUrl: '',
        metadata: { width: 0, height: 0, format: '', size: 0 },
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Process post media image - resize and optimize
   */
  static async processPostImage(
    inputPath: string,
    outputDir: string,
    fileName: string,
    options: ImageProcessingOptions = {}
  ): Promise<ProcessingResult> {
    try {
      const outputPath = path.join(outputDir, fileName)
      
      let processor = sharp(inputPath)

      // Apply resizing if specified
      if (options.width || options.height) {
        processor = processor.resize(options.width, options.height, {
          fit: options.fit || 'inside',
          withoutEnlargement: options.withoutEnlargement !== false,
          background: options.background || { r: 255, g: 255, b: 255, alpha: 1 }
        })
      }

      // Apply format and quality
      const format = options.format || 'jpeg'
      const quality = options.quality || MEDIA_CONFIG.IMAGE_QUALITY.POST_MEDIA

      switch (format) {
        case 'jpeg':
          processor = processor.jpeg({ quality, progressive: true })
          break
        case 'png':
          processor = processor.png({ quality, progressive: true })
          break
        case 'webp':
          processor = processor.webp({ quality })
          break
      }

      const processed = await processor.toFile(outputPath)

      return {
        success: true,
        outputPath,
        publicUrl: MediaPathHelper.getPublicUrl(outputPath),
        metadata: {
          width: processed.width,
          height: processed.height,
          format,
          size: processed.size
        }
      }
    } catch (error) {
      return {
        success: false,
        outputPath: '',
        publicUrl: '',
        metadata: { width: 0, height: 0, format: '', size: 0 },
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Generate thumbnails for an image
   */
  static async generateThumbnails(
    inputPath: string,
    outputDir: string,
    baseFileName: string,
    options: ThumbnailOptions
  ): Promise<Array<ProcessingResult>> {
    const results: ProcessingResult[] = []
    
    for (const size of options.sizes) {
      try {
        const thumbnailName = MediaPathHelper.generateThumbnailName(baseFileName, size.name)
        const outputPath = path.join(outputDir, thumbnailName)
        
        const processed = await sharp(inputPath)
          .resize(size.width, size.height, {
            fit: options.fit || 'cover',
            position: 'center'
          })
          .jpeg({
            quality: size.quality || MEDIA_CONFIG.IMAGE_QUALITY.THUMBNAIL,
            progressive: true
          })
          .toFile(outputPath)

        results.push({
          success: true,
          outputPath,
          publicUrl: MediaPathHelper.getPublicUrl(outputPath),
          metadata: {
            width: processed.width,
            height: processed.height,
            format: 'jpeg',
            size: processed.size
          }
        })
      } catch (error) {
        results.push({
          success: false,
          outputPath: '',
          publicUrl: '',
          metadata: { width: 0, height: 0, format: '', size: 0 },
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
    
    return results
  }

  /**
   * Generate standard thumbnails for post images
   */
  static async generatePostThumbnails(
    inputPath: string,
    outputDir: string,
    baseFileName: string
  ): Promise<Array<ProcessingResult>> {
    return this.generateThumbnails(inputPath, outputDir, baseFileName, {
      sizes: [
        {
          name: 'small',
          width: MEDIA_CONFIG.THUMBNAIL_SIZES.POST_SMALL.width,
          height: MEDIA_CONFIG.THUMBNAIL_SIZES.POST_SMALL.height
        },
        {
          name: 'medium',
          width: MEDIA_CONFIG.THUMBNAIL_SIZES.POST_MEDIUM.width,
          height: MEDIA_CONFIG.THUMBNAIL_SIZES.POST_MEDIUM.height
        }
      ],
      fit: 'cover'
    })
  }

  /**
   * Generate profile picture thumbnails
   */
  static async generateProfileThumbnails(
    inputPath: string,
    outputDir: string,
    baseFileName: string
  ): Promise<Array<ProcessingResult>> {
    return this.generateThumbnails(inputPath, outputDir, baseFileName, {
      sizes: [
        {
          name: 'small',
          width: MEDIA_CONFIG.THUMBNAIL_SIZES.PROFILE_SMALL.width,
          height: MEDIA_CONFIG.THUMBNAIL_SIZES.PROFILE_SMALL.height
        },
        {
          name: 'medium',
          width: MEDIA_CONFIG.THUMBNAIL_SIZES.PROFILE_MEDIUM.width,
          height: MEDIA_CONFIG.THUMBNAIL_SIZES.PROFILE_MEDIUM.height
        }
      ],
      fit: 'cover'
    })
  }

  /**
   * Get image metadata without processing
   */
  static async getImageMetadata(inputPath: string): Promise<{
    width: number
    height: number
    format: string
    size: number
    hasAlpha: boolean
    density?: number
  } | null> {
    try {
      const metadata = await sharp(inputPath).metadata()
      
      return {
        width: metadata.width || 0,
        height: metadata.height || 0,
        format: metadata.format || 'unknown',
        size: metadata.size || 0,
        hasAlpha: metadata.hasAlpha || false,
        density: metadata.density
      }
    } catch (error) {
      console.error('Error getting image metadata:', error)
      return null
    }
  }

  /**
   * Optimize image without resizing
   */
  static async optimizeImage(
    inputPath: string,
    outputPath: string,
    quality: number = 80
  ): Promise<ProcessingResult> {
    try {
      const processed = await sharp(inputPath)
        .jpeg({
          quality,
          progressive: true,
          mozjpeg: true
        })
        .toFile(outputPath)

      return {
        success: true,
        outputPath,
        publicUrl: MediaPathHelper.getPublicUrl(outputPath),
        metadata: {
          width: processed.width,
          height: processed.height,
          format: 'jpeg',
          size: processed.size
        }
      }
    } catch (error) {
      return {
        success: false,
        outputPath: '',
        publicUrl: '',
        metadata: { width: 0, height: 0, format: '', size: 0 },
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Convert image to WebP format
   */
  static async convertToWebP(
    inputPath: string,
    outputPath: string,
    quality: number = 80
  ): Promise<ProcessingResult> {
    try {
      const processed = await sharp(inputPath)
        .webp({
          quality,
          effort: 6
        })
        .toFile(outputPath)

      return {
        success: true,
        outputPath,
        publicUrl: MediaPathHelper.getPublicUrl(outputPath),
        metadata: {
          width: processed.width,
          height: processed.height,
          format: 'webp',
          size: processed.size
        }
      }
    } catch (error) {
      return {
        success: false,
        outputPath: '',
        publicUrl: '',
        metadata: { width: 0, height: 0, format: '', size: 0 },
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Create a blurred placeholder image
   */
  static async createBlurredPlaceholder(
    inputPath: string,
    outputPath: string,
    width: number = 20,
    height: number = 20
  ): Promise<ProcessingResult> {
    try {
      const processed = await sharp(inputPath)
        .resize(width, height, { fit: 'cover' })
        .blur(2)
        .jpeg({ quality: 50 })
        .toFile(outputPath)

      return {
        success: true,
        outputPath,
        publicUrl: MediaPathHelper.getPublicUrl(outputPath),
        metadata: {
          width: processed.width,
          height: processed.height,
          format: 'jpeg',
          size: processed.size
        }
      }
    } catch (error) {
      return {
        success: false,
        outputPath: '',
        publicUrl: '',
        metadata: { width: 0, height: 0, format: '', size: 0 },
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}
