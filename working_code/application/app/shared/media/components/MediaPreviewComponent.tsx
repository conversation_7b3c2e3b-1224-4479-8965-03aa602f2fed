/** @format */

'use client';

import React, { useState } from 'react';
import {
	FiCalendar,
	FiEdit3,
	FiHeart,
	FiMapPin,
	FiMessageCircle,
	FiMoreHorizontal,
	FiShare2,
	FiTrash2,
} from 'react-icons/fi';

// Import MediaItem from shared types instead of defining locally
import { MediaItem } from '../types';

/**
 * Media preview component props
 */
interface MediaPreviewComponentProps {
	media: MediaItem;
	showPOIInfo?: boolean;
	showActions?: boolean;
	onLike?: (mediaId: string) => void;
	onComment?: (mediaId: string) => void;
	onShare?: (mediaId: string) => void;
	onEdit?: (mediaId: string) => void;
	onDelete?: (mediaId: string) => void;
	className?: string;
}

/**
 * Media preview component for displaying individual media items
 */
export const MediaPreviewComponent: React.FC<MediaPreviewComponentProps> = ({
	media,
	showPOIInfo = true,
	showActions = true,
	onLike,
	onComment,
	onShare,
	onEdit,
	onDelete,
	className = '',
}) => {
	const [isLiked, setIsLiked] = useState(false);
	const [showMenu, setShowMenu] = useState(false);
	const [isLoading, setIsLoading] = useState(false);

	/**
	 * Handle like action
	 */
	const handleLike = async () => {
		if (isLoading) return;

		setIsLoading(true);
		try {
			setIsLiked(!isLiked);
			onLike?.(media.id);
		} catch {
			setIsLiked(isLiked); // Revert on error
		} finally {
			setIsLoading(false);
		}
	};

	/**
	 * Format date
	 */
	const formatDate = (dateString: string): string => {
		const date = new Date(dateString);
		const now = new Date();
		const diffInHours = Math.floor(
			(now.getTime() - date.getTime()) / (1000 * 60 * 60)
		);

		if (diffInHours < 1) {
			return 'Just now';
		} else if (diffInHours < 24) {
			return `${diffInHours}h ago`;
		} else if (diffInHours < 24 * 7) {
			const days = Math.floor(diffInHours / 24);
			return `${days}d ago`;
		} else {
			return date.toLocaleDateString();
		}
	};

	/**
	 * Get POI location string
	 */
	const getPOILocation = (): string => {
		const parts = [media.poi_district, media.poi_city].filter(Boolean);
		return parts.join(', ');
	};

	// Helper functions to handle both camelCase and snake_case properties
	const getMediaUrl = () => media.mediaUrl || media.media_url || '';
	const getThumbnailUrl = () => media.thumbnailUrl || media.thumbnail_url;
	const getMediaType = () => media.mediaType || media.media_type || '';
	const getLikeCount = () => media.likeCount || media.like_count || 0;
	const getFavoriteCount = () =>
		media.favoriteCount || media.favorite_count || 0;
	const getCreatedAt = () => media.createdAt || media.created_at || '';

	return (
		<div
			className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden ${className}`}>
			{/* Media Content */}
			<div className='relative'>
				{getMediaType() === 'photo' ? (
					<img
						src={getThumbnailUrl() || getMediaUrl()}
						alt={media.caption || 'Media'}
						className='w-full h-64 object-cover'
						loading='lazy'
					/>
				) : (
					<div className='relative'>
						<video
							src={getMediaUrl()}
							className='w-full h-64 object-cover'
							controls
							preload='metadata'
						/>
						<div className='absolute top-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs'>
							Video
						</div>
					</div>
				)}

				{/* Verified Badge */}
				{media.is_verified && (
					<div className='absolute top-2 right-2 bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-medium'>
						Verified
					</div>
				)}
			</div>

			{/* Content */}
			<div className='p-4'>
				{/* POI Information */}
				{showPOIInfo && media.poi_name && (
					<div className='mb-3'>
						<div className='flex items-center text-sm text-gray-600'>
							<FiMapPin className='h-4 w-4 mr-1' />
							<span className='font-medium'>{media.poi_name}</span>
							{getPOILocation() && (
								<span className='ml-1'>• {getPOILocation()}</span>
							)}
						</div>
						{(media.poi_category || media.poi_subcategory) && (
							<div className='text-xs text-gray-500 mt-1'>
								{[media.poi_category, media.poi_subcategory]
									.filter(Boolean)
									.join(' • ')}
							</div>
						)}
					</div>
				)}

				{/* Caption */}
				{media.caption && (
					<div className='mb-3'>
						<p className='text-gray-900 text-sm leading-relaxed'>
							{String(media.caption)}
						</p>
					</div>
				)}

				{/* Metadata */}
				<div className='flex items-center justify-between text-xs text-gray-500 mb-3'>
					<div className='flex items-center'>
						<FiCalendar className='h-3 w-3 mr-1' />
						<span>{formatDate(String(getCreatedAt()))}</span>
					</div>

					{typeof media.metadata?.fileSize === 'number' && (
						<span>
							{(media.metadata.fileSize / (1024 * 1024)).toFixed(1)}MB
						</span>
					)}
				</div>

				{/* Actions */}
				{showActions && (
					<div className='flex items-center justify-between pt-3 border-t border-gray-100'>
						{/* Left Actions */}
						<div className='flex items-center space-x-4'>
							{/* Like Button */}
							<button
								onClick={handleLike}
								disabled={isLoading}
								className={`flex items-center space-x-1 text-sm transition-colors ${
									isLiked ? 'text-red-500' : 'text-gray-500 hover:text-red-500'
								}`}>
								<FiHeart
									className={`h-4 w-4 ${isLiked ? 'fill-current' : ''}`}
								/>
								<span>{getLikeCount() + (isLiked ? 1 : 0)}</span>
							</button>

							{/* Comment Button */}
							<button
								onClick={() => onComment?.(media.id)}
								className='flex items-center space-x-1 text-sm text-gray-500 hover:text-blue-500 transition-colors'>
								<FiMessageCircle className='h-4 w-4' />
								<span>Comment</span>
							</button>

							{/* Share Button */}
							<button
								onClick={() => onShare?.(media.id)}
								className='flex items-center space-x-1 text-sm text-gray-500 hover:text-green-500 transition-colors'>
								<FiShare2 className='h-4 w-4' />
								<span>Share</span>
							</button>
						</div>

						{/* Right Actions */}
						<div className='relative'>
							<button
								onClick={() => setShowMenu(!showMenu)}
								className='text-gray-500 hover:text-gray-700 transition-colors'>
								<FiMoreHorizontal className='h-4 w-4' />
							</button>

							{/* Dropdown Menu */}
							{showMenu && (
								<div className='absolute right-0 top-6 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10 min-w-[120px]'>
									<button
										onClick={() => {
											onEdit?.(media.id);
											setShowMenu(false);
										}}
										className='w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center'>
										<FiEdit3 className='h-4 w-4 mr-2' />
										Edit
									</button>
									<button
										onClick={() => {
											onDelete?.(media.id);
											setShowMenu(false);
										}}
										className='w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center'>
										<FiTrash2 className='h-4 w-4 mr-2' />
										Delete
									</button>
								</div>
							)}
						</div>
					</div>
				)}

				{/* Stats */}
				{(getLikeCount() > 0 || getFavoriteCount() > 0) && (
					<div className='flex items-center justify-between text-xs text-gray-500 mt-2 pt-2 border-t border-gray-100'>
						<span>{getLikeCount()} likes</span>
						{getFavoriteCount() > 0 && (
							<span>{getFavoriteCount()} favorites</span>
						)}
					</div>
				)}
			</div>
		</div>
	);
};

/**
 * Media grid component for displaying multiple media items
 */
interface MediaGridProps {
	mediaItems: MediaItem[];
	columns?: number;
	showPOIInfo?: boolean;
	showActions?: boolean;
	onLike?: (mediaId: string) => void;
	onComment?: (mediaId: string) => void;
	onShare?: (mediaId: string) => void;
	onEdit?: (mediaId: string) => void;
	onDelete?: (mediaId: string) => void;
	className?: string;
}

export const MediaGrid: React.FC<MediaGridProps> = ({
	mediaItems,
	columns = 3,
	showPOIInfo = true,
	showActions = true,
	onLike,
	onComment,
	onShare,
	onEdit,
	onDelete,
	className = '',
}) => {
	const gridCols = {
		1: 'grid-cols-1',
		2: 'grid-cols-1 md:grid-cols-2',
		3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
		4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
	};

	return (
		<div
			className={`grid gap-6 ${
				gridCols[columns as keyof typeof gridCols]
			} ${className}`}>
			{mediaItems.map((media) => (
				<MediaPreviewComponent
					key={media.id}
					media={media}
					showPOIInfo={showPOIInfo}
					showActions={showActions}
					onLike={onLike}
					onComment={onComment}
					onShare={onShare}
					onEdit={onEdit}
					onDelete={onDelete}
				/>
			))}
		</div>
	);
};

/**
 * Empty state component
 */
interface EmptyMediaStateProps {
	title?: string;
	description?: string;
	actionText?: string;
	onAction?: () => void;
	className?: string;
}

export const EmptyMediaState: React.FC<EmptyMediaStateProps> = ({
	title = 'No media yet',
	description = 'Upload your first photo or video to get started',
	actionText = 'Upload Media',
	onAction,
	className = '',
}) => {
	return (
		<div className={`text-center py-12 ${className}`}>
			<div className='mx-auto h-24 w-24 text-gray-300 mb-4'>
				<svg
					fill='currentColor'
					viewBox='0 0 24 24'>
					<path d='M4 4h16v16H4V4zm2 2v12h12V6H6zm2 2h8v8H8V8zm2 2v4h4v-4h-4z' />
				</svg>
			</div>
			<h3 className='text-lg font-medium text-gray-900 mb-2'>{title}</h3>
			<p className='text-gray-500 mb-6'>{description}</p>
			{onAction && (
				<button
					onClick={onAction}
					className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700'>
					{actionText}
				</button>
			)}
		</div>
	);
};
