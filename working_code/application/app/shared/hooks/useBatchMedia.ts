/** @format */

import { useState, useCallback } from 'react';

interface POI {
	poi_id: string;
	poi_type?: string;
}

interface MediaItem {
	id: string;
	poi_id: string;
	file_path: string;
	file_type: string;
	caption?: string;
	like_count: number;
	favorite_count: number;
	// ... other media properties
}

interface UseBatchMediaReturn {
	mediaData: Record<string, MediaItem[]>;
	loading: boolean;
	error: string | null;
	loadMedia: (pois: POI[]) => Promise<void>;
	getMediaForPoi: (poiId: string) => MediaItem[];
}

export function useBatchMedia(): UseBatchMediaReturn {
	const [mediaData, setMediaData] = useState<Record<string, MediaItem[]>>({});
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const loadMedia = useCallback(async (pois: POI[]) => {
		if (!pois || pois.length === 0) return;

		setLoading(true);
		setError(null);

		try {
			const response = await fetch('/api/pois/media/batch', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ pois }),
			});

			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const data = await response.json();

			if (data.success) {
				setMediaData(prev => ({
					...prev,
					...data.media,
				}));
			} else {
				throw new Error(data.error || 'Failed to load media');
			}
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : 'Unknown error';
			setError(errorMessage);
			console.error('Error loading batch media:', err);
		} finally {
			setLoading(false);
		}
	}, []);

	const getMediaForPoi = useCallback((poiId: string): MediaItem[] => {
		return mediaData[poiId] || [];
	}, [mediaData]);

	return {
		mediaData,
		loading,
		error,
		loadMedia,
		getMediaForPoi,
	};
}
