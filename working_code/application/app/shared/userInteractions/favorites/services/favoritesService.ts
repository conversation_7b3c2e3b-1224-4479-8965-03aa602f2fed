/** @format */

// Unified Favorites/Saves service for API interactions
// Handles both legacy saves API (/favorite) and new favorites API (/interactions)

import {
	FavoriteResponse,
	POIFavoriteInteraction,
} from '@/app/shared/userInteractions/favorites/types';
import { BaseInteractionService } from '@/app/shared/userInteractions/shared/services';
import { POIIdentifier } from '@/app/shared/userInteractions/shared/types';
import { InteractionListResponse } from '@/app/shared/userInteractions/shared/types/base';

export class FavoritesService extends BaseInteractionService {
	// === NEW FAVORITES API (/interactions) ===

	// Add a favorite to a POI (simplified like likes)
	static async addFavorite(poi: POIIdentifier): Promise<FavoriteResponse> {
		return this.updateInteraction(poi, 'favorite', 'add');
	}

	// Remove a favorite from a POI (simplified like likes)
	static async removeFavorite(poi: POIIdentifier): Promise<FavoriteResponse> {
		return this.updateInteraction(poi, 'favorite', 'remove');
	}

	// Toggle favorite status
	static async toggleFavorite(
		poi: POIIdentifier,
		currentlyFavorited: boolean
	): Promise<FavoriteResponse> {
		return currentlyFavorited
			? this.removeFavorite(poi)
			: this.addFavorite(poi);
	}

	// Get user's favorite status for a POI (like likes service)
	static async getUserFavoriteStatus(
		poi: POIIdentifier,
		userId: string
	): Promise<{
		success: boolean;
		isFavorited: boolean;
		favoriteCount: number;
	}> {
		try {
			// Get user's favorite status
			const userResponse = await this.getUserInteractions(
				poi,
				userId,
				'favorite'
			);

			if (!userResponse.success) {
				// If it's an authentication error, return default values instead of throwing
				if (
					userResponse.error?.includes('Authentication required') ||
					userResponse.error?.includes('Access denied')
				) {
					// Still try to get the public favorite count
					const countResponse = await this.getPOIFavoriteCount(poi);
					const favoriteCount = countResponse.success
						? countResponse.favoriteCount
						: 0;

					return {
						success: true,
						isFavorited: false,
						favoriteCount,
					};
				}
				throw new Error(userResponse.error || 'Failed to get favorite status');
			}

			// Check if user has favorited this POI
			const userFavorite = userResponse.interactions?.find(
				(interaction) =>
					interaction.user_id === userId &&
					interaction.interaction_type === 'favorite'
			);

			// Get total favorite count
			const countResponse = await this.getPOIFavoriteCount(poi);
			const favoriteCount = countResponse.success
				? countResponse.favoriteCount
				: 0;

			return {
				success: true,
				isFavorited: !!userFavorite,
				favoriteCount,
			};
		} catch (error) {
			// Handle HTTP errors gracefully
			const errorMessage = super.handleApiError(error);
			if (
				errorMessage.includes('401') ||
				errorMessage.includes('403') ||
				errorMessage.includes('Authentication required')
			) {
				// Still try to get the public favorite count
				try {
					const countResponse = await this.getPOIFavoriteCount(poi);
					const favoriteCount = countResponse.success
						? countResponse.favoriteCount
						: 0;

					return {
						success: true,
						isFavorited: false,
						favoriteCount,
					};
				} catch {
					return {
						success: true,
						isFavorited: false,
						favoriteCount: 0,
					};
				}
			}
			throw new Error(errorMessage);
		}
	}

	// Get public favorite count for a POI (no authentication required)
	static async getPOIFavoriteCount(poi: POIIdentifier): Promise<{
		success: boolean;
		favoriteCount: number;
	}> {
		try {
			const response = await this.getPOIFavorites(poi, { limit: 1, offset: 0 }); // Just get count, not actual data

			if (!response.success) {
				throw new Error(response.error || 'Failed to get favorite count');
			}

			return {
				success: true,
				favoriteCount: response.total_count || 0,
			};
		} catch (error) {
			throw new Error(super.handleApiError(error));
		}
	}

	// === UNIFIED METHODS (use new interactions API) ===

	// Unified toggle method - uses new interactions API
	static async toggle(
		poi: POIIdentifier,
		currentlySaved: boolean,
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		_notes?: string // Notes not supported in unified API yet
	): Promise<FavoriteResponse> {
		return this.toggleFavorite(poi, currentlySaved);
	}

	// Unified add method - uses new interactions API
	static async add(
		poi: POIIdentifier,
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		_notes?: string // Notes not supported in unified API yet
	): Promise<FavoriteResponse> {
		return this.addFavorite(poi);
	}

	// Unified remove method - uses new interactions API
	static async remove(poi: POIIdentifier): Promise<FavoriteResponse> {
		return this.removeFavorite(poi);
	}

	// Get favorites for a POI
	static async getPOIFavorites(
		poi: POIIdentifier,
		options: {
			limit?: number;
			offset?: number;
			sortBy?: 'created_at' | 'last_interaction_at' | 'priority';
			sortOrder?: 'asc' | 'desc';
			favorite_type?: 'location' | 'bookmark' | 'wishlist';
		} = {}
	): Promise<InteractionListResponse> {
		const params = {
			...this.buildPOIParams(poi),
			interactionType: 'favorite',
			limit: options.limit || 20,
			offset: options.offset || 0,
			sortBy: options.sortBy || 'created_at',
			sortOrder: options.sortOrder || 'desc',
			...(options.favorite_type && { favorite_type: options.favorite_type }),
		};

		try {
			const response = await this.get<InteractionListResponse>(
				'/interactions',
				params
			);
			// Filter for only favorite interactions and cast
			return {
				...response,
				interactions: (response.interactions || []).filter(
					(i) => i.interaction_type === 'favorite'
				) as POIFavoriteInteraction[],
			};
		} catch (error) {
			return {
				success: false,
				interactions: [],
				total_count: 0,
				has_more: false,
				error: super.handleApiError(error),
			};
		}
	}

	// Get user's favorite for a specific POI
	static async getUserFavoriteForPOI(
		poi: POIIdentifier,
		userId: string
	): Promise<{
		success: boolean;
		favorite: POIFavoriteInteraction | null;
		isFavorited: boolean;
	}> {
		try {
			const params = {
				...this.buildPOIParams(poi),
				interactionType: 'favorite',
				userId,
			};

			const response = await this.get<InteractionListResponse>(
				'/interactions',
				params
			);

			if (!response.success) {
				throw new Error(response.error || 'Failed to get user favorite');
			}

			// Defensive: ensure response.interactions is an array
			const favoritesArr = Array.isArray(response.interactions)
				? (response.interactions.filter(
						(i) => i.interaction_type === 'favorite'
				  ) as POIFavoriteInteraction[])
				: [];
			const userFavorite = favoritesArr.find(
				(favorite) => favorite.user_id === userId
			);

			return {
				success: true,
				favorite: userFavorite || null,
				isFavorited: !!userFavorite,
			};
		} catch (error) {
			throw new Error(super.handleApiError(error));
		}
	}

	// Get user's favorites across all POIs
	static async getUserFavorites(
		userId: string,
		limit: number = 20,
		offset: number = 0,
		favorite_type?: 'location' | 'bookmark' | 'wishlist'
	): Promise<InteractionListResponse> {
		const params = {
			userId,
			interactionType: 'favorite',
			limit,
			offset,
			...(favorite_type && { favorite_type }),
		};

		return this.get<InteractionListResponse>('/interactions', params);
	}

	// Get favorite statistics for a POI
	static async getFavoriteStats(poi: POIIdentifier): Promise<{
		success: boolean;
		totalFavorites: number;
		favoritesByType: Record<string, number>;
		recentFavorites: number; // favorites in last 30 days
	}> {
		try {
			const response = await this.getPOIFavorites(poi, { limit: 1 });

			if (!response.success) {
				throw new Error(response.error || 'Failed to get favorite stats');
			}

			return {
				success: true,
				totalFavorites: response.total_count || 0,
				favoritesByType: {}, // Would need additional API endpoint for this
				recentFavorites: 0, // Would need additional API endpoint for this
			};
		} catch (error) {
			throw new Error(super.handleApiError(error));
		}
	}

	// Check if user has favorited a POI
	static async checkUserFavorite(
		poi: POIIdentifier,
		userId: string
	): Promise<{
		success: boolean;
		isFavorited: boolean;
		favoriteCount: number;
	}> {
		try {
			const result = await this.getUserFavoriteForPOI(poi, userId);
			const stats = await this.getFavoriteStats(poi);

			return {
				success: true,
				isFavorited: result.isFavorited,
				favoriteCount: stats.totalFavorites,
			};
		} catch (error) {
			throw new Error(super.handleApiError(error));
		}
	}

	// === ADDITIONAL UNIFIED METHODS ===

	// Get user's save status for a POI (legacy API)
	static async getUserSaveStatus(
		poi: POIIdentifier,
		userId: string
	): Promise<{
		success: boolean;
		is_saved?: boolean;
		save_count?: number;
		error?: string;
	}> {
		try {
			// Get user's save interactions for this POI
			const userParams = {
				...this.buildPOIParams(poi),
				interactionType: 'favorite',
				userId,
			};

			const userResponse = await this.get<InteractionListResponse>(
				'/interactions',
				userParams
			);

			if (!userResponse.success) {
				throw new Error(userResponse.error || 'Failed to get user save status');
			}

			const is_saved =
				userResponse.interactions && userResponse.interactions.length > 0;

			// Get total save count for the POI (all users)
			const countParams = {
				...this.buildPOIParams(poi),
				interactionType: 'favorite',
			};

			const countResponse = await this.get<InteractionListResponse>(
				'/interactions',
				countParams
			);
			const favorite_count = countResponse.success
				? countResponse.total_count || 0
				: 0;

			return {
				success: true,
				is_saved,
				save_count: favorite_count, // Legacy compatibility
			};
		} catch (error) {
			return {
				success: false,
				error: super.handleApiError(error),
			};
		}
	}

	// Get public save count for a POI (no authentication required)
	static async getPOISaveCount(poi: POIIdentifier): Promise<{
		success: boolean;
		saveCount: number;
	}> {
		try {
			const params = {
				...this.buildPOIParams(poi),
				interactionType: 'save',
				limit: 1,
				offset: 0,
			};

			const response = await this.get<InteractionListResponse>(
				'/interactions',
				params
			);

			if (!response.success) {
				throw new Error(response.error || 'Failed to get save count');
			}

			return {
				success: true,
				saveCount: response.total_count || 0,
			};
		} catch (error) {
			throw new Error(super.handleApiError(error));
		}
	}
}
