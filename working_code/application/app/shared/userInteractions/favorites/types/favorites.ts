/** @format */

// Unified Favorite/Save types and interfaces
// This module handles both saves (legacy API) and favorites (new API)

import {
	BaseInteraction,
	BaseInteractionResponse,
	POIIdentifier,
} from '@/app/shared/userInteractions/shared/types';

// Main favorite interaction interface (unified)
export interface FavoriteInteraction extends BaseInteraction {
	interaction_type: 'favorite';
	favorite_id?: string | number;
	favorite_type?: 'location' | 'bookmark' | 'wishlist';
	notes?: string;
	tags?: string[];
	priority?: 'low' | 'medium' | 'high';
	category?: string;
	// Legacy save fields
	favorited_at?: string;
}

export interface POIFavoriteInteraction
	extends FavoriteInteraction,
		POIIdentifier {
	// Explicitly include POI identifier properties to ensure they're available
	// Note: All spatial IDs are BIGINT in DB but handled as numbers in JS (safe up to 9+ quadrillion)
	poi_id?: number | null; // BIGINT from spatial_schema.pois.id
	user_poi_temp_id?: number | null; // BIGINT from spatial_schema.user_pois_temp.id
	user_poi_approved_id?: number | null; // BIGINT from spatial_schema.user_pois_approved.id
	poi_type: 'official' | 'user_temp' | 'user_approved';
	// Explicitly include BaseInteraction properties
	created_at: string;
	updated_at?: string;
}

// Extended favorite interface (matches database schema)
export interface FavoriteWithUser extends POIFavoriteInteraction {
	user_name?: string;
	user_avatar?: string;
	user_favorite_count?: number;
}

// Legacy save interface (for backward compatibility)
export interface SaveInteraction extends FavoriteInteraction {
	interaction_type: 'favorite'; // Saves are now treated as favorites
}

export interface POISaveInteraction extends SaveInteraction, POIIdentifier {}

// Unified request interface (works with both APIs)
export interface FavoriteRequest {
	poi_identifier: POIIdentifier;
	action: 'add' | 'remove' | 'toggle';
	favorite_id?: string | number; // for remove action
	favorite_type?: 'location' | 'bookmark' | 'wishlist';
	notes?: string;
	tags?: string[];
	priority?: 'low' | 'medium' | 'high';
	category?: string;
}

// Legacy save request (for backward compatibility)
export interface SaveRequest extends FavoriteRequest {
	action: 'add' | 'remove';
}

// Unified response interface
export interface FavoriteResponse extends BaseInteractionResponse {
	favorite?: POIFavoriteInteraction;
	save?: POISaveInteraction; // Legacy save response
	favorite_count?: number;
	// save_count removed - now unified with favorite_count
	is_favorited?: boolean;
	is_saved?: boolean; // Legacy save state
	already_favorited?: boolean;
}

// Legacy favorite POI interface (for existing components)
export interface FavoritePOI {
	favorite_id: string;
	poi_type: 'official' | 'user_temp' | 'user_approved';
	name: string;
	category: string;
	subcategory?: string;
	latitude: number;
	longitude: number;
	favorite_notes?: string;
	favorited_at: string;
	created_at?: string; // Add created_at property for compatibility
	poi_id?: number;
	user_poi_temp_id?: number;
	user_poi_approved_id?: number;
}

// Unified hook options (works for both saves and favorites)
export interface UseFavoritesOptions {
	poi_identifier?: POIIdentifier;
	user_id?: string;
	auto_load?: boolean;
	enable_optimistic_updates?: boolean;
	sort_by?: 'created_at' | 'last_interaction_at' | 'priority';
	sort_order?: 'asc' | 'desc';
	limit?: number;
	favorite_type?: 'location' | 'bookmark' | 'wishlist';
}

// Unified hook result (works for both saves and favorites)
export interface UseFavoritesResult {
	// State
	favorites: FavoriteWithUser[];
	userFavorite: POIFavoriteInteraction | null;
	favoriteCount: number;
	isFavorited: boolean;
	loading: boolean;
	actionLoading: boolean; // true only during user action
	ready: boolean; // true when initial state is loaded and ready for actions
	error: string | null;
	hasMore: boolean;

	// Actions (simplified like likes)
	addFavorite: () => Promise<void>;
	removeFavorite: () => Promise<void>;
	toggleFavorite: () => Promise<void>;

	// Data loading
	loadFavorites: () => Promise<void>;
	loadMore: () => Promise<void>;
	refresh: () => Promise<void>;
	loadUserFavorite: () => Promise<void>;
	loadFavoriteState: () => Promise<void>;
}

// User favorites across all POIs
export interface UseUserFavoritesResult {
	// State
	favorites: POIFavoriteInteraction[];
	loading: boolean;
	error: string | null;
	hasMore: boolean;
	totalCount: number;

	// Actions
	loadFavorites: (userId?: string) => Promise<void>;
	loadMore: () => Promise<void>;
	refresh: () => Promise<void>;
	removeFavorite: (favoriteId: string | number) => Promise<void>;
}

export interface UseUserFavoritesOptions {
	user_id?: string;
	auto_load?: boolean;
	limit?: number;
	offset?: number;
	favorite_type?: 'location' | 'bookmark' | 'wishlist';
}

// Legacy saves hook options (for backward compatibility)
export interface UseSavesOptions {
	poi_identifier?: POIIdentifier;
	user_id?: string;
	auto_load?: boolean;
	enable_optimistic_updates?: boolean;
}

// Legacy saves hook result (for backward compatibility)
export interface UseSavesResult {
	saveCount: number;
	isSaved: boolean;
	loading: boolean;
	actionLoading: boolean;
	ready: boolean;
	error: string | null;
	toggleSave: (notes?: string) => Promise<void>;
	addSave: (notes?: string) => Promise<void>;
	removeSave: () => Promise<void>;
	updateSaveNotes: (notes: string) => Promise<void>;
	refresh: () => Promise<void>;
	loadSaveState: () => Promise<void>;
}

export interface UseUserFavoritesResult {
	// State
	favorites: POIFavoriteInteraction[];
	loading: boolean;
	error: string | null;
	hasMore: boolean;
	totalCount: number;

	// Actions
	loadFavorites: (userId?: string) => Promise<void>;
	loadMore: () => Promise<void>;
	refresh: () => Promise<void>;
	removeFavorite: (favoriteId: string | number) => Promise<void>;
}

// Legacy compatibility types for existing saves implementation
export interface FavoritePOILegacy {
	favorite_id?: string;
	poi_id?: number | string | null;
	user_poi_temp_id?: number | string | null;
	user_poi_approved_id?: number | string | null;
	poi_type: 'official' | 'user_temp' | 'user_approved';
	name?: string;
	name_en?: string;
	category?: string;
	subcategory?: string;
	latitude?: number;
	longitude?: number;
	neighborhood?: string;
	favorite_notes?: string;
	favorite_type?: string;
	created_at?: string;
}

// Utility functions for favorites
export const createFavoriteRequest = (
	poi: POIIdentifier,
	action: FavoriteRequest['action'],
	favoriteData?: Partial<Omit<FavoriteRequest, 'poi_identifier' | 'action'>>
): FavoriteRequest => ({
	poi_identifier: poi,
	action,
	...favoriteData,
});

export const getDefaultFavoriteData = () => ({
	favorite_type: 'location' as const,
	priority: 'medium' as const,
	notes: '',
	tags: [],
	category: '',
});

export const convertLegacyFavoriteToPOIIdentifier = (
	favorite: FavoritePOILegacy
): POIIdentifier => ({
	poi_id:
		typeof favorite.poi_id === 'string'
			? parseInt(favorite.poi_id) || null
			: favorite.poi_id,
	user_poi_temp_id:
		typeof favorite.user_poi_temp_id === 'string'
			? parseInt(favorite.user_poi_temp_id) || null
			: favorite.user_poi_temp_id,
	user_poi_approved_id:
		typeof favorite.user_poi_approved_id === 'string'
			? parseInt(favorite.user_poi_approved_id) || null
			: favorite.user_poi_approved_id,
	poi_type: favorite.poi_type,
});

export const convertFavoriteInteractionToLegacy = (
	favorite: POIFavoriteInteraction
): FavoritePOILegacy => ({
	favorite_id: favorite.favorite_id?.toString(),
	poi_id: favorite.poi_id,
	user_poi_temp_id: favorite.user_poi_temp_id,
	user_poi_approved_id: favorite.user_poi_approved_id,
	poi_type: favorite.poi_type,
	favorite_notes: favorite.notes,
	favorite_type: favorite.favorite_type,
	created_at: favorite.created_at,
});
