/** @format */

'use client';

import { POIIdentifier } from '@/app/shared/userInteractions/shared/types/base';
import { useSession } from 'next-auth/react';
import { useCallback, useState } from 'react';

// Simplified review types for the new system
export interface SimpleReview {
	id: string;
	user_id: string;
	username?: string;
	user_name?: string;
	user_avatar?: string;
	rating: number;
	review_title?: string;
	review_text: string;
	visit_date: string;
	photos: string[];
	tags?: string[];
	helpful_votes: number;
	is_verified?: boolean;
	created_at: string;
	updated_at?: string;
	is_own_review?: boolean;
	user_helpful_vote?: 'up' | 'down' | null;
}

export interface ReviewStats {
	total_count: number;
	average_rating: number;
	rating_distribution: {
		1: number;
		2: number;
		3: number;
		4: number;
		5: number;
	};
}

export interface UseSimpleReviewsOptions {
	poi_identifier: POIIdentifier;
	auto_load?: boolean;
	limit?: number;
}

export interface UseSimpleReviewsResult {
	// Data
	reviews: SimpleReview[];
	stats: ReviewStats;
	userReview: SimpleReview | null;

	// Loading states
	loading: boolean;
	loadingMore: boolean;
	submitting: boolean;

	// Pagination
	hasMore: boolean;
	currentOffset: number;

	// Error state
	error: string | null;

	// Actions
	loadReviews: () => Promise<void>;
	loadMore: () => Promise<void>;
	submitReview: (reviewData: {
		rating: number;
		review_title?: string;
		review_text: string;
		visit_date: string;
		photos?: string[];
		tags?: string[];
	}) => Promise<void>;
	updateReview: (
		reviewId: string,
		reviewData: Partial<{
			rating: number;
			review_title?: string;
			review_text: string;
			visit_date: string;
			photos?: string[];
			tags?: string[];
		}>
	) => Promise<void>;
	deleteReview: (reviewId: string) => Promise<void>;
	toggleHelpful: (reviewId: string) => Promise<void>;

	// Utility
	refresh: () => Promise<void>;
}

/**
 * Simplified review hook for lazy loading and managing reviews
 * Only loads when explicitly called, not on mount
 */
export const useSimpleReviews = ({
	poi_identifier,
	auto_load = false,
	limit = 20,
}: UseSimpleReviewsOptions): UseSimpleReviewsResult => {
	const { data: session } = useSession();
	const userId = session?.user?.id;

	// State
	const [reviews, setReviews] = useState<SimpleReview[]>([]);
	const [stats, setStats] = useState<ReviewStats>({
		total_count: 0,
		average_rating: 0,
		rating_distribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
	});
	const [userReview, setUserReview] = useState<SimpleReview | null>(null);
	const [loading, setLoading] = useState(false);
	const [loadingMore, setLoadingMore] = useState(false);
	const [submitting, setSubmitting] = useState(false);
	const [hasMore, setHasMore] = useState(false);
	const [currentOffset, setCurrentOffset] = useState(0);
	const [error, setError] = useState<string | null>(null);

	// Helper to build API URL
	const buildApiUrl = useCallback(
		(offset: number = 0) => {
			const params = new URLSearchParams();

			if (poi_identifier.poi_type === 'official' && poi_identifier.poi_id) {
				params.set('poiId', poi_identifier.poi_id.toString());
			} else if (
				poi_identifier.poi_type === 'user_temp' &&
				poi_identifier.user_poi_temp_id
			) {
				params.set('userPoiTempId', poi_identifier.user_poi_temp_id.toString());
			} else if (
				poi_identifier.poi_type === 'user_approved' &&
				poi_identifier.user_poi_approved_id
			) {
				params.set(
					'userPoiApprovedId',
					poi_identifier.user_poi_approved_id.toString()
				);
			}

			params.set('poiType', poi_identifier.poi_type);
			params.set('limit', limit.toString());
			params.set('offset', offset.toString());
			params.set('sortBy', 'created_at');
			params.set('sortOrder', 'desc');

			return `/api/pois/reviews?${params.toString()}`;
		},
		[poi_identifier, limit]
	);

	// Load reviews
	const loadReviews = useCallback(
		async (offset: number = 0, append: boolean = false) => {
			if (!poi_identifier) return;

			if (append) {
				setLoadingMore(true);
			} else {
				setLoading(true);
			}
			setError(null);

			try {
				const response = await fetch(buildApiUrl(offset));

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const result = await response.json();

				if (result.success) {
					const newReviews = result.reviews || [];

					if (append) {
						setReviews((prev) => [...prev, ...newReviews]);
					} else {
						setReviews(newReviews);
					}

					setStats({
						total_count: result.total_count || 0,
						average_rating: result.average_rating || 0,
						rating_distribution: result.rating_distribution || {
							1: 0,
							2: 0,
							3: 0,
							4: 0,
							5: 0,
						},
					});

					setHasMore(result.has_more || false);
					setCurrentOffset(offset + newReviews.length);

					// Find user's review if it exists
					if (userId) {
						const userReviewFound = newReviews.find(
							(review: SimpleReview) => review.user_id === userId
						);
						if (userReviewFound) {
							setUserReview(userReviewFound);
						}
					}
				} else {
					throw new Error(result.error || 'Failed to load reviews');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to load reviews';
				setError(errorMessage);
				console.error('Error loading reviews:', err);
			} finally {
				setLoading(false);
				setLoadingMore(false);
			}
		},
		[poi_identifier, buildApiUrl, userId]
	);

	// Load more reviews
	const loadMore = useCallback(async () => {
		if (loadingMore || !hasMore) return;
		await loadReviews(currentOffset, true);
	}, [loadReviews, currentOffset, hasMore, loadingMore]);

	// Submit new review
	const submitReview = useCallback(
		async (reviewData: {
			rating: number;
			review_title?: string;
			review_text: string;
			visit_date: string;
			photos?: string[];
			tags?: string[];
		}) => {
			if (!poi_identifier || !userId) {
				throw new Error('POI identifier and user authentication required');
			}

			setSubmitting(true);
			setError(null);

			try {
				const body = {
					...reviewData,
					poiType: poi_identifier.poi_type,
				};

				// Add the correct POI ID field based on POI type
				if (poi_identifier.poi_type === 'official' && poi_identifier.poi_id) {
					(body as any).poiId = poi_identifier.poi_id;
				} else if (
					poi_identifier.poi_type === 'user_temp' &&
					poi_identifier.user_poi_temp_id
				) {
					(body as any).userPoiTempId = poi_identifier.user_poi_temp_id;
				} else if (
					poi_identifier.poi_type === 'user_approved' &&
					poi_identifier.user_poi_approved_id
				) {
					(body as any).userPoiApprovedId = poi_identifier.user_poi_approved_id;
				}

				const response = await fetch('/api/pois/reviews', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify(body),
				});

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const result = await response.json();

				if (!result.success) {
					throw new Error(result.error || 'Failed to submit review');
				}

				// Refresh reviews after successful submission
				await loadReviews(0, false);
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to submit review';
				setError(errorMessage);
				console.error('Error submitting review:', err);
				throw err;
			} finally {
				setSubmitting(false);
			}
		},
		[poi_identifier, userId, loadReviews]
	);

	// Update existing review
	const updateReview = useCallback(
		async (
			reviewId: string,
			reviewData: Partial<{
				rating: number;
				review_title?: string;
				review_text: string;
				visit_date: string;
				photos?: string[];
				tags?: string[];
			}>
		) => {
			if (!userId) {
				throw new Error('User authentication required');
			}

			setSubmitting(true);
			setError(null);

			try {
				const response = await fetch('/api/pois/reviews', {
					method: 'PUT',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						reviewId,
						...reviewData,
					}),
				});

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const result = await response.json();

				if (!result.success) {
					throw new Error(result.error || 'Failed to update review');
				}

				// Refresh reviews after successful update
				await loadReviews(0, false);
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to update review';
				setError(errorMessage);
				console.error('Error updating review:', err);
				throw err;
			} finally {
				setSubmitting(false);
			}
		},
		[userId, loadReviews]
	);

	// Delete review
	const deleteReview = useCallback(
		async (reviewId: string) => {
			if (!userId) {
				throw new Error('User authentication required');
			}

			setSubmitting(true);
			setError(null);

			try {
				const response = await fetch(`/api/pois/reviews?reviewId=${reviewId}`, {
					method: 'DELETE',
				});

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const result = await response.json();

				if (!result.success) {
					throw new Error(result.error || 'Failed to delete review');
				}

				// Refresh reviews after successful deletion
				await loadReviews(0, false);
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to delete review';
				setError(errorMessage);
				console.error('Error deleting review:', err);
				throw err;
			} finally {
				setSubmitting(false);
			}
		},
		[userId, loadReviews]
	);

	// Toggle helpful vote
	const toggleHelpful = useCallback(
		async (reviewId: string) => {
			if (!userId) {
				throw new Error('User authentication required');
			}

			setError(null);

			try {
				// Find the review to determine current helpful state
				const review = reviews.find((r) => r.id === reviewId);
				const isCurrentlyHelpful = review?.user_helpful_vote === 'up';

				const response = await fetch('/api/pois/reviews/helpful', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						review_id: reviewId,
						action: isCurrentlyHelpful ? 'remove' : 'add',
					}),
				});

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const result = await response.json();

				if (!result.success) {
					throw new Error(result.error || 'Failed to toggle helpful vote');
				}

				// Update the review in the local state
				setReviews((prev) =>
					prev.map((r) => {
						if (r.id === reviewId) {
							return {
								...r,
								helpful_votes: result.helpful_votes || r.helpful_votes,
								user_helpful_vote: result.is_helpful ? 'up' : null,
							};
						}
						return r;
					})
				);
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to toggle helpful vote';
				setError(errorMessage);
				console.error('Error toggling helpful vote:', err);
				throw err;
			}
		},
		[userId, reviews]
	);

	// Refresh reviews
	const refresh = useCallback(async () => {
		await loadReviews(0, false);
	}, [loadReviews]);

	// Auto-load if enabled
	if (auto_load && poi_identifier && !loading && reviews.length === 0) {
		loadReviews();
	}

	return {
		reviews,
		stats,
		userReview,
		loading,
		loadingMore,
		submitting,
		hasMore,
		currentOffset,
		error,
		loadReviews,
		loadMore,
		submitReview,
		updateReview,
		deleteReview,
		toggleHelpful,
		refresh,
	};
};
