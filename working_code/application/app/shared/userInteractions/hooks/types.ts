/** @format */

/**
 * Shared types for interaction hooks to ensure consistency
 * between unified and batch interaction systems
 */

// Base POI identifier for batch operations
export interface BatchPOI {
	poi_id: number;
	poi_type: string;
}

// Standardized interaction data format used by both systems
export interface InteractionData {
	poi_id: number;
	poi_type: string;
	like_count: number;
	favorite_count: number;
	visit_count: number;
	review_count: number;
	user_has_liked: boolean;
	user_has_favorited: boolean;
	user_has_visited: boolean;
}

// Cache entry structure for shared caching
export interface CacheEntry {
	data: InteractionData;
	timestamp: number;
}

// Shared cache type
export type SharedCache = Map<string, CacheEntry>;

// Common options for both hook types
export interface BaseInteractionOptions {
	enable_caching?: boolean;
	cache_duration?: number; // in milliseconds
	enable_optimistic_updates?: boolean;
}

// Action loading states
export interface ActionLoadingStates {
	like: boolean;
	favorite: boolean;
	visit: boolean;
}

// Interaction action types
export type InteractionAction = 'add' | 'remove';
export type InteractionType = 'like' | 'favorite' | 'visit';

// API response format for consistency
export interface APIInteractionResponse {
	poi_id: number;
	poi_type: string;
	like?: { count: number; hasInteraction: boolean };
	favorite?: { count: number; hasInteraction: boolean };
	visit?: { count: number; hasInteraction: boolean };
	review?: { count: number };
}

// Utility functions for data conversion
export const convertAPIToInteractionData = (apiData: APIInteractionResponse): InteractionData => {
	return {
		poi_id: apiData.poi_id,
		poi_type: apiData.poi_type,
		like_count: apiData.like?.count || 0,
		favorite_count: apiData.favorite?.count || 0,
		visit_count: apiData.visit?.count || 0,
		review_count: apiData.review?.count || 0,
		user_has_liked: apiData.like?.hasInteraction || false,
		user_has_favorited: apiData.favorite?.hasInteraction || false,
		user_has_visited: apiData.visit?.hasInteraction || false,
	};
};

// Generate cache key for POI
export const generatePOIKey = (poi: BatchPOI | { poi_id?: number; poi_type: string; user_poi_temp_id?: number; user_poi_approved_id?: number }): string => {
	if ('poi_id' in poi && poi.poi_id) {
		return `${poi.poi_type}_${poi.poi_id}`;
	} else if ('user_poi_temp_id' in poi && poi.user_poi_temp_id) {
		return `user_temp_${poi.user_poi_temp_id}`;
	} else if ('user_poi_approved_id' in poi && poi.user_poi_approved_id) {
		return `user_approved_${poi.user_poi_approved_id}`;
	}
	return '';
};

// Check if cache entry is still valid
export const isCacheEntryValid = (entry: CacheEntry | undefined, cacheDuration: number): boolean => {
	if (!entry) return false;
	return Date.now() - entry.timestamp < cacheDuration;
};
