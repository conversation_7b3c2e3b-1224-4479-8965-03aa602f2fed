/** @format */

// Base service with common API logic

import {
	InteractionAction,
	InteractionListResponse,
	InteractionResponse,
	InteractionType,
	POIIdentifier,
	createPOIKey,
} from '../types';

export class BaseInteractionService {
	protected static baseUrl = '/api/pois';

	// Helper method to build query parameters
	protected static buildQueryParams(params: Record<string, unknown>): string {
		const searchParams = new URLSearchParams();

		Object.entries(params).forEach(([key, value]) => {
			if (value !== null && value !== undefined) {
				searchParams.append(key, String(value));
			}
		});

		return searchParams.toString();
	}

	// Helper method to build POI parameters
	static buildPOIParams(poi: POIIdentifier): Record<string, unknown> {
		const params: Record<string, unknown> = {
			poiType: poi.poi_type,
		};

		if (poi.poi_type === 'official' && poi.poi_id) {
			params.poiId = poi.poi_id;
		} else if (poi.poi_type === 'user_temp' && poi.user_poi_temp_id) {
			params.userPoiTempId = poi.user_poi_temp_id;
		} else if (poi.poi_type === 'user_approved' && poi.user_poi_approved_id) {
			params.userPoiApprovedId = poi.user_poi_approved_id;
		}

		return params;
	}

	// Helper method to build POI body
	static buildPOIBody(poi: POIIdentifier): Record<string, unknown> {
		const body: Record<string, unknown> = {
			poiType: poi.poi_type,
		};

		if (poi.poi_type === 'official' && poi.poi_id) {
			body.poiId = poi.poi_id;
		} else if (poi.poi_type === 'user_temp' && poi.user_poi_temp_id) {
			body.userPoiTempId = poi.user_poi_temp_id;
		} else if (poi.poi_type === 'user_approved' && poi.user_poi_approved_id) {
			body.userPoiApprovedId = poi.user_poi_approved_id;
		}

		return body;
	}

	// Generic GET request
	static async get<T = unknown>(
		endpoint: string,
		params?: Record<string, unknown>
	): Promise<T> {
		const url = params
			? `${this.baseUrl}${endpoint}?${this.buildQueryParams(params)}`
			: `${this.baseUrl}${endpoint}`;

		console.log('BaseService GET request:', url);

		const response = await fetch(url, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
			},
			credentials: 'include', // Include cookies for authentication
		});

		if (!response.ok) {
			console.error(
				'BaseService GET error:',
				response.status,
				response.statusText
			);
			// For authentication/authorization errors, try to get the error message from the response
			if (response.status === 401 || response.status === 403) {
				try {
					const errorData = await response.json();
					console.log('BaseService auth error data:', errorData);
					return errorData as T;
				} catch {
					// If we can't parse the response, return a generic error object
					return {
						success: false,
						error:
							response.status === 401
								? 'Authentication required'
								: 'Access denied',
					} as T;
				}
			}
			throw new Error(`HTTP error! status: ${response.status}`);
		}

		const jsonResponse = await response.json();
		console.log('BaseService GET response:', jsonResponse);
		return jsonResponse;
	}

	// Generic POST request
	static async post<T = unknown>(
		endpoint: string,
		body?: Record<string, unknown>
	): Promise<T> {
		const response = await fetch(`${this.baseUrl}${endpoint}`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			credentials: 'include', // Include cookies for authentication
			body: body ? JSON.stringify(body) : undefined,
		});

		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`);
		}

		return response.json();
	}

	// Generic PUT request
	static async put<T = unknown>(
		endpoint: string,
		body?: Record<string, unknown>
	): Promise<T> {
		const response = await fetch(`${this.baseUrl}${endpoint}`, {
			method: 'PUT',
			headers: {
				'Content-Type': 'application/json',
			},
			credentials: 'include', // Include cookies for authentication
			body: body ? JSON.stringify(body) : undefined,
		});

		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`);
		}

		return response.json();
	}

	// Generic DELETE request
	static async delete<T = unknown>(
		endpoint: string,
		params?: Record<string, unknown>
	): Promise<T> {
		const url = params
			? `${this.baseUrl}${endpoint}?${this.buildQueryParams(params)}`
			: `${this.baseUrl}${endpoint}`;

		const response = await fetch(url, {
			method: 'DELETE',
			headers: {
				'Content-Type': 'application/json',
			},
			credentials: 'include', // Include cookies for authentication
		});

		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`);
		}

		return response.json();
	}

	// Get user interactions for a POI
	static async getUserInteractions(
		poi: POIIdentifier,
		userId: string,
		interactionType?: InteractionType
	): Promise<InteractionListResponse> {
		const params = {
			...this.buildPOIParams(poi),
			userId,
			...(interactionType && { interactionType }),
		};

		return this.get<InteractionListResponse>('/interactions', params);
	}

	// Add or remove an interaction
	static async updateInteraction(
		poi: POIIdentifier,
		interactionType: InteractionType,
		action: InteractionAction,
		metadata?: Record<string, unknown>
	): Promise<InteractionResponse> {
		const body = {
			...this.buildPOIBody(poi),
			interactionType,
			action,
			...(metadata && { metadata }),
		};

		return this.post<InteractionResponse>('/interactions', body);
	}

	// Note: Bulk operations not implemented yet

	// Note: getInteractionCounts removed - counts should be retrieved from POI data or individual hooks

	// Utility methods
	static createPOIKey = createPOIKey;

	static validatePOI(poi: POIIdentifier): boolean {
		if (
			!poi.poi_type ||
			!['official', 'user_temp', 'user_approved'].includes(poi.poi_type)
		) {
			return false;
		}

		switch (poi.poi_type) {
			case 'official':
				return !!poi.poi_id;
			case 'user_temp':
				return !!poi.user_poi_temp_id;
			case 'user_approved':
				return !!poi.user_poi_approved_id;
			default:
				return false;
		}
	}

	static handleApiError(error: unknown): string {
		if (error instanceof Error) {
			return error.message;
		}
		if (typeof error === 'string') {
			return error;
		}
		if (typeof error === 'object' && error !== null && 'error' in error) {
			return (error as { error: string }).error;
		}
		return 'An unexpected error occurred';
	}
}
