/** @format */

'use client';

import React, { createContext, ReactNode, useContext } from 'react';
import {
	InteractionType,
	useSharedUserInteractions,
	UseSharedUserInteractionsResult,
} from '../hooks/useSharedUserInteractions';

const UserInteractionsContext =
	createContext<UseSharedUserInteractionsResult | null>(null);

interface UserInteractionsProviderProps {
	children: ReactNode;
	userId: string;
}

export const UserInteractionsProvider: React.FC<
	UserInteractionsProviderProps
> = ({ children, userId }) => {
	const sharedInteractions = useSharedUserInteractions({
		user_id: userId,
		auto_load: false, // We'll manually load when needed
		limit: 50,
	});

	// console.log('UserInteractionsProvider initialized with userId:', userId)

	return (
		<UserInteractionsContext.Provider value={sharedInteractions}>
			{children}
		</UserInteractionsContext.Provider>
	);
};

export const useUserInteractionsContext =
	(): UseSharedUserInteractionsResult => {
		const context = useContext(UserInteractionsContext);
		if (!context) {
			throw new Error(
				'useUserInteractionsContext must be used within a UserInteractionsProvider'
			);
		}
		return context;
	};

// Hook for specific interaction type with automatic loading
export const useInteractionType = (type: InteractionType) => {
	const context = useUserInteractionsContext();
	const interactionState = context[type];
	const [hasTriedLoading, setHasTriedLoading] = React.useState(false);

	// Note: Auto-loading is now handled centrally in ProfileInfo component
	// This prevents duplicate API calls when multiple tabs try to load the same data
	// Individual tabs will use the data that's already loaded by ProfileInfo

	// Only auto-load if data is specifically requested and not already loading/loaded
	React.useEffect(() => {}, [
		type,
		context,
		interactionState.items.length,
		interactionState.loading,
		interactionState.error,
		hasTriedLoading,
	]);

	return {
		...interactionState,
		loadMore: () => context.loadMore(type),
		refresh: () => {
			setHasTriedLoading(false); // Reset loading flag on manual refresh
			return context.refresh(type);
		},
		removeInteraction: (id: string) => context.removeInteraction(type, id),
	};
};
