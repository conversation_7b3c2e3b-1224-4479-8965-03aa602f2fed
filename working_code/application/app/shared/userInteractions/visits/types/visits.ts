// Visit-specific types and interfaces

import { BaseInteraction, POIIdentifier, BaseInteractionResponse } from '@/app/shared/userInteractions/shared/types'

export interface VisitInteraction extends BaseInteraction {
  interaction_type: 'visit'
  visit_id?: string | number
  visit_date?: string
  visit_duration_minutes?: number
  visit_type?: 'planned' | 'spontaneous' | 'recommended'
  notes?: string
}

export interface POIVisitInteraction extends VisitInteraction, POIIdentifier {}

// Extended visit interface (matches database schema)
export interface VisitWithUser extends POIVisitInteraction {
  user_name?: string
  user_avatar?: string
  user_visit_count?: number
}

export interface VisitRequest {
  poi_identifier: POIIdentifier
  action: 'add' | 'remove'
  visit_id?: string | number // for remove
  visit_date?: string
  visit_duration_minutes?: number
  visit_type?: 'planned' | 'spontaneous' | 'recommended'
  notes?: string
}

export interface VisitResponse extends BaseInteractionResponse {
  visit?: POIVisitInteraction
  visit_count?: number
  has_visited?: boolean
}

export interface VisitListResponse extends BaseInteractionResponse {
  interactions: POIVisitInteraction[]
  total_count?: number
  has_more?: boolean
}

export interface UseVisitsOptions {
  poi_identifier?: POIIdentifier
  user_id?: string
  auto_load?: boolean
  enable_optimistic_updates?: boolean
  sort_by?: 'created_at' | 'visit_date'
  sort_order?: 'asc' | 'desc'
  limit?: number
}

export interface UseVisitsResult {
  // State
  visits: VisitWithUser[]
  userVisit: POIVisitInteraction | null
  visitCount: number
  hasVisited: boolean
  loading: boolean
  actionLoading: boolean // true only during user action
  ready: boolean // true when initial state is loaded and ready for actions
  error: string | null
  hasMore: boolean

  // Actions (simplified like likes)
  addVisit: (visitData?: Omit<VisitRequest, 'poi_identifier' | 'action'>) => Promise<void>
  removeVisit: () => Promise<void>
  toggleVisit: (visitData?: Omit<VisitRequest, 'poi_identifier' | 'action'>) => Promise<void>
  
  // Data loading
  loadVisits: () => Promise<void>
  loadMore: () => Promise<void>
  refresh: () => Promise<void>
  loadUserVisit: () => Promise<void>
  loadVisitState: () => Promise<void>
}

export interface UseUserVisitsOptions {
  user_id?: string
  auto_load?: boolean
  limit?: number
  offset?: number
}

export interface UseUserVisitsResult {
  // State
  visits: POIVisitInteraction[]
  loading: boolean
  error: string | null
  hasMore: boolean
  totalCount: number

  // Actions
  loadVisits: (userId?: string) => Promise<void>
  loadMore: () => Promise<void>
  refresh: () => Promise<void>
  removeVisit: (visitId: string | number) => Promise<void>
}

// Utility functions for visits
export const createVisitRequest = (
  poi: POIIdentifier,
  action: VisitRequest['action'],
  visitData?: Partial<Omit<VisitRequest, 'poi_identifier' | 'action'>>
): VisitRequest => ({
  poi_identifier: poi,
  action,
  ...visitData
})

export const getDefaultVisitData = () => ({
  visit_date: new Date().toISOString(),
  visit_type: 'spontaneous' as const,
  visit_duration_minutes: undefined,
  notes: ''
})
