/** @format */

// Review-specific types and interfaces

import {
	BaseInteraction,
	BaseInteractionResponse,
	POIIdentifier,
} from '@/app/shared/userInteractions/shared/types';

export interface ReviewInteraction extends BaseInteraction {
	interaction_type: 'review';
	review_id?: string | number;
	rating: number; // 1-5 scale
	review_title?: string;
	review_text?: string;
	visit_date?: string;
	photos?: string[]; // photo URLs
	tags?: string[];
	helpful_votes?: number;
	is_verified?: boolean;
	response_from_owner?: string;
	response_date?: string;
}

export interface POIReviewInteraction
	extends ReviewInteraction,
		POIIdentifier {}

// Extended review interface (matches existing API)
export interface ReviewWithUser extends POIReviewInteraction {
	user_name?: string;
	user_avatar?: string;
	user_review_count?: number;
	is_helpful?: boolean; // if current user found it helpful
	poi_name?: string; // POI name from joined tables
}

export interface ReviewRequest {
	poi_identifier: POIIdentifier;
	action: 'add' | 'update' | 'remove';
	review_id?: string | number; // for update/remove
	rating?: number;
	review_title?: string;
	review_text?: string;
	visit_date?: string;
	photos?: string[];
	tags?: string[];
}

export interface ReviewResponse extends BaseInteractionResponse {
	review?: POIReviewInteraction;
	review_count?: number;
	average_rating?: number;
	has_reviewed?: boolean;
}

export interface ReviewListResponse extends BaseInteractionResponse {
	reviews: ReviewWithUser[];
	total_count?: number;
	average_rating?: number;
	rating_distribution?: {
		1: number;
		2: number;
		3: number;
		4: number;
		5: number;
	};
	has_more?: boolean;
}

export interface ReviewHelpfulRequest {
	review_id: string | number;
	action: 'add' | 'remove';
}

export interface ReviewHelpfulResponse extends BaseInteractionResponse {
	helpful_votes?: number;
	is_helpful?: boolean;
}

export interface UseReviewsOptions {
	poi_identifier?: POIIdentifier;
	user_id?: string;
	auto_load?: boolean;
	enable_optimistic_updates?: boolean;
	sort_by?: 'created_at' | 'rating' | 'helpful_votes';
	sort_order?: 'asc' | 'desc';
	limit?: number;
}

export interface UseReviewsResult {
	// State
	reviews: ReviewWithUser[];
	userReview: POIReviewInteraction | null;
	reviewCount: number;
	averageRating: number;
	ratingDistribution: ReviewListResponse['rating_distribution'];
	hasReviewed: boolean;
	loading: boolean;
	error: string | null;
	hasMore: boolean;

	// Actions
	addReview: (
		reviewData: Omit<ReviewRequest, 'poi_identifier' | 'action'>
	) => Promise<void>;
	updateReview: (
		reviewId: string | number,
		reviewData: Partial<ReviewRequest>
	) => Promise<void>;
	removeReview: (reviewId: string | number) => Promise<void>;
	toggleHelpful: (reviewId: string | number) => Promise<void>;

	// Data loading
	loadReviews: () => Promise<void>;
	loadMore: () => Promise<void>;
	refresh: () => Promise<void>;
	loadUserReview: () => Promise<void>;
}

export interface UseUserReviewsOptions {
	user_id?: string;
	auto_load?: boolean;
	limit?: number;
	offset?: number;
}

export interface UseUserReviewsResult {
	// State
	reviews: POIReviewInteraction[];
	loading: boolean;
	error: string | null;
	hasMore: boolean;
	totalCount: number;

	// Actions
	loadReviews: (userId?: string) => Promise<void>;
	loadMore: () => Promise<void>;
	refresh: () => Promise<void>;
	removeReview: (reviewId: string | number) => Promise<void>;
}

// Utility functions for reviews
export const createReviewRequest = (
	poi: POIIdentifier,
	action: ReviewRequest['action'],
	reviewData?: Partial<Omit<ReviewRequest, 'poi_identifier' | 'action'>>
): ReviewRequest => ({
	poi_identifier: poi,
	action,
	...reviewData,
});

export const isValidReviewResponse = (
	response: unknown
): response is ReviewResponse => {
	return (
		typeof response === 'object' &&
		response !== null &&
		'success' in response &&
		typeof (response as { success: unknown }).success === 'boolean'
	);
};

export const calculateAverageRating = (
	reviews: ReviewInteraction[]
): number => {
	if (reviews.length === 0) return 0;
	const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
	return Math.round((sum / reviews.length) * 10) / 10; // Round to 1 decimal place
};

export const getRatingDistribution = (
	reviews: ReviewInteraction[]
): ReviewListResponse['rating_distribution'] => {
	const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
	reviews.forEach((review) => {
		const rating = Math.round(review.rating) as keyof typeof distribution;
		if (rating >= 1 && rating <= 5) {
			distribution[rating]++;
		}
	});
	return distribution;
};

export const formatReviewDate = (date: string | Date): string => {
	const reviewDate = typeof date === 'string' ? new Date(date) : date;
	return reviewDate.toLocaleDateString('en-US', {
		year: 'numeric',
		month: 'short',
		day: 'numeric',
	});
};

export const validateRating = (rating: number): boolean => {
	return rating >= 1 && rating <= 5 && Number.isInteger(rating);
};
