/** @format */

// Review-specific service operations

import {
	POIReviewInteraction,
	ReviewHelpfulRequest,
	ReviewHelpfulResponse,
	ReviewListResponse,
	ReviewRequest,
	ReviewResponse,
	ReviewWithUser,
} from '@/app/shared/userInteractions/reviews/types';
import { BaseInteractionService } from '@/app/shared/userInteractions/shared/services';
import { POIIdentifier } from '@/app/shared/userInteractions/shared/types';

export class ReviewsService extends BaseInteractionService {
	// Add a review to a POI
	static async addReview(
		poi: POIIdentifier,
		reviewData: Omit<ReviewRequest, 'poi_identifier' | 'action'>
	): Promise<ReviewResponse> {
		const body = {
			...this.buildPOIBody(poi),
			...reviewData,
		};

		return this.post<ReviewResponse>('/reviews', body);
	}

	// Update an existing review
	static async updateReview(
		reviewId: string | number,
		reviewData: Partial<ReviewRequest>
	): Promise<ReviewResponse> {
		const body = {
			reviewId,
			action: 'update',
			...reviewData,
		};

		return this.put<ReviewResponse>('/reviews', body);
	}

	// Remove a review
	static async removeReview(
		reviewId: string | number
	): Promise<ReviewResponse> {
		const params = {
			reviewId,
		};

		return this.delete<ReviewResponse>('/reviews', params);
	}

	// Get reviews for a POI
	static async getPOIReviews(
		poi: POIIdentifier,
		options: {
			limit?: number;
			offset?: number;
			sortBy?: 'created_at' | 'rating' | 'helpful_votes';
			sortOrder?: 'asc' | 'desc';
		} = {}
	): Promise<ReviewListResponse> {
		const params = {
			...this.buildPOIParams(poi),
			limit: options.limit || 20,
			offset: options.offset || 0,
			sortBy: options.sortBy || 'created_at',
			sortOrder: options.sortOrder || 'desc',
		};

		return this.get<ReviewListResponse>('/reviews', params);
	}

	// Get user's review for a specific POI
	static async getUserReviewForPOI(
		poi: POIIdentifier,
		userId: string
	): Promise<{
		success: boolean;
		review: POIReviewInteraction | null;
		hasReviewed: boolean;
	}> {
		try {
			const params = {
				...this.buildPOIParams(poi),
				userId,
			};

			const response = await this.get<ReviewListResponse>('/reviews', params);

			if (!response.success) {
				throw new Error(response.error || 'Failed to get user review');
			}

			const userReview = response.reviews.find(
				(review) => review.user_id === userId
			);

			return {
				success: true,
				review: userReview || null,
				hasReviewed: !!userReview,
			};
		} catch (error) {
			throw new Error(super.handleApiError(error));
		}
	}

	// Get user's reviews across all POIs
	static async getUserReviews(
		userId: string,
		limit: number = 20,
		offset: number = 0
	): Promise<ReviewListResponse> {
		const params = {
			userId,
			limit,
			offset,
		};

		return this.get<ReviewListResponse>('/reviews', params);
	}

	// Mark review as helpful/unhelpful
	static async toggleReviewHelpful(
		reviewId: string | number,
		isCurrentlyHelpful: boolean
	): Promise<ReviewHelpfulResponse> {
		const body: ReviewHelpfulRequest = {
			review_id: reviewId,
			action: isCurrentlyHelpful ? 'remove' : 'add',
		};

		return this.post<ReviewHelpfulResponse>(
			'/reviews/helpful',
			body as unknown as Record<string, unknown>
		);
	}

	// Get review statistics for a POI
	static async getReviewStats(poi: POIIdentifier): Promise<{
		success: boolean;
		totalReviews: number;
		averageRating: number;
		ratingDistribution: {
			1: number;
			2: number;
			3: number;
			4: number;
			5: number;
		};
	}> {
		try {
			const response = await this.getPOIReviews(poi, { limit: 1 });

			if (!response.success) {
				throw new Error(response.error || 'Failed to get review stats');
			}

			return {
				success: true,
				totalReviews: response.total_count || 0,
				averageRating: response.average_rating || 0,
				ratingDistribution: response.rating_distribution || {
					1: 0,
					2: 0,
					3: 0,
					4: 0,
					5: 0,
				},
			};
		} catch (error) {
			throw new Error(super.handleApiError(error));
		}
	}

	// Utility methods
	static createReviewRequest(
		poi: POIIdentifier,
		action: ReviewRequest['action'],
		reviewData?: Partial<Omit<ReviewRequest, 'poi_identifier' | 'action'>>
	): ReviewRequest {
		return {
			poi_identifier: poi,
			action,
			...reviewData,
		};
	}

	static validateReviewResponse(response: unknown): response is ReviewResponse {
		return (
			typeof response === 'object' &&
			response !== null &&
			'success' in response &&
			typeof (response as { success: unknown }).success === 'boolean'
		);
	}

	static validateRating(rating: number): boolean {
		return rating >= 1 && rating <= 5 && Number.isInteger(rating);
	}

	static calculateAverageRating(reviews: ReviewWithUser[]): number {
		if (reviews.length === 0) return 0;
		const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
		return Math.round((sum / reviews.length) * 10) / 10; // Round to 1 decimal place
	}

	static getRatingDistribution(
		reviews: ReviewWithUser[]
	): ReviewListResponse['rating_distribution'] {
		const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
		reviews.forEach((review) => {
			const rating = Math.round(review.rating) as keyof typeof distribution;
			if (rating >= 1 && rating <= 5) {
				distribution[rating]++;
			}
		});
		return distribution;
	}

	static formatReviewDate(date: string | Date): string {
		const reviewDate = typeof date === 'string' ? new Date(date) : date;
		return reviewDate.toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	}
}
