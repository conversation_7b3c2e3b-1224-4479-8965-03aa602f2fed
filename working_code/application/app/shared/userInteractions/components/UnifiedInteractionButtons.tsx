/** @format */

'use client';

import { useUnifiedInteractions } from '@/app/shared/userInteractions/hooks/useUnifiedInteractions';
import {
	POIIdentifier,
	POIType,
} from '@/app/shared/userInteractions/shared/types/base';
import React, { useMemo } from 'react';
import {
	FaEdit,
	FaHeart,
	FaMapMarkerAlt,
	FaRegHeart,
	FaRegStar,
	FaStar,
} from 'react-icons/fa';

interface UnifiedInteractionButtonsProps {
	poiId?: number | null;
	poiType: POIType;
	userPoiTempId?: number | null;
	userPoiApprovedId?: number | null;

	// Pre-loaded interaction data (optional)
	initialData?: {
		like_count: number;
		favorite_count: number;
		visit_count: number;
		review_count: number;
		user_has_liked: boolean;
		user_has_favorited: boolean;
		user_has_visited: boolean;
	};

	// Callbacks
	onWriteReview?: () => void;

	// Layout options
	layout?: 'horizontal' | 'vertical' | 'compact';
	showCounts?: boolean;
	showLabels?: boolean;
	size?: 'sm' | 'md' | 'lg';

	// Options
	enableOptimisticUpdates?: boolean;
	autoLoad?: boolean;
}

export default function UnifiedInteractionButtons({
	poiId,
	poiType,
	userPoiTempId,
	userPoiApprovedId,
	initialData,
	onWriteReview,
	layout = 'horizontal',
	showCounts = true,
	showLabels = false,
	size = 'md',
	enableOptimisticUpdates = true,
	autoLoad = true,
}: UnifiedInteractionButtonsProps) {
	// Create POI identifier
	const poiIdentifier: POIIdentifier = useMemo(
		() => ({
			poi_id: poiId,
			user_poi_temp_id: userPoiTempId,
			user_poi_approved_id: userPoiApprovedId,
			poi_type: poiType,
		}),
		[poiId, userPoiTempId, userPoiApprovedId, poiType]
	);

	// Use the unified interaction hook
	const {
		data,
		loading,
		actionLoading,
		error,
		ready,
		toggleLike,
		toggleFavorite,
		toggleVisit,
	} = useUnifiedInteractions({
		poi_identifier: poiIdentifier,
		auto_load: autoLoad,
		enable_optimistic_updates: enableOptimisticUpdates,
		initial_data: initialData,
	});

	// Helper functions
	const formatCount = (count: number): string => {
		if (count >= 1000000) {
			return `${(count / 1000000).toFixed(1)}M`;
		} else if (count >= 1000) {
			return `${(count / 1000).toFixed(1)}K`;
		}
		return count.toString();
	};

	const getIconSize = () => {
		switch (size) {
			case 'sm':
				return 'w-4 h-4';
			case 'lg':
				return 'w-6 h-6';
			default:
				return 'w-5 h-5';
		}
	};

	// Render individual button
	const renderButton = (
		icon: React.ReactNode,
		activeIcon: React.ReactNode,
		isActive: boolean,
		count: number,
		label: string,
		onClick: () => void,
		isLoading: boolean = false
	) => {
		const buttonClass = `
			flex flex-col items-center gap-1 p-2 transition-colors duration-200
			${isLoading ? 'opacity-75 cursor-wait' : 'cursor-pointer'}
		`;

		// Get icon colors based on interaction type and state
		const getIconColor = () => {
			if (label === 'Favorite') {
				return isActive
					? 'text-yellow-500'
					: 'text-gray-400 hover:text-yellow-400';
			} else if (label === 'Like this place') {
				return isActive ? 'text-red-500' : 'text-gray-400 hover:text-red-400';
			} else if (label === 'Mark as visited') {
				return isActive
					? 'text-green-500'
					: 'text-gray-400 hover:text-green-400';
			} else {
				return isActive ? 'text-blue-500' : 'text-gray-400 hover:text-blue-400';
			}
		};

		const iconColor = getIconColor();

		return (
			<button
				onClick={onClick}
				disabled={isLoading}
				className={buttonClass}
				title={label}>
				{/* Icon - centered and colored */}
				<div
					className={`${getIconSize()} ${iconColor} transition-colors duration-200 flex items-center justify-center`}>
					{isLoading ? (
						<div className='animate-spin rounded-full border-2 border-current border-t-transparent' />
					) : isActive ? (
						activeIcon
					) : (
						icon
					)}
				</div>

				{/* Count underneath - simple text */}
				{showCounts && (
					<span className='text-sm font-medium text-gray-600'>
						{formatCount(count)}
					</span>
				)}

				{/* Label if enabled */}
				{showLabels && (
					<span className='text-xs text-gray-500'>
						{label === 'Like this place'
							? 'Like'
							: label === 'Mark as visited'
							? 'Visited'
							: label}
					</span>
				)}
			</button>
		);
	};

	const containerClass = `
		flex items-center justify-center
		${layout === 'vertical' ? 'flex-col gap-4' : 'flex-row gap-6'}
		${layout === 'compact' ? 'gap-3' : 'gap-6'}
	`;

	// Show loading state if not ready and no initial data
	if (!ready && !initialData && loading) {
		return (
			<div className={containerClass}>
				<div className='flex items-center gap-2 text-gray-500'>
					<div className='animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-gray-600' />
					<span className='text-sm'>Loading interactions...</span>
				</div>
			</div>
		);
	}

	return (
		<div className={containerClass}>
			{/* Error Display */}
			{error && (
				<div className='mb-2 p-2 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm'>
					{error}
				</div>
			)}

			{/* Like Button */}
			{renderButton(
				<FaRegHeart />,
				<FaHeart />,
				data.user_has_liked,
				data.like_count,
				'Like this place',
				toggleLike,
				actionLoading.like
			)}

			{/* Favorite Button */}
			{renderButton(
				<FaRegStar />,
				<FaStar />,
				data.user_has_favorited,
				data.favorite_count,
				'Favorite',
				toggleFavorite,
				actionLoading.favorite
			)}

			{/* Visit Button */}
			{renderButton(
				<FaMapMarkerAlt />,
				<FaMapMarkerAlt />,
				data.user_has_visited,
				data.visit_count,
				'Mark as visited',
				toggleVisit,
				actionLoading.visit
			)}

			{/* Write Review Button */}
			{onWriteReview &&
				renderButton(
					<FaEdit />,
					<FaEdit />,
					false, // Reviews don't have an "active" state like likes/favorites
					data.review_count,
					'Write Review',
					onWriteReview,
					false // Reviews don't have loading state
				)}
		</div>
	);
}
