/** @format */

'use client';

import { useSimpleReviews } from '@/app/shared/userInteractions/hooks/useSimpleReviews';
import { POIIdentifier, POIType } from '@/app/shared/userInteractions/shared/types/base';
import React, { useMemo, useState } from 'react';
import {
	FaCalendar,
	FaChevronDown,
	FaChevronUp,
	FaEdit,
	FaStar,
	FaThumbsUp,
	FaTrash,
	FaUser,
} from 'react-icons/fa';

interface SimpleReviewSectionProps {
	poiId?: number | null;
	poiType: POIType;
	userPoiTempId?: number | null;
	userPoiApprovedId?: number | null;
	
	// Options
	showWriteButton?: boolean;
	onWriteReview?: () => void;
	maxInitialReviews?: number;
}

export default function SimpleReviewSection({
	poiId,
	poiType,
	userPoiTempId,
	userPoiApprovedId,
	showWriteButton = true,
	onWriteReview,
	maxInitialReviews = 5,
}: SimpleReviewSectionProps) {
	const [isExpanded, setIsExpanded] = useState(false);
	const [hasLoadedReviews, setHasLoadedReviews] = useState(false);

	// Create POI identifier
	const poiIdentifier: POIIdentifier = useMemo(
		() => ({
			poi_id: poiId,
			user_poi_temp_id: userPoiTempId,
			user_poi_approved_id: userPoiApprovedId,
			poi_type: poiType,
		}),
		[poiId, userPoiTempId, userPoiApprovedId, poiType]
	);

	// Use the simple reviews hook (lazy loading)
	const {
		reviews,
		stats,
		loading,
		loadingMore,
		hasMore,
		error,
		loadReviews,
		loadMore,
		toggleHelpful,
	} = useSimpleReviews({
		poi_identifier: poiIdentifier,
		auto_load: false, // Lazy loading
		limit: maxInitialReviews,
	});

	// Load reviews when expanded for the first time
	const handleToggleExpanded = async () => {
		if (!isExpanded && !hasLoadedReviews) {
			setHasLoadedReviews(true);
			await loadReviews();
		}
		setIsExpanded(!isExpanded);
	};

	// Render star rating
	const renderStars = (rating: number, size: 'sm' | 'md' = 'sm') => {
		const starSize = size === 'sm' ? 'w-3 h-3' : 'w-4 h-4';
		return (
			<div className='flex items-center gap-1'>
				{[1, 2, 3, 4, 5].map((star) => (
					<FaStar
						key={star}
						className={`${starSize} ${
							star <= rating ? 'text-yellow-500' : 'text-gray-300'
						}`}
					/>
				))}
			</div>
		);
	};

	// Render individual review
	const renderReview = (review: any) => (
		<div key={review.id} className='border-b border-gray-200 pb-4 mb-4 last:border-b-0'>
			{/* Review Header */}
			<div className='flex items-start justify-between mb-2'>
				<div className='flex items-center gap-3'>
					{/* User Avatar */}
					<div className='w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center'>
						{review.user_avatar ? (
							<img
								src={review.user_avatar}
								alt={review.username || review.user_name}
								className='w-8 h-8 rounded-full object-cover'
							/>
						) : (
							<FaUser className='w-4 h-4 text-gray-600' />
						)}
					</div>
					
					{/* User Info */}
					<div>
						<div className='font-medium text-gray-900'>
							{review.username || review.user_name || 'Anonymous'}
						</div>
						<div className='flex items-center gap-2 text-sm text-gray-500'>
							{renderStars(review.rating)}
							<span>•</span>
							<FaCalendar className='w-3 h-3' />
							<span>{new Date(review.created_at).toLocaleDateString()}</span>
						</div>
					</div>
				</div>
				
				{/* Review Actions */}
				{review.is_own_review && (
					<div className='flex items-center gap-2'>
						<button
							className='text-gray-400 hover:text-blue-500 transition-colors'
							title='Edit Review'>
							<FaEdit className='w-4 h-4' />
						</button>
						<button
							className='text-gray-400 hover:text-red-500 transition-colors'
							title='Delete Review'>
							<FaTrash className='w-4 h-4' />
						</button>
					</div>
				)}
			</div>

			{/* Review Title */}
			{review.review_title && (
				<h4 className='font-medium text-gray-900 mb-2'>{review.review_title}</h4>
			)}

			{/* Review Text */}
			<p className='text-gray-700 mb-3'>{review.review_text}</p>

			{/* Review Footer */}
			<div className='flex items-center justify-between'>
				<div className='flex items-center gap-4 text-sm text-gray-500'>
					{review.visit_date && (
						<span>Visited: {new Date(review.visit_date).toLocaleDateString()}</span>
					)}
					{review.is_verified && (
						<span className='text-green-600 font-medium'>✓ Verified</span>
					)}
				</div>
				
				{/* Helpful Button */}
				<button
					onClick={() => toggleHelpful(review.id)}
					className={`flex items-center gap-1 px-2 py-1 rounded transition-colors ${
						review.user_helpful_vote === 'up'
							? 'bg-blue-100 text-blue-700'
							: 'text-gray-500 hover:text-blue-600'
					}`}>
					<FaThumbsUp className='w-3 h-3' />
					<span className='text-sm'>{review.helpful_votes}</span>
				</button>
			</div>
		</div>
	);

	return (
		<div className='bg-white rounded-lg border border-gray-200'>
			{/* Header */}
			<div className='p-4 border-b border-gray-200'>
				<div className='flex items-center justify-between'>
					<div className='flex items-center gap-4'>
						<h3 className='text-lg font-semibold text-gray-900'>Reviews</h3>
						{stats.total_count > 0 && (
							<div className='flex items-center gap-2'>
								{renderStars(Math.round(stats.average_rating), 'md')}
								<span className='text-sm text-gray-600'>
									{stats.average_rating.toFixed(1)} ({stats.total_count} reviews)
								</span>
							</div>
						)}
					</div>
					
					{/* Write Review Button */}
					{showWriteButton && onWriteReview && (
						<button
							onClick={onWriteReview}
							className='px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors'>
							Write Review
						</button>
					)}
				</div>
			</div>

			{/* Content */}
			<div className='p-4'>
				{stats.total_count === 0 ? (
					<div className='text-center py-8 text-gray-500'>
						<p>No reviews yet. Be the first to review this place!</p>
					</div>
				) : (
					<>
						{/* Toggle Button */}
						<button
							onClick={handleToggleExpanded}
							className='flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors mb-4'>
							{isExpanded ? <FaChevronUp /> : <FaChevronDown />}
							<span>
								{isExpanded ? 'Hide Reviews' : `Show Reviews (${stats.total_count})`}
							</span>
						</button>

						{/* Reviews List */}
						{isExpanded && (
							<div>
								{loading && !hasLoadedReviews ? (
									<div className='flex items-center justify-center py-8'>
										<div className='animate-spin rounded-full h-6 w-6 border-2 border-blue-600 border-t-transparent' />
										<span className='ml-2 text-gray-600'>Loading reviews...</span>
									</div>
								) : error ? (
									<div className='text-center py-8 text-red-600'>
										<p>Error loading reviews: {error}</p>
									</div>
								) : (
									<>
										{/* Reviews */}
										<div className='space-y-4'>
											{reviews.map(renderReview)}
										</div>

										{/* Load More Button */}
										{hasMore && (
											<div className='text-center mt-6'>
												<button
													onClick={loadMore}
													disabled={loadingMore}
													className='px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50'>
													{loadingMore ? 'Loading...' : 'Load More Reviews'}
												</button>
											</div>
										)}
									</>
								)}
							</div>
						)}
					</>
				)}
			</div>
		</div>
	);
}
