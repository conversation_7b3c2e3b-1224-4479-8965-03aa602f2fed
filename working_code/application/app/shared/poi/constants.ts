/**
 * POI-related constants and field definitions
 *
 * @format
 */

// Fields that can be edited when submitting or updating POIs
export const POI_EDITABLE_FIELDS = [
	'name',
	'name_en',
	'name_tr',
	'name_uk',
	'name_de',
	'name_ru',
	'name_ar',
	'category',
	'subcategory',
	'cuisine',
	'full_address',
	'street',
	'neighborhood',
	'district',
	'city',
	'province',
	'country',
	'phone_number',
	'opening_hours',
	'description',
	'latitude',
	'longitude',
];

// POI categories and subcategories structure with importance rankings (1-10)
export const POI_CATEGORIES_DATA = {
	'Food & Drink': {
		subcategories: [
			{ name: 'Cafe', importance: 9 },
			{ name: 'Restaurant', importance: 10 },
			{ name: 'Bar', importance: 7 },
			{ name: 'Street Food', importance: 8 },
			{ name: 'Fine Dining', importance: 6 },
			{ name: 'Bakery', importance: 5 },
			{ name: 'Fast Food', importance: 7 },
			{ name: 'Food Truck', importance: 4 },
			{ name: 'Brewery', importance: 5 },
			{ name: 'Wine Bar', importance: 4 },
			{ name: 'Cocktail Bar', importance: 6 },
			{ name: 'Rooftop Bar', importance: 3 },
		],
	},
	'Cultural & Creative Experiences': {
		subcategories: [
			{ name: 'Museum Visit', importance: 9 },
			{ name: 'Art Gallery Walk', importance: 8 },
			{ name: 'Theater', importance: 7 },
			{ name: 'Concert', importance: 8 },
			{ name: 'Festival', importance: 6 },
			{ name: 'Workshop', importance: 5 },
			{ name: 'Art Studio', importance: 4 },
			{ name: 'Cultural Center', importance: 6 },
			{ name: 'Opera House', importance: 5 },
			{ name: 'Dance Performance', importance: 6 },
			{ name: 'Poetry Reading', importance: 3 },
			{ name: 'Book Reading', importance: 4 },
		],
	},
	'Sports & Fitness': {
		subcategories: [
			{ name: 'Gym', importance: 9 },
			{ name: 'Yoga Studio', importance: 8 },
			{ name: 'Swimming', importance: 8 },
			{ name: 'Rock Climbing', importance: 6 },
			{ name: 'Tennis', importance: 7 },
			{ name: 'Basketball', importance: 7 },
			{ name: 'Football', importance: 8 },
			{ name: 'Running Track', importance: 6 },
			{ name: 'Martial Arts', importance: 5 },
			{ name: 'Pilates', importance: 6 },
			{ name: 'CrossFit', importance: 5 },
			{ name: 'Boxing', importance: 4 },
		],
	},
	Entertainment: {
		subcategories: [
			{ name: 'Cinema', importance: 9 },
			{ name: 'Bowling', importance: 6 },
			{ name: 'Karaoke', importance: 7 },
			{ name: 'Gaming', importance: 8 },
			{ name: 'Comedy Club', importance: 5 },
			{ name: 'Dance Club', importance: 7 },
			{ name: 'Arcade', importance: 6 },
			{ name: 'Mini Golf', importance: 4 },
			{ name: 'Escape Room', importance: 6 },
			{ name: 'Board Game Cafe', importance: 5 },
			{ name: 'Pool Hall', importance: 4 },
			{ name: 'Laser Tag', importance: 3 },
		],
	},
	'Shopping & Markets': {
		subcategories: [
			{ name: 'Mall', importance: 9 },
			{ name: 'Boutique', importance: 6 },
			{ name: 'Market', importance: 8 },
			{ name: 'Vintage Store', importance: 5 },
			{ name: 'Bookstore', importance: 7 },
			{ name: 'Electronics', importance: 7 },
			{ name: 'Farmers Market', importance: 6 },
			{ name: 'Antique Shop', importance: 4 },
			{ name: 'Jewelry Store', importance: 5 },
			{ name: 'Clothing Store', importance: 8 },
			{ name: 'Shoe Store', importance: 6 },
			{ name: 'Gift Shop', importance: 4 },
		],
	},
	'Outdoor & Nature': {
		subcategories: [
			{ name: 'Park', importance: 10 },
			{ name: 'Beach', importance: 9 },
			{ name: 'Hiking Trail', importance: 8 },
			{ name: 'Garden', importance: 7 },
			{ name: 'Lake', importance: 8 },
			{ name: 'Forest', importance: 7 },
			{ name: 'Mountain', importance: 6 },
			{ name: 'Waterfall', importance: 5 },
			{ name: 'Botanical Garden', importance: 6 },
			{ name: 'Nature Reserve', importance: 5 },
			{ name: 'Camping', importance: 6 },
			{ name: 'Picnic Area', importance: 7 },
		],
	},
	'Wellness & Beauty': {
		subcategories: [
			{ name: 'Spa', importance: 8 },
			{ name: 'Massage', importance: 7 },
			{ name: 'Hair Salon', importance: 9 },
			{ name: 'Nail Salon', importance: 6 },
			{ name: 'Beauty Clinic', importance: 5 },
			{ name: 'Wellness Center', importance: 7 },
			{ name: 'Meditation Center', importance: 6 },
			{ name: 'Sauna', importance: 5 },
			{ name: 'Hot Springs', importance: 4 },
			{ name: 'Acupuncture', importance: 4 },
			{ name: 'Chiropractor', importance: 5 },
			{ name: 'Dermatologist', importance: 6 },
		],
	},
	Transportation: {
		subcategories: [
			{ name: 'Metro Station', importance: 10 },
			{ name: 'Bus Stop', importance: 9 },
			{ name: 'Airport', importance: 8 },
			{ name: 'Train Station', importance: 8 },
			{ name: 'Taxi Stand', importance: 7 },
			{ name: 'Car Rental', importance: 6 },
			{ name: 'Bike Rental', importance: 7 },
			{ name: 'Ferry Terminal', importance: 5 },
			{ name: 'Parking', importance: 8 },
			{ name: 'Gas Station', importance: 7 },
			{ name: 'Electric Charging', importance: 6 },
			{ name: 'Scooter Rental', importance: 5 },
		],
	},
};

// Helper functions to get categories and subcategories
export const getPOICategories = (): string[] => {
	return Object.keys(POI_CATEGORIES_DATA);
};

export const getPOISubcategories = (category?: string): string[] => {
	if (!category) {
		// Return all subcategories if no category specified
		return Object.values(POI_CATEGORIES_DATA).flatMap((cat) =>
			cat.subcategories.map((sub) => sub.name)
		);
	}

	const categoryData =
		POI_CATEGORIES_DATA[category as keyof typeof POI_CATEGORIES_DATA];
	return categoryData ? categoryData.subcategories.map((sub) => sub.name) : [];
};

// Helper function to get subcategory with importance
export const getPOISubcategoriesWithImportance = (category?: string) => {
	if (!category) {
		// Return all subcategories with importance if no category specified
		return Object.values(POI_CATEGORIES_DATA).flatMap(
			(cat) => cat.subcategories
		);
	}

	const categoryData =
		POI_CATEGORIES_DATA[category as keyof typeof POI_CATEGORIES_DATA];
	return categoryData ? categoryData.subcategories : [];
};

// Helper function to get importance for a specific subcategory
export const getSubcategoryImportance = (subcategoryName: string): number => {
	for (const category of Object.values(POI_CATEGORIES_DATA)) {
		const subcategory = category.subcategories.find(
			(sub) => sub.name === subcategoryName
		);
		if (subcategory) {
			return subcategory.importance;
		}
	}
	return 5; // Default importance if not found
};

// Legacy exports for backward compatibility
export const POI_CATEGORIES = getPOICategories();
export const POI_SUBCATEGORY_OPTIONS = getPOISubcategories();

// Submission reasons for POI reports/updates
export const POI_SUBMISSION_REASONS = [
	{ value: 'new_poi', label: 'New POI' },
	{ value: 'missing_info', label: 'Missing Information' },
	{ value: 'incorrect_info', label: 'Incorrect Information' },
	{ value: 'closed_business', label: 'Business Closed' },
	{ value: 'moved_location', label: 'Location Changed' },
];

// Landing Page Constants
export const LANDING_PAGE_DATA = {
	// Company branding
	branding: {
		companyName: 'Wizlop',
		tagline: 'Explore Life, Experience Everything',
		description:
			'Your gateway to discovering amazing places, hidden gems, and unforgettable experiences worldwide',
		heroTitle: 'Explore Life, Experience Everything',
		heroDescription:
			'Discover amazing places through our advanced spatial search algorithms, intelligent location matching, and lightning-fast geospatial technology. From AI-powered conversations to precision distance calculations - find your next adventure with ease.',
	},

	// Feature badges - will be dynamically populated with actual counts
	getFeatureBadges: () => {
		const totalCategories = Object.keys(POI_CATEGORIES_DATA).length;
		const totalSubcategories = Object.values(POI_CATEGORIES_DATA).reduce(
			(total, category) => total + category.subcategories.length,
			0
		);
		return [
			{
				icon: 'FiGlobe',
				text: `${totalCategories} Categories • ${totalSubcategories} Experiences`,
			},
			{ icon: 'FiZap', text: 'Lightning-Fast Spatial Search' },
			{ icon: 'FiTarget', text: 'Precision Location Matching' },
			{ icon: 'FiMessageCircle', text: 'AI-Enhanced Discovery' },
		];
	},

	// Live search preview data
	searchPreview: {
		title: 'Live Search Preview',
		subtitle: 'Real locations in Turkey',
		locations: [
			{ name: 'Karaköy Lokantası', type: 'Restaurant', rating: 4.8 },
			{ name: 'Galata Tower Cafe', type: 'Cafe', rating: 4.6 },
			{ name: 'Cihangir Yoga Studio', type: 'Wellness', rating: 4.9 },
		],
	},

	// AI conversation demo
	aiConversation: {
		title: 'AI Conversation',
		subtitle: 'Natural language search',
		userMessage: 'Find me a cozy cafe with good wifi in Istanbul',
		aiResponse:
			'I found 8 cozy cafes with excellent wifi in Istanbul. Here are the top 3 based on ambiance and connectivity...',
		typingIndicator: 'AI is typing...',
	},

	// Multiple conversation examples for looping animation
	conversationExamples: [
		{
			userMessage: 'Find me a cozy cafe with good wifi in Istanbul',
			aiResponse:
				'I found 8 cozy cafes with excellent wifi in Istanbul. Here are the top 3 based on ambiance and connectivity...',
		},
		{
			userMessage: 'Show me romantic restaurants for a date night',
			aiResponse:
				'Perfect! I found 12 romantic restaurants with intimate ambiance. Here are the most highly rated options...',
		},
		{
			userMessage: 'Where can I find outdoor activities near the Bosphorus?',
			aiResponse:
				'Great choice! I discovered 15 outdoor activities along the Bosphorus. Here are the most popular ones...',
		},
	],

	// Section headers
	sectionHeaders: {
		categoryExplorer: {
			badge: 'Explore Categories',
			title: 'Explore',
			subtitle: 'Every Experience',
			description:
				'From adrenaline-pumping adventures to peaceful retreats. Discover amazing locations powered by advanced spatial algorithms and lightning-fast geospatial technology.',
		},
		aiDemo: {
			badge: 'Advanced Technology',
			title: 'Powered by',
			subtitle: 'Smart Algorithms',
			description:
				'Experience our cutting-edge spatial search engine, PostGIS-powered distance calculations, intelligent location matching, and AI-enhanced discovery working together seamlessly.',
		},
		spatialTech: {
			badge: 'Spatial Technology',
			title: 'Lightning-Fast',
			subtitle: 'Location Discovery',
			description:
				'Our advanced spatial database with PostGIS indexing, sub-second distance calculations, and intelligent boundary detection delivers results faster than ever before.',
		},
	},

	// AI Demo scenarios
	aiDemoScenarios: [
		{
			id: 'cozy-cafe',
			userQuery:
				'I want a cozy cafe with good wifi near Galata Tower where I can work for a few hours',
			aiResponse:
				'I found 8 cozy cafes with excellent wifi near Galata Tower. Based on your work needs, here are the top 3 with quiet atmospheres and reliable internet...',
			searchResults: [
				{
					name: 'Karakoy Lokantasi Cafe',
					type: 'Cafe',
					rating: 4.8,
					distance: '200m from Galata Tower',
					features: ['Free WiFi', 'Quiet', 'Power Outlets', 'Coffee'],
				},
				{
					name: 'Galata Coffee Roasters',
					type: 'Coffee Shop',
					rating: 4.6,
					distance: '150m from Galata Tower',
					features: ['High-Speed WiFi', 'Work-Friendly', 'Specialty Coffee'],
				},
				{
					name: 'Minimalist Cafe',
					type: 'Cafe',
					rating: 4.7,
					distance: '300m from Galata Tower',
					features: ['Silent Zone', 'Fast WiFi', 'Laptop-Friendly'],
				},
			],
			processingSteps: [
				'Understanding: cozy cafe + good wifi + work environment',
				'Location: Galata Tower area (radius: 500m)',
				'Filtering: WiFi quality ratings > 4.0',
				'Ranking: Coziness score + Work-friendliness',
				'Context: Work duration (few hours) = quiet preference',
			],
			traditionalSteps: [
				"Select 'Cafe' from category dropdown",
				"Choose 'Galata Tower' area on map",
				"Filter by 'WiFi Available'",
				'Sort by rating or distance',
				'Manually check each result for work suitability',
			],
		},
		{
			id: 'romantic-dinner',
			userQuery:
				'Looking for a romantic restaurant with Bosphorus view for anniversary dinner',
			aiResponse:
				'Perfect! I found several romantic restaurants with stunning Bosphorus views. Here are 3 ideal spots for your anniversary celebration...',
			searchResults: [
				{
					name: 'Sunset Grill & Bar',
					type: 'Fine Dining',
					rating: 4.9,
					distance: 'Ulus, Bosphorus view',
					features: [
						'Bosphorus View',
						'Romantic',
						'Fine Dining',
						'Reservations',
					],
				},
				{
					name: 'Lacivert Restaurant',
					type: 'Seafood',
					rating: 4.7,
					distance: 'Anadolu Hisarı',
					features: ['Waterfront', 'Intimate', 'Seafood', 'Sunset View'],
				},
				{
					name: 'Feriye Palace Restaurant',
					type: 'Ottoman Cuisine',
					rating: 4.8,
					distance: 'Ortaköy',
					features: [
						'Historic',
						'Bosphorus View',
						'Elegant',
						'Special Occasions',
					],
				},
			],
			processingSteps: [
				'Understanding: romantic + restaurant + Bosphorus view + anniversary',
				'Location: Bosphorus waterfront areas',
				'Filtering: Romantic atmosphere + water view',
				'Ranking: Romance score + view quality + special occasion suitability',
				'Context: Anniversary = special occasion preferences',
			],
			traditionalSteps: [
				"Select 'Restaurant' category",
				"Filter by 'Waterfront' or 'View'",
				'Search multiple areas along Bosphorus',
				'Check individual photos for view quality',
				'Read reviews for romantic atmosphere confirmation',
			],
		},
	],
};
