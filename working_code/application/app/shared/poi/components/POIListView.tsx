/** @format */

'use client';

import { colors } from '@/app/colors';
import React, { useState } from 'react';

interface POI {
	poi_type: string;
	poi_id: number | null;
	temp_id: number | null;
	approved_id: number | null;
	name?: string;
	name_en?: string;
	name_de?: string;
	name_ru?: string;
	name_ar?: string;
	category?: string;
	subcategory?: string;
	latitude?: number;
	longitude?: number;
	address?: string;
	city?: string;
	district?: string;
	country?: string;
	phone_number?: string;
	opening_hours?: string;
	is_favorite?: boolean;
	neighborhood?: string;
	like_count?: number;
	favorite_count?: number;
	visit_count?: number;
	review_count?: number;
	media_count?: number;
	user_has_liked?: boolean;
	user_has_favorited?: boolean;
	user_has_visited?: boolean;
	created_at?: string;
	updated_at?: string;
	description?: string;
	rating?: number;
	media?: Array<{ url: string; type: string }>;
}

interface POIListViewProps {
	pois: POI[];
	onPOIClick: (poi: POI) => void;
	onToggleFavorite?: (poi: POI) => void;
	interactions?: Record<string, any>;
}

interface ImageModalProps {
	isOpen: boolean;
	imageUrl: string;
	onClose: () => void;
	poiName: string;
}

const ImageModal: React.FC<ImageModalProps> = ({
	isOpen,
	imageUrl,
	onClose,
	poiName,
}) => {
	if (!isOpen) return null;

	return (
		<div className='fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75'>
			<div className='relative max-w-4xl max-h-[90vh] p-4'>
				<button
					onClick={onClose}
					className='absolute top-2 right-2 z-10 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-75 transition-all duration-200'>
					<svg
						className='w-6 h-6'
						fill='none'
						stroke='currentColor'
						viewBox='0 0 24 24'>
						<path
							strokeLinecap='round'
							strokeLinejoin='round'
							strokeWidth={2}
							d='M6 18L18 6M6 6l12 12'
						/>
					</svg>
				</button>
				<img
					src={imageUrl}
					alt={poiName}
					className='max-w-full max-h-full object-contain rounded-lg'
				/>
				<div className='absolute bottom-4 left-4 right-4 text-center'>
					<p className='text-white text-lg font-semibold bg-black bg-opacity-50 rounded-lg px-4 py-2'>
						{poiName}
					</p>
				</div>
			</div>
		</div>
	);
};

export const POIListView: React.FC<POIListViewProps> = ({
	pois,
	onPOIClick,
	onToggleFavorite,
	interactions = {},
}) => {
	const [sortColumn, setSortColumn] = useState<string>('name');
	const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
	const [selectedImageModal, setSelectedImageModal] = useState<{
		url: string;
		name: string;
	} | null>(null);

	const handleSort = (column: string) => {
		if (sortColumn === column) {
			setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
		} else {
			setSortColumn(column);
			setSortDirection('asc');
		}
	};

	const sortedPois = [...pois].sort((a, b) => {
		let aValue = '';
		let bValue = '';

		switch (sortColumn) {
			case 'name':
				aValue = a.name || '';
				bValue = b.name || '';
				break;
			case 'category':
				aValue = a.category || '';
				bValue = b.category || '';
				break;
			case 'location':
				aValue = `${a.city || ''}, ${a.country || ''}`;
				bValue = `${b.city || ''}, ${b.country || ''}`;
				break;
			case 'rating':
				return sortDirection === 'asc'
					? (a.rating || 0) - (b.rating || 0)
					: (b.rating || 0) - (a.rating || 0);
			case 'date':
				aValue = a.created_at || '';
				bValue = b.created_at || '';
				break;
			default:
				aValue = a.name || '';
				bValue = b.name || '';
		}

		if (sortDirection === 'asc') {
			return aValue.localeCompare(bValue);
		} else {
			return bValue.localeCompare(aValue);
		}
	});

	const formatDate = (dateString?: string) => {
		if (!dateString) return '-';
		return new Date(dateString).toLocaleDateString();
	};

	const formatLocation = (poi: POI) => {
		const parts = [
			poi.neighborhood,
			poi.district,
			poi.city,
			poi.country,
		].filter(Boolean);
		return parts.join(', ') || '-';
	};

	const getFirstImage = (poi: POI) => {
		return poi.media?.find((m) => m.type === 'image')?.url;
	};

	const SortIcon = ({ column }: { column: string }) => (
		<svg
			className={`w-4 h-4 ml-1 transition-transform duration-200 ${
				sortColumn === column && sortDirection === 'desc' ? 'rotate-180' : ''
			}`}
			fill='none'
			stroke='currentColor'
			viewBox='0 0 24 24'>
			<path
				strokeLinecap='round'
				strokeLinejoin='round'
				strokeWidth={2}
				d='M19 9l-7 7-7-7'
			/>
		</svg>
	);

	return (
		<>
			<div
				className='w-full overflow-hidden rounded-lg border'
				style={{ borderColor: colors.ui.gray200 }}>
				{/* Header */}
				<div
					className='grid grid-cols-12 gap-4 px-4 py-3 border-b font-semibold text-sm sticky top-0 z-10'
					style={{
						backgroundColor: colors.ui.gray50,
						borderColor: colors.ui.gray200,
						color: colors.neutral.textBlack,
					}}>
					<div className='col-span-1 text-center'>Image</div>
					<div
						className='col-span-3 flex items-center cursor-pointer hover:opacity-70 transition-opacity'
						onClick={() => handleSort('name')}>
						Name
						{sortColumn === 'name' && <SortIcon column='name' />}
					</div>
					<div
						className='col-span-2 flex items-center cursor-pointer hover:opacity-70 transition-opacity'
						onClick={() => handleSort('category')}>
						Category
						{sortColumn === 'category' && <SortIcon column='category' />}
					</div>
					<div
						className='col-span-3 flex items-center cursor-pointer hover:opacity-70 transition-opacity'
						onClick={() => handleSort('location')}>
						Location
						{sortColumn === 'location' && <SortIcon column='location' />}
					</div>
					<div
						className='col-span-1 flex items-center cursor-pointer hover:opacity-70 transition-opacity'
						onClick={() => handleSort('rating')}>
						Rating
						{sortColumn === 'rating' && <SortIcon column='rating' />}
					</div>
					<div
						className='col-span-2 flex items-center cursor-pointer hover:opacity-70 transition-opacity'
						onClick={() => handleSort('date')}>
						Date Added
						{sortColumn === 'date' && <SortIcon column='date' />}
					</div>
				</div>

				{/* Rows - No fixed height, integrates with page scroll */}
				<div>
					{sortedPois.map((poi, index) => {
						const poiId =
							poi.poi_id || poi.approved_id || poi.temp_id || `poi-${index}`;
						const firstImage = getFirstImage(poi);

						return (
							<div
								key={poiId}
								className='grid grid-cols-12 gap-4 px-4 py-3 border-b hover:bg-gray-50 transition-colors duration-200 cursor-pointer'
								style={{ borderColor: colors.ui.gray100 }}
								onClick={() => onPOIClick(poi)}>
								{/* Image Column */}
								<div className='col-span-1 flex justify-center'>
									{firstImage ? (
										<button
											onClick={(e) => {
												e.stopPropagation();
												setSelectedImageModal({
													url: firstImage,
													name: poi.name || poi.name_en || 'Unnamed POI',
												});
											}}
											className='w-8 h-8 rounded-md overflow-hidden hover:scale-110 transition-transform duration-200'
											style={{ backgroundColor: colors.ui.gray100 }}>
											<img
												src={firstImage}
												alt={poi.name || poi.name_en || 'POI Image'}
												className='w-full h-full object-cover'
											/>
										</button>
									) : (
										<div
											className='w-8 h-8 rounded-md flex items-center justify-center'
											style={{ backgroundColor: colors.ui.gray100 }}>
											<svg
												className='w-4 h-4'
												fill='none'
												stroke='currentColor'
												viewBox='0 0 24 24'
												style={{ color: colors.neutral.slateGray }}>
												<path
													strokeLinecap='round'
													strokeLinejoin='round'
													strokeWidth={2}
													d='M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z'
												/>
											</svg>
										</div>
									)}
								</div>

								{/* Name Column */}
								<div className='col-span-3 flex items-center'>
									<span
										className='font-medium text-sm truncate'
										style={{ color: colors.neutral.textBlack }}>
										{poi.name || poi.name_en || 'Unnamed POI'}
									</span>
								</div>

								{/* Category Column */}
								<div className='col-span-2 flex items-center'>
									<span
										className='text-sm truncate'
										style={{ color: colors.neutral.slateGray }}>
										{poi.category || '-'}
									</span>
								</div>

								{/* Location Column */}
								<div className='col-span-3 flex items-center'>
									<span
										className='text-sm truncate'
										style={{ color: colors.neutral.slateGray }}>
										{formatLocation(poi)}
									</span>
								</div>

								{/* Rating Column */}
								<div className='col-span-1 flex items-center justify-center'>
									{poi.rating ? (
										<div className='flex items-center gap-1'>
											<span
												className='text-sm font-medium'
												style={{ color: colors.neutral.textBlack }}>
												{poi.rating.toFixed(1)}
											</span>
											<svg
												className='w-3 h-3'
												fill='currentColor'
												viewBox='0 0 20 20'
												style={{ color: '#F59E0B' }}>
												<path d='M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z' />
											</svg>
										</div>
									) : (
										<span
											className='text-sm'
											style={{ color: colors.neutral.slateGray }}>
											-
										</span>
									)}
								</div>

								{/* Date Column */}
								<div className='col-span-2 flex items-center'>
									<span
										className='text-sm'
										style={{ color: colors.neutral.slateGray }}>
										{formatDate(poi.created_at)}
									</span>
								</div>
							</div>
						);
					})}
				</div>
			</div>

			{/* Image Modal */}
			<ImageModal
				isOpen={!!selectedImageModal}
				imageUrl={selectedImageModal?.url || ''}
				poiName={selectedImageModal?.name || ''}
				onClose={() => setSelectedImageModal(null)}
			/>
		</>
	);
};
