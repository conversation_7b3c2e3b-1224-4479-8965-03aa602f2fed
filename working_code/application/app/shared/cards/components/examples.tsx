'use client'

/**
 * Example usage of shared POI cards
 * This file demonstrates how to use the shared POI card components
 * across different scenarios and pages.
 */

import React, { useState } from 'react'
import { LocationHoverCard, POICard, BasePOI, LocationData } from './index'

// Example data
const exampleLocation: LocationData = {
  id: "example-location",
  name: "Central Park",
  lat: 40.7829,
  lng: -73.9654,
  confidence: 0.95,
  address: "Central Park, New York, NY",
  walk_route_distance_m: 500
}

const examplePOI: BasePOI = {
  id: 123,
  poi_type: "official",
  poi_id: 123,
  name: "Amazing Restaurant",
  category: "restaurant",
  subcategory: "fine_dining",
  city: "New York",
  district: "Manhattan",
  neighborhood: "Upper East Side",
  latitude: 40.7829,
  longitude: -73.9654,
  phone_number: "+****************",
  opening_hours: "Mon-Fri: 11:00 AM - 10:00 PM\nSat-Sun: 10:00 AM - 11:00 PM",
  description: "A wonderful fine dining restaurant with amazing views and exceptional service.",
  user_rating_avg: 4.5,
  user_rating_count: 120,
  distance_km: 0.5,
  is_favorite: false
}

const POICardExamples: React.FC = () => {
  const [showLocationHover, setShowLocationHover] = useState(false)
  const [showPOIModal, setShowPOIModal] = useState(false)
  const [hoverPosition, setHoverPosition] = useState({ x: 100, y: 100 })

  const handleMouseEnter = (event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect()
    setHoverPosition({
      x: rect.right + 10,
      y: rect.top
    })
    setShowLocationHover(true)
  }

  const handleNavigate = (poi: BasePOI) => {
    console.log('Navigate to POI:', poi)
    alert(`Navigating to ${poi.name}`)
  }

  const handleFavoriteToggle = (poi: BasePOI) => {
    console.log('Toggle favorite for POI:', poi)
    alert(`${poi.is_favorite ? 'Removed from' : 'Added to'} favorites: ${poi.name}`)
  }

  return (
    <div className="p-8 space-y-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-900">POI Cards Examples</h1>
      
      {/* Location Hover Card Example */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold text-gray-800">Location Hover Card</h2>
        <p className="text-gray-600">
          Used in chat messages when hovering over location mentions.
        </p>
        
        <div className="bg-gray-50 p-4 rounded-lg">
          <p className="text-gray-700">
            I visited{' '}
            <span
              className="text-blue-600 underline cursor-pointer"
              onMouseEnter={handleMouseEnter}
              onMouseLeave={() => setShowLocationHover(false)}
            >
              Central Park
            </span>
            {' '}yesterday and it was amazing!
          </p>
        </div>

        {/* Location Hover Card */}
        <LocationHoverCard
          location={exampleLocation}
          isVisible={showLocationHover}
          position={hoverPosition}
          onClose={() => setShowLocationHover(false)}
          onCardHoverChange={(hovering) => {
            if (!hovering) {
              setTimeout(() => setShowLocationHover(false), 100)
            }
          }}
        />
      </section>

      {/* POI Modal Card Example */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold text-gray-800">POI Modal Card</h2>
        <p className="text-gray-600">
          Used in flat-map and globe pages for displaying selected POI details.
        </p>
        
        <button
          onClick={() => setShowPOIModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Show POI Modal
        </button>

        {/* POI Modal Card */}
        <POICard
          poi={examplePOI}
          isVisible={showPOIModal}
          onClose={() => setShowPOIModal(false)}
          onNavigate={handleNavigate}
          onFavoriteToggle={handleFavoriteToggle}
          variant="modal"
          showActions={true}
        />
      </section>

      {/* POI Inline Card Example */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold text-gray-800">POI Inline Card</h2>
        <p className="text-gray-600">
          Used in POI listing pages and search results.
        </p>
        
        <div className="max-w-md">
          <POICard
            poi={examplePOI}
            isVisible={true}
            onClose={() => {}}
            onNavigate={handleNavigate}
            onFavoriteToggle={handleFavoriteToggle}
            variant="inline"
            showActions={true}
          />
        </div>
      </section>

      {/* Grid Example */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold text-gray-800">POI Grid Layout</h2>
        <p className="text-gray-600">
          Multiple POI cards in a grid layout for listing pages.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3].map((index) => (
            <POICard
              key={index}
              poi={{
                ...examplePOI,
                id: index,
                name: `Restaurant ${index}`,
                user_rating_avg: 4.0 + (index * 0.2),
                user_rating_count: 50 + (index * 20)
              }}
              isVisible={true}
              onClose={() => {}}
              onNavigate={handleNavigate}
              onFavoriteToggle={handleFavoriteToggle}
              variant="inline"
              showActions={true}
            />
          ))}
        </div>
      </section>

      {/* Usage Code Examples */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold text-gray-800">Code Examples</h2>
        
        <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
          <pre className="text-sm">
{`// Import shared components
import { LocationHoverCard, POICard } from '@/app/shared/cards'

// Location Hover Card (Chat)
<LocationHoverCard
  location={locationData}
  isVisible={showCard}
  position={{ x: mouseX, y: mouseY }}
  onClose={() => setShowCard(false)}
  onCardHoverChange={(hovering) => setIsHovering(hovering)}
/>

// POI Modal Card (Flat Map)
<POICard
  poi={selectedPOI}
  isVisible={showPOI}
  onClose={() => setShowPOI(false)}
  onNavigate={(poi) => router.push(\`/pois/\${poi.id}\`)}
  variant="modal"
/>

// POI Inline Card (Listing)
<POICard
  poi={poi}
  isVisible={true}
  onClose={() => {}}
  onNavigate={handleNavigate}
  variant="inline"
  showActions={true}
/>`}
          </pre>
        </div>
      </section>
    </div>
  )
}

export default POICardExamples
