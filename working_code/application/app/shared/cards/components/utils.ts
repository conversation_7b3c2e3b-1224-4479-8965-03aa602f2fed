/**
 * Shared utilities for POI cards
 *
 * @format
 */

import { BasePOI, LocationData } from './types';

/**
 * Format distance for display
 */
export const formatDistance = (
	distanceKm?: number,
	distanceM?: number
): string => {
	if (distanceM !== undefined) {
		if (distanceM < 1000) {
			return `${Math.round(distanceM)}m walk`;
		}
		return `${(distanceM / 1000).toFixed(1)}km walk`;
	}

	if (distanceKm !== undefined) {
		if (distanceKm < 1) {
			return `${Math.round(distanceKm * 1000)}m away`;
		}
		return `${distanceKm.toFixed(1)}km away`;
	}

	return '';
};

/**
 * Format rating for display
 */
export const formatRating = (rating?: number, count?: number): string => {
	if (!rating) return 'No ratings';
	return `${rating.toFixed(1)} (${count || 0} reviews)`;
};

/**
 * Get location string from POI data
 */
export const getLocationString = (poi: BasePOI): string => {
	const parts = [poi.neighborhood, poi.district, poi.city, poi.country].filter(
		Boolean
	);
	return parts.join(', ') || 'Location not specified';
};

/**
 * Get category icon for POI
 */
export const getCategoryIcon = (
	category: string | null | undefined
): string => {
	const iconMap: Record<string, string> = {
		restaurant: '🍽️',
		cafe: '☕',
		hotel: '🏨',
		attraction: '🎯',
		shopping: '🛍️',
		entertainment: '🎭',
		transport: '🚌',
		health: '🏥',
		education: '🎓',
		finance: '🏦',
		government: '🏛️',
		religious: '⛪',
		sports: '⚽',
		automotive: '🚗',
		beauty: '💄',
		food: '🍕',
		nightlife: '🌙',
		outdoors: '🌲',
		services: '🔧',
		default: '📍',
	};

	// Handle null, undefined, or empty category
	if (!category || typeof category !== 'string') {
		return iconMap.default;
	}

	return iconMap[category.toLowerCase()] || iconMap.default;
};

/**
 * Generate Google Maps URL for navigation
 */
export const generateMapsUrl = (lat: number, lng: number): string => {
	return `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
};

/**
 * Generate search URL for POI
 */
export const generateSearchUrl = (poi: BasePOI): string => {
	return `https://www.google.com/maps/search/?api=1&query=${poi.latitude},${poi.longitude}`;
};

/**
 * Copy coordinates to clipboard
 */
export const copyCoordinates = (lat: number, lng: number): Promise<void> => {
	const coords = `${lat}, ${lng}`;
	return navigator.clipboard.writeText(coords);
};

/**
 * Calculate optimal card position to avoid screen edges
 */
export const calculateCardPosition = (
	mouseX: number,
	mouseY: number,
	cardWidth: number = 260,
	cardHeight: number = 200,
	padding: number = 20
): { x: number; y: number } => {
	let x = mouseX + 15; // Offset from cursor
	let y = mouseY - cardHeight / 2;

	// Adjust horizontal position if card would go off-screen
	if (x + cardWidth > window.innerWidth - padding) {
		x = mouseX - cardWidth - 15; // Show on left side of cursor
	}
	if (x < padding) {
		x = padding;
	}

	// Adjust vertical position if card would go off-screen
	if (y + cardHeight > window.innerHeight - padding) {
		y = window.innerHeight - cardHeight - padding;
	}
	if (y < padding) {
		y = padding;
	}

	return { x, y };
};

/**
 * Convert LocationData to BasePOI format
 */
export const locationToPOI = (location: LocationData): BasePOI => {
	return {
		id: parseInt(location.id) || 0,
		name: location.name,
		category: 'location',
		latitude: location.lat,
		longitude: location.lng,
		distance_km: location.walk_route_distance_m
			? location.walk_route_distance_m / 1000
			: undefined,
	};
};

/**
 * Convert BasePOI to LocationData format
 */
export const poiToLocation = (poi: BasePOI): LocationData => {
	return {
		id: poi.id.toString(),
		name: poi.name,
		lat: poi.latitude,
		lng: poi.longitude,
		walk_route_distance_m: poi.distance_km ? poi.distance_km * 1000 : undefined,
	};
};

/**
 * Validate POI data
 */
export const validatePOI = (poi: unknown): poi is BasePOI => {
	if (!poi || typeof poi !== 'object' || poi === null) {
		return false;
	}

	const candidate = poi as Record<string, unknown>;

	return Boolean(
		typeof candidate.id !== 'undefined' &&
			typeof candidate.name === 'string' &&
			typeof candidate.category === 'string' &&
			typeof candidate.latitude === 'number' &&
			typeof candidate.longitude === 'number' &&
			!isNaN(candidate.latitude) &&
			!isNaN(candidate.longitude)
	);
};

/**
 * Validate location data
 */
export const validateLocation = (
	location: unknown
): location is LocationData => {
	if (!location || typeof location !== 'object' || location === null) {
		return false;
	}

	const candidate = location as Record<string, unknown>;

	return Boolean(
		typeof candidate.id === 'string' &&
			typeof candidate.name === 'string' &&
			typeof candidate.lat === 'number' &&
			typeof candidate.lng === 'number' &&
			!isNaN(candidate.lat) &&
			!isNaN(candidate.lng)
	);
};
