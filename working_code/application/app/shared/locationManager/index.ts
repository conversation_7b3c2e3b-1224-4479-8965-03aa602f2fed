/**
 * 🌍 Wizlop Location Manager
 * 
 * Central export point for all location-related functionality.
 * This provides a clean, organized interface to the location system.
 */

// Hooks
export { useLocationManager } from '@/app/shared/locationManager/hooks/useLocationManager'
export { useLocationSetup } from '@/app/shared/locationManager/hooks/useLocationSetup'

// Components
export { default as LocationSetup } from '@/app/shared/locationManager/components/LocationSetup'

// Utilities
export {
  canRequestLocation,
  setLocationRequestInProgress,
  isLocationRequestInProgress,
  validateCoordinates,
  calculateDistance,
  formatDistance
} from '@/app/shared/locationManager/utils/locationUtils'

// Types (re-export from hooks for convenience)
export type {
  LocationData,
  LocationSetupStatus,
  UseLocationManagerReturn
} from '@/app/shared/locationManager/hooks/useLocationManager'

export type {
  UseLocationSetupReturn
} from '@/app/shared/locationManager/hooks/useLocationSetup'
