'use client';

import React, { useState, useEffect } from 'react';
import { FaLocationArrow, FaCog, FaTimes, FaSync } from 'react-icons/fa';
import { useRouter } from 'next/navigation';
import { useLocationManager } from 'app/shared/locationManager/hooks/useLocationManager';
import { colors } from 'app/colors';

interface LocationSetupProps {
  isOpen: boolean;
  onClose?: () => void;
  onComplete: (choice: 'auto' | 'manual' | 'none') => void;
  title?: string;
  subtitle?: string;
  showCloseButton?: boolean;
  redirectAfterSetup?: string;
  pageContext?: 'chat' | 'globe' | 'welcome';
  isModal?: boolean; // Whether to show as modal overlay or full screen
  setCurrentLocation?: (location: {lat: number, lng: number}) => void; // For globe page
}

const LocationSetup: React.FC<LocationSetupProps> = ({
  isOpen,
  onClose,
  onComplete,
  title,
  subtitle,
  showCloseButton = true,
  redirectAfterSetup,
  pageContext = 'welcome',
  isModal = false,
  setCurrentLocation
}) => {
  const router = useRouter();

  // Use the central location manager
  const { requestAutoLocation, markSetupComplete, isLoading, error, location } = useLocationManager();

  const [hasTriedLocation, setHasTriedLocation] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastLocationTimestamp, setLastLocationTimestamp] = useState<number>(0);

  // Dynamic content based on page context
  const getContextualContent = () => {
    switch (pageContext) {
      case 'chat':
        return {
          icon: '💬',
          title: title || 'Location Setup Required',
          subtitle: subtitle || 'To provide better location-based responses, we need to know your location or you can set it manually.',
          returnPath: '/chat'
        };
      case 'globe':
        return {
          icon: '🌍',
          title: title || 'Location Setup Required',
          subtitle: subtitle || 'To explore the interactive globe, we need to know your location or you can set it manually.',
          returnPath: '/globe'
        };
      default:
        return {
          icon: '🌟',
          title: title || 'Welcome to Wizlop!',
          subtitle: subtitle || "Let's set up your location preferences to provide you with the best experience",
          returnPath: '/welcome'
        };
    }
  };

  const contextContent = getContextualContent();

  // Initialize the timestamp when component mounts to track existing location
  useEffect(() => {
    if (location && location.timestamp) {
      setLastLocationTimestamp(location.timestamp);
    }
  }, []); // Only run once on mount

  // Watch for location changes after requesting auto location
  useEffect(() => {
    // Only run effect logic when modal is open
    if (!isOpen) return;

    // Check if we got a new auto location after the user tried to get location
    if (hasTriedLocation && location && location.source === 'auto' && location.timestamp > lastLocationTimestamp) {
      // Location was successfully obtained
      markSetupComplete('auto');
      onComplete('auto');
      setIsProcessing(false);
      setLastLocationTimestamp(location.timestamp);

      // Note: Removed automatic page refresh/redirect here
      // The parent component (ChatPageComponent) will handle the modal closing
      // through the onComplete callback -> handleLocationSetupCompleteInternal -> setShowLocationSetup(false)
    } else if (isProcessing && error && hasTriedLocation) {
      // Location request failed - show error briefly then allow user to choose other options
      setIsProcessing(false);

      // If the error is permission denied, we could auto-close after showing the error
      // But it's better UX to let user see the error and choose an alternative
      // The error message will be shown and user can pick "Enter Location Manually" or "Continue Without Location"
    }
  }, [isOpen, isProcessing, location, error, hasTriedLocation, lastLocationTimestamp, markSetupComplete, onComplete, redirectAfterSetup, router]);

  // Early return after all hooks are declared
  if (!isOpen) return null;

  const handleRequestLocation = async () => {
    setHasTriedLocation(true);
    setIsProcessing(true);

    // Check if we already have a valid auto location before making the request
    if (location && location.source === 'auto') {
      markSetupComplete('auto');
      onComplete('auto');
      setIsProcessing(false);

      // Note: Removed automatic page refresh/redirect here
      // The parent component will handle the modal closing through the onComplete callback
      return;
    }

    try {
      await requestAutoLocation();

      // After the request, check again if we now have a valid auto location
      // This handles the case where requestAutoLocation succeeded
      // The useEffect will also handle this, but this provides immediate feedback
    } catch (error) {
      // Error is handled by the hook, user can try other options
      console.warn('Location request failed:', error);
      setIsProcessing(false);
      // Don't close the modal on error - let user try other options
    }
  };

  const handleManualSetup = () => {
    markSetupComplete('manual');
    onComplete('manual');
    // Note: Removed automatic redirect - let parent component handle navigation
    // The parent can decide whether to redirect to settings or handle it differently
  };

  const handleSkipLocation = () => {
    // For globe page, set default global view
    if (pageContext === 'globe' && setCurrentLocation) {
      setCurrentLocation({ lat: 0, lng: 0 });
    }
    markSetupComplete('none');
    onComplete('none');
  };

  const handleClose = () => {
    if (onClose) {
      onClose();
    } else if (showCloseButton) {
      // Default close behavior - go back
      router.back();
    }
  };

  // Determine container styling based on modal vs full screen
  const containerClass = isModal
    ? `fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 transition-opacity duration-300 ${
        isOpen ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'
      }`
    : `h-screen w-full overflow-hidden flex items-center justify-center`;

  const backgroundStyle = isModal
    ? {}
    : {
        background: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.supporting.softNavy} 100%)`
      };

  return (
    <div className={containerClass} style={backgroundStyle}>
      <div className={`${isModal ? 'bg-white' : 'backdrop-blur-sm'} rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden`}
           style={isModal ? {} : {
             backgroundColor: `${colors.neutral.cloudWhite}F5`,
             border: `1px solid ${colors.ui.gray200}`
           }}>
        {/* Header */}
        <div className={`${isModal ? 'bg-gradient-to-r from-blue-500 to-blue-600' : ''} p-6 ${isModal ? 'text-white' : ''} relative`}
             style={!isModal ? { color: colors.neutral.textBlack } : {}}>
          {showCloseButton && (
            <button
              onClick={handleClose}
              className={`absolute top-4 right-4 ${isModal ? 'text-white/80 hover:text-white' : 'text-gray-600 hover:text-gray-800'} transition-colors`}
            >
              <FaTimes size={20} />
            </button>
          )}
          <div className="text-center">
            <div className="text-6xl mb-4">{contextContent.icon}</div>
            <h2 className="text-2xl font-bold mb-4">{contextContent.title}</h2>
            <p className={`${isModal ? 'text-white/90' : ''} text-sm`}
               style={!isModal ? { color: colors.neutral.slateGray } : {}}>
              {contextContent.subtitle}
            </p>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-3">
          {error && hasTriedLocation && (
            <div className="rounded-lg p-3 text-sm"
                 style={{
                   backgroundColor: `${colors.utility.error}20`,
                   border: `1px solid ${colors.utility.error}40`,
                   color: colors.utility.error
                 }}>
              ⚠️ {error}
            </div>
          )}

          {/* Primary option - Auto location */}
          <button
            onClick={handleRequestLocation}
            disabled={isLoading || isProcessing}
            className="w-full text-white font-medium py-3 px-4 rounded-xl transition-colors flex items-center justify-center gap-2"
            style={{
              backgroundColor: (isLoading || isProcessing) ? colors.ui.gray400 : colors.brand.blue
            }}
            onMouseEnter={(e) => {
              if (!(isLoading || isProcessing)) {
                e.currentTarget.style.backgroundColor = colors.supporting.lightBlue;
              }
            }}
            onMouseLeave={(e) => {
              if (!(isLoading || isProcessing)) {
                e.currentTarget.style.backgroundColor = colors.brand.blue;
              }
            }}
          >
            {(isLoading || isProcessing) ? (
              <>
                <FaSync className="w-4 h-4 animate-spin" />
                Getting Location...
              </>
            ) : (
              <>
                <FaLocationArrow className="w-4 h-4" />
                Use My Current Location
              </>
            )}
          </button>

          {/* Secondary option - Manual entry */}
          <button
            onClick={handleManualSetup}
            className="w-full text-white font-medium py-3 px-4 rounded-xl transition-colors flex items-center justify-center gap-2"
            style={{
              backgroundColor: colors.neutral.slateGray
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = colors.neutral.textBlack;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = colors.neutral.slateGray;
            }}
          >
            <FaCog className="w-4 h-4" />
            Enter Location Manually
          </button>

          {/* Last resort - Continue without location */}
          <div className="text-center">
            <button
              onClick={handleSkipLocation}
              className="text-sm underline transition-colors"
              style={{ color: colors.neutral.slateGray }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = colors.neutral.textBlack;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = colors.neutral.slateGray;
              }}
            >
              Continue without location {pageContext === 'globe' ? '(global view)' : ''}
            </button>
          </div>

          {/* Go back button */}
          {showCloseButton && (
            <button
              onClick={handleClose}
              className="w-full font-medium py-3 px-4 rounded-xl transition-colors flex items-center justify-center gap-2"
              style={{
                backgroundColor: colors.ui.gray200,
                color: colors.neutral.textBlack
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = colors.ui.gray300;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = colors.ui.gray200;
              }}
            >
              <FaTimes className="w-4 h-4" />
              Go Back
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default LocationSetup;
