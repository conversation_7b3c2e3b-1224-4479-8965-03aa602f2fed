'use client';

import { useState, useEffect, useCallback } from 'react';
import { useLocationManager } from 'app/shared/locationManager/hooks/useLocationManager';

export interface UseLocationSetupReturn {
  showLocationSetup: boolean;
  setShowLocationSetup: (show: boolean) => void;
  triggerLocationSetupIfNeeded: () => boolean;
  handleLocationSetupComplete: (choice: 'auto' | 'manual' | 'none') => void;
  isLocationSetupRequired: boolean;
  locationSetupStatus: {
    hasSetup: boolean;
    setupChoice: 'auto' | 'manual' | 'none' | null;
    setupTimestamp: number;
  };
}

const TRIGGERED_SETUP_KEY = 'wizlop_location_setup_triggered';

export function useLocationSetup(): UseLocationSetupReturn {
  const {
    needsLocationSetup,
    markSetupComplete,
    isInitialized,
    setupStatus,
    location
  } = useLocationManager();

  const [showLocationSetup, setShowLocationSetup] = useState(false);
  const [hasTriggeredSetup, setHasTriggeredSetup] = useState(false);

  // Load triggered setup status from localStorage on mount
  useEffect(() => {
    if (typeof window === 'undefined') return;

    try {
      const triggered = localStorage.getItem(TRIGGERED_SETUP_KEY);
      if (triggered === 'true') {
        setHasTriggeredSetup(true);
      }
    } catch (error) {
      console.warn('Failed to load triggered setup status:', error);
    }
  }, []);

  // Reset triggered setup when setup status changes (setup completed)
  useEffect(() => {
    if (setupStatus.hasSetup) {
      setHasTriggeredSetup(false);
      if (typeof window !== 'undefined') {
        try {
          localStorage.removeItem(TRIGGERED_SETUP_KEY);
        } catch (error) {
          console.warn('Failed to clear triggered setup status:', error);
        }
      }
    }
  }, [setupStatus.hasSetup]);

  // Save triggered setup status to localStorage
  const setTriggeredSetup = useCallback((triggered: boolean) => {
    setHasTriggeredSetup(triggered);
    if (typeof window !== 'undefined') {
      try {
        if (triggered) {
          localStorage.setItem(TRIGGERED_SETUP_KEY, 'true');
        } else {
          localStorage.removeItem(TRIGGERED_SETUP_KEY);
        }
      } catch (error) {
        console.warn('Failed to save triggered setup status:', error);
      }
    }
  }, []);

  // Check if location setup is required (no setup completed and no location)
  const isLocationSetupRequired = useCallback(() => {
    if (!isInitialized) return false;

    // If setup was completed, don't require setup again
    if (setupStatus.hasSetup) return false;

    // If we have a location but no setup record, mark as complete
    if (location && !setupStatus.hasSetup) {
      markSetupComplete(location.source === 'auto' ? 'auto' : 'manual');
      return false;
    }

    return needsLocationSetup();
  }, [isInitialized, setupStatus.hasSetup, location, needsLocationSetup, markSetupComplete]);



  // Auto-trigger location setup on page load if needed (only once per session)
  useEffect(() => {
    if (isInitialized && !hasTriggeredSetup && isLocationSetupRequired()) {
      setShowLocationSetup(true);
      setTriggeredSetup(true);
    }
  }, [isInitialized, hasTriggeredSetup, isLocationSetupRequired, setTriggeredSetup]);

  const triggerLocationSetupIfNeeded = useCallback(() => {
    if (isLocationSetupRequired()) {
      setShowLocationSetup(true);
      setTriggeredSetup(true);
      return true;
    }
    return false;
  }, [isLocationSetupRequired, setTriggeredSetup]);

  const handleLocationSetupComplete = useCallback((choice: 'auto' | 'manual' | 'none') => {
    markSetupComplete(choice);
    setShowLocationSetup(false);
    setTriggeredSetup(true); // Prevent re-triggering
  }, [markSetupComplete, setTriggeredSetup]);

  return {
    showLocationSetup,
    setShowLocationSetup,
    triggerLocationSetupIfNeeded,
    handleLocationSetupComplete,
    isLocationSetupRequired: isLocationSetupRequired(),
    locationSetupStatus: setupStatus
  };
}

export default useLocationSetup;
