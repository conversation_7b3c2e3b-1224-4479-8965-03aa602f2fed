'use client'

import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { FaSpinner, FaExclamationTriangle } from 'react-icons/fa'

interface SecurityWrapperProps {
  children: React.ReactNode
  requiredRole?: 'user' | 'agent' | 'superuser'
  requiredPermissions?: string[]
  fallbackUrl?: string
  loadingComponent?: React.ReactNode
  unauthorizedComponent?: React.ReactNode
}

interface UserRole {
  role: string
  permissions: string[]
  hasAccess: boolean
}

export default function SecurityWrapper({
  children,
  requiredRole = 'user',
  requiredPermissions = [],
  fallbackUrl = '/',
  loadingComponent,
  unauthorizedComponent
}: SecurityWrapperProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [userRole, setUserRole] = useState<UserRole | null>(null)
  const [isChecking, setIsChecking] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    checkUserAccess()
  }, [session, status])

  const checkUserAccess = async () => {
    if (status === 'loading') return

    if (!session?.user?.id) {
      router.push(`/auth/signin?callbackUrl=${encodeURIComponent(window.location.pathname)}`)
      return
    }

    try {
      setIsChecking(true)
      setError(null)

      // Check user role and permissions
      const response = await fetch('/api/auth/check-permissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          requiredRole,
          requiredPermissions
        })
      })

      if (!response.ok) {
        throw new Error('Failed to check permissions')
      }

      const data = await response.json()
      setUserRole(data)

      // If user doesn't have access, redirect
      if (!data.hasAccess) {
        setError(`Access denied. Required role: ${requiredRole}`)
        setTimeout(() => {
          router.push(`${fallbackUrl}?error=access_denied`)
        }, 2000)
      }

    } catch (error) {
      console.error('Error checking user access:', error)
      setError('Failed to verify access permissions')
      setTimeout(() => {
        router.push(`${fallbackUrl}?error=permission_check_failed`)
      }, 2000)
    } finally {
      setIsChecking(false)
    }
  }

  // Loading state
  if (status === 'loading' || isChecking) {
    if (loadingComponent) {
      return <>{loadingComponent}</>
    }

    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FaSpinner className="animate-spin text-4xl text-blue-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Verifying Access</h2>
          <p className="text-gray-600">Checking your permissions...</p>
        </div>
      </div>
    )
  }

  // Not authenticated
  if (!session) {
    return null // Will redirect
  }

  // Access denied or error
  if (error || !userRole?.hasAccess) {
    if (unauthorizedComponent) {
      return <>{unauthorizedComponent}</>
    }

    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto">
          <FaExclamationTriangle className="text-6xl text-red-500 mx-auto mb-6" />
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">
            {error || `You don't have permission to access this page. Required role: ${requiredRole}`}
          </p>
          <div className="space-y-3">
            <button
              onClick={() => router.push(fallbackUrl)}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              Go Back
            </button>
            <button
              onClick={() => window.location.reload()}
              className="w-full bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Access granted
  return <>{children}</>
}

// Specialized wrappers for common use cases
export function AdminSecurityWrapper({ children, ...props }: Omit<SecurityWrapperProps, 'requiredRole'>) {
  return (
    <SecurityWrapper
      requiredRole="superuser"
      fallbackUrl="/"
      {...props}
    >
      {children}
    </SecurityWrapper>
  )
}

export function AgentSecurityWrapper({ children, ...props }: Omit<SecurityWrapperProps, 'requiredRole'>) {
  return (
    <SecurityWrapper
      requiredRole="agent"
      fallbackUrl="/"
      {...props}
    >
      {children}
    </SecurityWrapper>
  )
}

// Hook for checking permissions in components
export function usePermissions(requiredRole?: string, requiredPermissions?: string[]) {
  const { data: session, status } = useSession()
  const [permissions, setPermissions] = useState<UserRole | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (status === 'loading') return
    if (!session?.user?.id) {
      setIsLoading(false)
      return
    }

    const checkPermissions = async () => {
      try {
        const response = await fetch('/api/auth/check-permissions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            requiredRole,
            requiredPermissions
          })
        })

        if (response.ok) {
          const data = await response.json()
          setPermissions(data)
        }
      } catch (error) {
        console.error('Error checking permissions:', error)
      } finally {
        setIsLoading(false)
      }
    }

    checkPermissions()
  }, [session, status, requiredRole, requiredPermissions])

  return {
    permissions,
    isLoading,
    hasAccess: permissions?.hasAccess || false,
    userRole: permissions?.role,
    userPermissions: permissions?.permissions || []
  }
}
