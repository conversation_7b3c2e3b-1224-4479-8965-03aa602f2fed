// System feature exports
export { default as ErrorBoundary } from '@/app/shared/system/components/ErrorBoundary'
export { default as LoadingSpinner } from '@/app/shared/system/components/LoadingSpinner'
export { default as LayoutWrapper } from '@/app/shared/system/components/LayoutWrapper'
export { MaintenanceMode, useMaintenanceMode } from '@/app/shared/system/components/MaintenanceMode'

export { useAuthGuard, AUTH_CONFIGS } from '@/app/shared/system/hooks/useAuthGuard'
export { default as useSessionManager } from '@/app/shared/system/hooks/useSessionManager'

// Export types
export type { AuthGuardConfig, AuthGuardReturn } from '@/app/shared/system/hooks/useAuthGuard'
