import { Card } from '@/app/shared/ui'

interface MaintenanceModeProps {
  message?: string
  estimatedTime?: string
  contactEmail?: string
}

export function MaintenanceMode({ 
  message = "We're currently performing scheduled maintenance to improve your experience.",
  estimatedTime = "We'll be back shortly",
  contactEmail = "<EMAIL>"
}: MaintenanceModeProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
      <Card className="max-w-md w-full text-center" padding="lg">
        <div className="text-6xl mb-6">🔧</div>
        
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Under Maintenance
        </h1>
        
        <p className="text-gray-600 mb-6">
          {message}
        </p>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <p className="text-blue-800 font-medium">
            {estimatedTime}
          </p>
        </div>
        
        <div className="space-y-4">
          <div className="text-sm text-gray-500">
            <p>We apologize for any inconvenience.</p>
            <p>
              If you need immediate assistance, please contact us at{' '}
              <a 
                href={`mailto:${contactEmail}`}
                className="text-blue-600 hover:text-blue-700 underline"
              >
                {contactEmail}
              </a>
            </p>
          </div>
          
          <button
            onClick={() => window.location.reload()}
            className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Try Again
          </button>
        </div>
        
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
            <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
            <span>Maintenance in progress...</span>
          </div>
        </div>
      </Card>
    </div>
  )
}

// Hook to check maintenance mode
export function useMaintenanceMode() {
  // In a real app, this could check an API endpoint or feature flag
  const isMaintenanceMode = process.env.NEXT_PUBLIC_MAINTENANCE_MODE === 'true'
  
  return {
    isMaintenanceMode,
    maintenanceMessage: process.env.NEXT_PUBLIC_MAINTENANCE_MESSAGE,
    estimatedTime: process.env.NEXT_PUBLIC_MAINTENANCE_ETA,
  }
}
