/** @format */

'use client';

import { useSession } from 'next-auth/react';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export interface AuthGuardConfig {
	requireAuth?: boolean;
	requireRole?: 'superuser' | 'agent' | 'user';
	requirePermission?: 'full_access' | 'user_management';
	redirectTo?: string;
	allowedRoles?: string[];
	blockUnauthenticated?: boolean;
}

export interface AuthGuardReturn {
	isAuthenticated: boolean;
	isAuthorized: boolean;
	isLoading: boolean;
	user: Record<string, unknown> | null;
	hasRole: (role: string) => boolean;
	hasPermission: (permission: 'full_access' | 'user_management') => boolean;
	isSuperuser: boolean;
	isAgent: boolean;
	isUser: boolean;
	redirectToAuth: () => void;
}

const DEFAULT_CONFIG: AuthGuardConfig = {
	requireAuth: false,
	blockUnauthenticated: false,
	redirectTo: '/auth',
};

export function useAuthGuard(config: AuthGuardConfig = {}): AuthGuardReturn {
	const { data: session, status } = useSession();
	const router = useRouter();
	const pathname = usePathname();
	const [hasRedirected, setHasRedirected] = useState(false);

	const finalConfig = { ...DEFAULT_CONFIG, ...config };

	const isLoading = status === 'loading';
	const isAuthenticated = status === 'authenticated' && !!session?.user;
	const user = session?.user ?? null;

	// Check if user has specific role
	const hasRole = (role: string): boolean => {
		if (!user) return false;
		return user.role === role;
	};

	// Check if user has a specific permission (role-aware)
	const hasPermission = (
		permission: 'full_access' | 'user_management'
	): boolean => {
		if (!user || !user.permissions) return false;
		// Only superuser or agent roles can have effective permissions
		if (user.role === 'superuser') {
			if (permission === 'full_access')
				return user.permissions.includes('full_access');
			if (permission === 'user_management')
				return (
					user.permissions.includes('user_management') ||
					user.permissions.includes('full_access')
				);
		}
		if (user.role === 'agent') {
			return (
				permission === 'full_access' && user.permissions.includes('full_access')
			);
		}
		return false;
	};

	const isSuperuser = user?.role === 'superuser';
	const isAgent = user?.role === 'agent';
	const isUser = user?.role === 'user';

	// Check if user is authorized based on config
	const isAuthorized = (): boolean => {
		if (!finalConfig.requireAuth) return true;
		if (!isAuthenticated) return false;
		if (finalConfig.requireRole) {
			return hasRole(finalConfig.requireRole);
		}
		if (finalConfig.allowedRoles && finalConfig.allowedRoles.length > 0) {
			return finalConfig.allowedRoles.some((role) => hasRole(role));
		}
		if (finalConfig.requirePermission) {
			return hasPermission(finalConfig.requirePermission);
		}
		return true;
	};

	const redirectToAuth = () => {
		const callbackUrl = encodeURIComponent(pathname);
		router.push(`${finalConfig.redirectTo}?callbackUrl=${callbackUrl}`);
	};

	// Handle redirects based on auth state
	useEffect(() => {
		if (isLoading || hasRedirected) return;

		// If authentication is required but user is not authenticated
		if (finalConfig.requireAuth && !isAuthenticated) {
			setHasRedirected(true);
			redirectToAuth();
			return;
		}

		// If user is authenticated but not authorized
		if (isAuthenticated && !isAuthorized()) {
			setHasRedirected(true);
			router.push('/?error=access_denied');
			return;
		}

		// If we should block unauthenticated users from accessing the page
		if (finalConfig.blockUnauthenticated && !isAuthenticated && !isLoading) {
			setHasRedirected(true);
			redirectToAuth();
			return;
		}
	}, [isLoading, isAuthenticated, hasRedirected, pathname]);

	return {
		isAuthenticated,
		isAuthorized: isAuthorized(),
		isLoading,
		user,
		hasRole,
		hasPermission,
		isSuperuser,
		isAgent,
		isUser,
		redirectToAuth,
	};
}

// Predefined configurations for common use cases
export const AUTH_CONFIGS = {
	// Require authentication, any authenticated user can access
	REQUIRE_AUTH: {
		requireAuth: true,
		blockUnauthenticated: true,
	} as AuthGuardConfig,

	// Require superuser role
	REQUIRE_SUPERUSER: {
		requireAuth: true,
		requireRole: 'superuser' as const,
		redirectTo: '/auth',
	} as AuthGuardConfig,

	// Require agent or superuser role
	REQUIRE_AGENT: {
		requireAuth: true,
		allowedRoles: ['agent', 'superuser'],
		redirectTo: '/auth',
	} as AuthGuardConfig,

	// Require full access permission (superuser or agent)
	REQUIRE_FULL_ACCESS: {
		requireAuth: true,
		requirePermission: 'full_access',
		redirectTo: '/auth',
	} as AuthGuardConfig,

	// Require user management permission (superuser only)
	REQUIRE_USER_MANAGEMENT: {
		requireAuth: true,
		requirePermission: 'user_management',
		redirectTo: '/auth',
	} as AuthGuardConfig,

	// Public page but redirect authenticated users to chat
	PUBLIC_REDIRECT_AUTHENTICATED: {
		requireAuth: false,
	} as AuthGuardConfig,

	// Chat page - require auth and redirect to landing if not authenticated
	CHAT_PAGE: {
		requireAuth: true,
		blockUnauthenticated: true,
		redirectTo: '/',
	} as AuthGuardConfig,
};
