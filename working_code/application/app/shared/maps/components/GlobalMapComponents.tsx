/** @format */

'use client';

import L from 'leaflet';
import React, { useEffect, useRef } from 'react';
import GlobalMapManager from './GlobalMapManager';
import { OptimizedTileLayer } from './OptimizedTileLayer';

// Fix for default markers in React Leaflet - ensure this runs before any markers are created
// eslint-disable-next-line @typescript-eslint/no-explicit-any
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
	iconRetinaUrl:
		'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
	iconUrl:
		'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
	shadowUrl:
		'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Global Map Marker Component
interface GlobalMapMarkerProps {
	position: [number, number];
	children?: React.ReactNode;
	icon?: L.Icon | L.DivIcon;
	popup?: React.ReactNode;
	onClick?: () => void;
}

export const GlobalMapMarker: React.FC<GlobalMapMarkerProps> = ({
	position,
	icon,
	popup,
	onClick,
}) => {
	const markerRef = useRef<L.Marker | null>(null);
	const manager = GlobalMapManager.getInstance();

	useEffect(() => {
		const map = manager.getGlobalMap();
		if (!map) return;

		// Create marker with proper icon handling
		try {
			markerRef.current = L.marker(position, {
				icon: icon || undefined, // Use default icon if none provided
			});

			// Add click handler
			if (onClick) {
				markerRef.current.on('click', onClick);
			}

			// Add popup if provided
			if (popup) {
				// Create a temporary div to render the React component
				const tempDiv = document.createElement('div');
				tempDiv.className = 'leaflet-popup-content';

				// If popup is a string, use it directly
				if (typeof popup === 'string') {
					tempDiv.innerHTML = popup;
				} else {
					// For React components, we'll use a simple text fallback
					tempDiv.innerHTML = '<div>Marker Information</div>';
				}

				markerRef.current.bindPopup(tempDiv);
			}

			// Add to map
			markerRef.current.addTo(map);
		} catch (error) {
			console.error('Error creating marker:', error);
		}

		return () => {
			if (markerRef.current) {
				try {
					markerRef.current.remove();
					markerRef.current = null;
				} catch (error) {
					console.warn('Error removing marker:', error);
				}
			}
		};
	}, [position, icon, popup, onClick, manager]);

	return null;
};

// Global Map Popup Component
interface GlobalMapPopupProps {
	children: React.ReactNode;
}

export const GlobalMapPopup: React.FC<GlobalMapPopupProps> = ({ children }) => {
	return <div>{children}</div>;
};

// Global Map Tile Layer Component - Now uses OptimizedTileLayer
interface GlobalMapTileLayerProps {
	url: string;
	attribution?: string;
	maxZoom?: number;
	minZoom?: number;
	keepBuffer?: number;
	updateWhenIdle?: boolean;
	updateWhenZooming?: boolean;
	updateInterval?: number;
	crossOrigin?: boolean;
	errorTileUrl?: string;
	maxNativeZoom?: number;
	tileSize?: number;
	zoomOffset?: number;
	// Performance options
	enableRetina?: boolean;
	detectRetina?: boolean;
	bounds?: L.LatLngBounds;
	noWrap?: boolean;
	pane?: string;
	className?: string;
	zIndex?: number;
	opacity?: number;
	// Custom tile loading
	tileLoadingTimeout?: number;
	maxTileLoadAttempts?: number;
	tileRetryDelay?: number;
	// Zoom level optimization
	zoomLevelOptimization?: boolean;
	adaptiveZoomLevels?: boolean;
}

export const GlobalMapTileLayer: React.FC<GlobalMapTileLayerProps> = (
	props
) => {
	return <OptimizedTileLayer {...props} />;
};

// Global Map Circle Component
interface GlobalMapCircleProps {
	center: [number, number];
	radius: number;
	color?: string;
	fillColor?: string;
	fillOpacity?: number;
	weight?: number;
	opacity?: number;
	popup?: React.ReactNode;
	onClick?: () => void;
}

export const GlobalMapCircle: React.FC<GlobalMapCircleProps> = ({
	center,
	radius,
	color = '#3388ff',
	fillColor = '#3388ff',
	fillOpacity = 0.2,
	weight = 3,
	opacity = 1,
	popup,
	onClick,
}) => {
	const circleRef = useRef<L.Circle | null>(null);
	const manager = GlobalMapManager.getInstance();

	useEffect(() => {
		const map = manager.getGlobalMap();
		if (!map) return;

		try {
			// Create circle
			circleRef.current = L.circle(center, {
				radius,
				color,
				fillColor,
				fillOpacity,
				weight,
				opacity,
			});

			// Add click handler
			if (onClick) {
				circleRef.current.on('click', onClick);
			}

			// Add popup if provided
			if (popup) {
				// Create a temporary div to render the React component
				const tempDiv = document.createElement('div');
				tempDiv.className = 'leaflet-popup-content';

				// If popup is a string, use it directly
				if (typeof popup === 'string') {
					tempDiv.innerHTML = popup;
				} else {
					// For React components, we'll use a simple text fallback
					tempDiv.innerHTML = '<div>Circle Information</div>';
				}

				circleRef.current.bindPopup(tempDiv);
			}

			// Add to map
			circleRef.current.addTo(map);
		} catch (error) {
			console.error('Error creating circle:', error);
		}

		return () => {
			if (circleRef.current) {
				try {
					circleRef.current.remove();
					circleRef.current = null;
				} catch (error) {
					console.warn('Error removing circle:', error);
				}
			}
		};
	}, [
		center,
		radius,
		color,
		fillColor,
		fillOpacity,
		weight,
		opacity,
		popup,
		onClick,
		manager,
	]);

	return null;
};

// Global Map Polyline Component
interface GlobalMapPolylineProps {
	positions: [number, number][];
	color?: string;
	weight?: number;
	opacity?: number;
	popup?: React.ReactNode;
	onClick?: () => void;
}

export const GlobalMapPolyline: React.FC<GlobalMapPolylineProps> = ({
	positions,
	color = '#3388ff',
	weight = 3,
	opacity = 1,
	popup,
	onClick,
}) => {
	const polylineRef = useRef<L.Polyline | null>(null);
	const manager = GlobalMapManager.getInstance();

	useEffect(() => {
		const map = manager.getGlobalMap();
		if (!map) return;

		try {
			// Create polyline
			polylineRef.current = L.polyline(positions, {
				color,
				weight,
				opacity,
			});

			// Add click handler
			if (onClick) {
				polylineRef.current.on('click', onClick);
			}

			// Add popup if provided
			if (popup) {
				// Create a temporary div to render the React component
				const tempDiv = document.createElement('div');
				tempDiv.className = 'leaflet-popup-content';

				// If popup is a string, use it directly
				if (typeof popup === 'string') {
					tempDiv.innerHTML = popup;
				} else {
					// For React components, we'll use a simple text fallback
					tempDiv.innerHTML = '<div>Polyline Information</div>';
				}

				polylineRef.current.bindPopup(tempDiv);
			}

			// Add to map
			polylineRef.current.addTo(map);
		} catch (error) {
			console.error('Error creating polyline:', error);
		}

		return () => {
			if (polylineRef.current) {
				try {
					polylineRef.current.remove();
					polylineRef.current = null;
				} catch (error) {
					console.warn('Error removing polyline:', error);
				}
			}
		};
	}, [positions, color, weight, opacity, popup, onClick, manager]);

	return null;
};

// Global Map Polygon Component
interface GlobalMapPolygonProps {
	positions: [number, number][];
	color?: string;
	fillColor?: string;
	fillOpacity?: number;
	weight?: number;
	opacity?: number;
	popup?: React.ReactNode;
	onClick?: () => void;
}

export const GlobalMapPolygon: React.FC<GlobalMapPolygonProps> = ({
	positions,
	color = '#3388ff',
	fillColor = '#3388ff',
	fillOpacity = 0.2,
	weight = 3,
	opacity = 1,
	popup,
	onClick,
}) => {
	const polygonRef = useRef<L.Polygon | null>(null);
	const manager = GlobalMapManager.getInstance();

	useEffect(() => {
		const map = manager.getGlobalMap();
		if (!map) return;

		try {
			// Create polygon
			polygonRef.current = L.polygon(positions, {
				color,
				fillColor,
				fillOpacity,
				weight,
				opacity,
			});

			// Add click handler
			if (onClick) {
				polygonRef.current.on('click', onClick);
			}

			// Add popup if provided
			if (popup) {
				// Create a temporary div to render the React component
				const tempDiv = document.createElement('div');
				tempDiv.className = 'leaflet-popup-content';

				// If popup is a string, use it directly
				if (typeof popup === 'string') {
					tempDiv.innerHTML = popup;
				} else {
					// For React components, we'll use a simple text fallback
					tempDiv.innerHTML = '<div>Polygon Information</div>';
				}

				polygonRef.current.bindPopup(tempDiv);
			}

			// Add to map
			polygonRef.current.addTo(map);
		} catch (error) {
			console.error('Error creating polygon:', error);
		}

		return () => {
			if (polygonRef.current) {
				try {
					polygonRef.current.remove();
					polygonRef.current = null;
				} catch (error) {
					console.warn('Error removing polygon:', error);
				}
			}
		};
	}, [
		positions,
		color,
		fillColor,
		fillOpacity,
		weight,
		opacity,
		popup,
		onClick,
		manager,
	]);

	return null;
};

// Hook to get the global map instance
export const useGlobalMap = () => {
	const manager = GlobalMapManager.getInstance();
	return manager.getGlobalMap();
};

// Hook to get map debug info
export const useGlobalMapDebug = () => {
	const manager = GlobalMapManager.getInstance();
	return manager.getDebugInfo();
};
