// Maps feature components exports
export { default as MapContainer } from './MapContainer'
export { default as GlobalMapContainer } from './GlobalMapContainer'
export { default as OptimizedMapContainer } from './OptimizedMapContainer'
export { default as UnifiedMapContainer } from './UnifiedMapContainer'
export { default as UserLocationMarker } from './UserLocationMarker'
export { default as POIMarker } from './POIMarker'
export { default as ExtractedLocationMarker } from './ExtractedLocationMarker'

// Export unified components
export {
  UnifiedUserLocationMarker,
  UnifiedExtractedLocationMarker,
  UnifiedMapResizeHandler,
  UnifiedFitBounds
} from './UnifiedMarkers'

// Export global map components
export {
  GlobalMapMarker,
  GlobalMapPopup,
  GlobalMapTileLayer,
  GlobalMapCircle,
  GlobalMapPolyline,
  GlobalMapPolygon,
  useGlobalMap,
  useGlobalMapDebug
} from './GlobalMapComponents'

// Export custom markers (icon-safe)
export {
  CustomUserLocationMarker,
  CustomPOIMarker,
  CustomTestMarker
} from './CustomMarkers'

// Export optimized tile layers
export {
  OptimizedTileLayer,
  HighPerformanceTileLayer,
  LightweightTileLayer
} from './OptimizedTileLayer'

// Export performance monitoring
export {
  PerformanceMonitor,
  useMapPerformance,
  optimizeMapPerformance,
  getPerformanceRecommendations
} from './PerformanceMonitor'

// Export map utilities
export {
  MapResizeHandler,
  FitBounds,
  MapCenterController,
  CustomControl,
  validateCoordinates,
  calculateDistance,
  formatDistance,
  DEFAULT_MAP_CONFIG
} from './MapUtils'

// Export managers
export { default as GlobalMapManager } from './GlobalMapManager'

// Re-export React Leaflet components for convenience
export { TileLayer, Marker, Popup, Circle, Polyline, Polygon } from 'react-leaflet'
