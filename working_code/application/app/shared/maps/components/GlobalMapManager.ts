/** @format */

// Global Map Manager - Single instance for the entire application
import L from 'leaflet';

interface MapConfig {
	center: [number, number];
	zoom: number;
	minZoom?: number;
	maxZoom?: number;
	zoomControl?: boolean;
	scrollWheelZoom?: boolean | 'center';
	doubleClickZoom?: boolean | 'center';
	touchZoom?: boolean | 'center';
	dragging?: boolean;
	keyboard?: boolean;
	boxZoom?: boolean;
	zoomSnap?: number;
	zoomDelta?: number;
	wheelPxPerZoomLevel?: number;
}

interface MapContainer {
	element: HTMLElement;
	map: L.Map;
	config: MapConfig;
	isActive: boolean;
}

class GlobalMapManager {
	private static instance: GlobalMapManager;
	private globalMap: L.Map | null = null;
	private currentContainer: MapContainer | null = null;
	private containers: Map<string, MapContainer> = new Map();
	private isInitialized = false;

	private constructor() {}

	static getInstance(): GlobalMapManager {
		if (!GlobalMapManager.instance) {
			GlobalMapManager.instance = new GlobalMapManager();
		}
		return GlobalMapManager.instance;
	}

	// Initialize the global map instance
	initializeGlobalMap(element: HTMLElement, config: MapConfig): L.Map {
		if (this.globalMap) {
			// If we already have a global map, just move it to the new container
			this.moveMapToContainer(element, config);
			return this.globalMap;
		}

		// Create new global map instance
		this.globalMap = L.map(element, {
			center: config.center,
			zoom: config.zoom,
			minZoom: config.minZoom || 3,
			maxZoom: config.maxZoom || 18,
			zoomControl: config.zoomControl !== false,
			scrollWheelZoom: config.scrollWheelZoom || 'center',
			doubleClickZoom: config.doubleClickZoom || 'center',
			touchZoom: config.touchZoom || 'center',
			dragging: config.dragging !== false,
			keyboard: config.keyboard !== false,
			boxZoom: config.boxZoom !== false,
			zoomSnap: config.zoomSnap || 0.5,
			zoomDelta: config.zoomDelta || 0.5,
			wheelPxPerZoomLevel: config.wheelPxPerZoomLevel || 120,
			preferCanvas: false,
			zoomAnimation: true,
			fadeAnimation: true,
			markerZoomAnimation: true,
			trackResize: true,
			worldCopyJump: false,
			attributionControl: false,
		});

		// Add default tile layer
		L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
			attribution:
				'&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
			maxZoom: 19,
			keepBuffer: 1,
			updateWhenIdle: true,
			updateWhenZooming: false,
			updateInterval: 200,
			crossOrigin: true,
			errorTileUrl:
				'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
			maxNativeZoom: 18,
			tileSize: 256,
			zoomOffset: 0,
		}).addTo(this.globalMap);

		this.isInitialized = true;
		console.log('🌍 Global map instance created');
		return this.globalMap;
	}

	// Move the global map to a different container
	private moveMapToContainer(element: HTMLElement, config: MapConfig): void {
		if (!this.globalMap) return;

		// Remove map from current container
		if (this.currentContainer) {
			this.currentContainer.isActive = false;
			this.globalMap.remove();
		}

		// Add map to new container
		this.globalMap.setView(config.center, config.zoom);
		element.appendChild(this.globalMap.getContainer());
		this.globalMap.invalidateSize();

		this.currentContainer = {
			element,
			map: this.globalMap,
			config,
			isActive: true,
		};

		console.log('🌍 Global map moved to new container');
	}

	// Get or create container for a specific component
	getContainer(
		containerId: string,
		element: HTMLElement,
		config: MapConfig
	): L.Map {
		// Check if this container already exists
		if (this.containers.has(containerId)) {
			const container = this.containers.get(containerId)!;
			if (container.element === element) {
				// Same element, return existing map
				return container.map;
			} else {
				// Different element, update the container
				container.element = element;
				this.moveMapToContainer(element, config);
				return container.map;
			}
		}

		// Create new container
		const map = this.initializeGlobalMap(element, config);
		const container: MapContainer = {
			element,
			map,
			config,
			isActive: true,
		};

		this.containers.set(containerId, container);
		this.currentContainer = container;

		return map;
	}

	// Remove a container
	removeContainer(containerId: string): void {
		if (this.containers.has(containerId)) {
			const container = this.containers.get(containerId)!;
			container.isActive = false;
			this.containers.delete(containerId);

			// If this was the current container, clear it
			if (this.currentContainer === container) {
				this.currentContainer = null;
			}

			console.log(`🗑️ Container ${containerId} removed`);
		}
	}

	// Get the current global map instance
	getGlobalMap(): L.Map | null {
		return this.globalMap;
	}

	// Check if global map is initialized
	isMapInitialized(): boolean {
		return this.isInitialized;
	}

	// Get current active container
	getCurrentContainer(): MapContainer | null {
		return this.currentContainer;
	}

	// Get all containers
	getAllContainers(): Map<string, MapContainer> {
		return this.containers;
	}

	// Cleanup all containers and destroy global map
	cleanup(): void {
		this.containers.clear();
		this.currentContainer = null;

		if (this.globalMap) {
			this.globalMap.remove();
			this.globalMap = null;
		}

		this.isInitialized = false;
		console.log('🧹 Global map manager cleaned up');
	}

	// Get debug info
	getDebugInfo(): {
		isInitialized: boolean;
		containerCount: number;
		hasGlobalMap: boolean;
		currentContainerId: string | null;
	} {
		return {
			isInitialized: this.isInitialized,
			containerCount: this.containers.size,
			hasGlobalMap: this.globalMap !== null,
			currentContainerId: this.currentContainer
				? Array.from(this.containers.entries()).find(
						// eslint-disable-next-line @typescript-eslint/no-unused-vars
						([_id, container]) => container === this.currentContainer
				  )?.[0] || null
				: null,
		};
	}
}

export default GlobalMapManager;
