/** @format */

'use client';

import L from 'leaflet';
import React, { useEffect, useRef } from 'react';
import GlobalMapManager from './GlobalMapManager';

// Create a simple div icon for user location
const createUserLocationIcon = () => {
	return L.divIcon({
		className: 'user-location-marker',
		html: `
      <div style="
        width: 20px; 
        height: 20px; 
        background: #3b82f6; 
        border: 3px solid white; 
        border-radius: 50%; 
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        position: relative;
      ">
        <div style="
          position: absolute;
          top: -5px;
          left: -5px;
          width: 30px;
          height: 30px;
          background: rgba(59, 130, 246, 0.3);
          border-radius: 50%;
          animation: pulse 2s infinite;
        "></div>
      </div>
      <style>
        @keyframes pulse {
          0% {
            transform: scale(0.95);
            box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
          }
          70% {
            transform: scale(1);
            box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
          }
          100% {
            transform: scale(0.95);
            box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
          }
        }
      </style>
    `,
		iconSize: [20, 20],
		iconAnchor: [10, 10],
		popupAnchor: [0, -10],
	});
};

// Create a simple div icon for POI markers
const createPOIIcon = (color: string = '#ef4444') => {
	return L.divIcon({
		className: 'poi-marker',
		html: `
      <div style="
        width: 16px; 
        height: 16px; 
        background: ${color}; 
        border: 2px solid white; 
        border-radius: 50%; 
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
      "></div>
    `,
		iconSize: [16, 16],
		iconAnchor: [8, 8],
		popupAnchor: [0, -8],
	});
};

// Create a simple div icon for test markers
const createTestIcon = () => {
	return L.divIcon({
		className: 'test-marker',
		html: `
      <div style="
        width: 18px; 
        height: 18px; 
        background: #10b981; 
        border: 2px solid white; 
        border-radius: 50%; 
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
      "></div>
    `,
		iconSize: [18, 18],
		iconAnchor: [9, 9],
		popupAnchor: [0, -9],
	});
};

// Custom User Location Marker
interface CustomUserLocationMarkerProps {
	position: [number, number];
	popup?: React.ReactNode;
	onClick?: () => void;
}

export const CustomUserLocationMarker: React.FC<
	CustomUserLocationMarkerProps
> = ({ position, popup, onClick }) => {
	const markerRef = useRef<L.Marker | null>(null);
	const manager = GlobalMapManager.getInstance();

	useEffect(() => {
		const map = manager.getGlobalMap();
		if (!map) return;

		try {
			const icon = createUserLocationIcon();
			markerRef.current = L.marker(position, { icon });

			if (onClick) {
				markerRef.current.on('click', onClick);
			}

			if (popup) {
				// Create a temporary div to render the React component
				const tempDiv = document.createElement('div');
				tempDiv.className = 'leaflet-popup-content';

				// If popup is a string, use it directly
				if (typeof popup === 'string') {
					tempDiv.innerHTML = popup;
				} else {
					// For React components, we'll use a simple text fallback
					tempDiv.innerHTML = '<div>Location Information</div>';
				}

				markerRef.current.bindPopup(tempDiv);
			}

			markerRef.current.addTo(map);
		} catch (error) {
			console.error('Error creating user location marker:', error);
		}

		return () => {
			if (markerRef.current) {
				try {
					markerRef.current.remove();
					markerRef.current = null;
				} catch (error) {
					console.warn('Error removing user location marker:', error);
				}
			}
		};
	}, [position, popup, onClick, manager]);

	return null;
};

// Custom POI Marker
interface CustomPOIMarkerProps {
	position: [number, number];
	color?: string;
	popup?: React.ReactNode;
	onClick?: () => void;
}

export const CustomPOIMarker: React.FC<CustomPOIMarkerProps> = ({
	position,
	color = '#ef4444',
	popup,
	onClick,
}) => {
	const markerRef = useRef<L.Marker | null>(null);
	const manager = GlobalMapManager.getInstance();

	useEffect(() => {
		const map = manager.getGlobalMap();
		if (!map) return;

		try {
			const icon = createPOIIcon(color);
			markerRef.current = L.marker(position, { icon });

			if (onClick) {
				markerRef.current.on('click', onClick);
			}

			if (popup) {
				// Create a temporary div to render the React component
				const tempDiv = document.createElement('div');
				tempDiv.className = 'leaflet-popup-content';

				// If popup is a string, use it directly
				if (typeof popup === 'string') {
					tempDiv.innerHTML = popup;
				} else {
					// For React components, we'll use a simple text fallback
					tempDiv.innerHTML = '<div>POI Information</div>';
				}

				markerRef.current.bindPopup(tempDiv);
			}

			markerRef.current.addTo(map);
		} catch (error) {
			console.error('Error creating POI marker:', error);
		}

		return () => {
			if (markerRef.current) {
				try {
					markerRef.current.remove();
					markerRef.current = null;
				} catch (error) {
					console.warn('Error removing POI marker:', error);
				}
			}
		};
	}, [position, color, popup, onClick, manager]);

	return null;
};

// Custom Test Marker
interface CustomTestMarkerProps {
	position: [number, number];
	popup?: React.ReactNode;
	onClick?: () => void;
}

export const CustomTestMarker: React.FC<CustomTestMarkerProps> = ({
	position,
	popup,
	onClick,
}) => {
	const markerRef = useRef<L.Marker | null>(null);
	const manager = GlobalMapManager.getInstance();

	useEffect(() => {
		const map = manager.getGlobalMap();
		if (!map) return;

		try {
			const icon = createTestIcon();
			markerRef.current = L.marker(position, { icon });

			if (onClick) {
				markerRef.current.on('click', onClick);
			}

			if (popup) {
				// Create a temporary div to render the React component
				const tempDiv = document.createElement('div');
				tempDiv.className = 'leaflet-popup-content';

				// If popup is a string, use it directly
				if (typeof popup === 'string') {
					tempDiv.innerHTML = popup;
				} else {
					// For React components, we'll use a simple text fallback
					tempDiv.innerHTML = '<div>Test Marker Information</div>';
				}

				markerRef.current.bindPopup(tempDiv);
			}

			markerRef.current.addTo(map);
		} catch (error) {
			console.error('Error creating test marker:', error);
		}

		return () => {
			if (markerRef.current) {
				try {
					markerRef.current.remove();
					markerRef.current = null;
				} catch (error) {
					console.warn('Error removing test marker:', error);
				}
			}
		};
	}, [position, popup, onClick, manager]);

	return null;
};
