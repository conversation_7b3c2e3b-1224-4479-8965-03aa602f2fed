/** @format */

'use client';

import L from 'leaflet';
import React, { useEffect, useMemo, useRef } from 'react';
import GlobalMapManager from './GlobalMapManager';

interface OptimizedTileLayerProps {
	url: string;
	attribution?: string;
	maxZoom?: number;
	minZoom?: number;
	keepBuffer?: number;
	updateWhenIdle?: boolean;
	updateWhenZooming?: boolean;
	updateInterval?: number;
	crossOrigin?: boolean;
	errorTileUrl?: string;
	maxNativeZoom?: number;
	tileSize?: number;
	zoomOffset?: number;
	// Performance optimizations
	enableRetina?: boolean;
	detectRetina?: boolean;
	bounds?: L.LatLngBounds;
	noWrap?: boolean;
	pane?: string;
	className?: string;
	zIndex?: number;
	opacity?: number;
	// Custom tile loading
	tileLoadingTimeout?: number;
	maxTileLoadAttempts?: number;
	tileRetryDelay?: number;
	// Zoom level optimization
	zoomLevelOptimization?: boolean;
	adaptiveZoomLevels?: boolean;
}

export const OptimizedTileLayer: React.FC<OptimizedTileLayerProps> = ({
	url,
	attribution,
	maxZoom = 18,
	minZoom = 0,
	keepBuffer = 2,
	updateWhenIdle = true,
	updateWhenZooming = false,
	updateInterval = 200,
	crossOrigin = true,
	errorTileUrl,
	maxNativeZoom = 18,
	tileSize = 1024, // Much larger default tile size
	zoomOffset = 0,
	detectRetina = true,
	bounds,
	noWrap = false,
	pane = 'tilePane',
	className = '',
	zIndex,
	opacity = 1,
	tileLoadingTimeout = 10000,
	maxTileLoadAttempts = 3,
	tileRetryDelay = 1000,
	zoomLevelOptimization = true,
	adaptiveZoomLevels = true,
}) => {
	const tileLayerRef = useRef<L.TileLayer | null>(null);
	const manager = GlobalMapManager.getInstance();
	const retryCountRef = useRef<Map<string, number>>(new Map());

	// Advanced tile size optimization based on zoom level and viewport
	const optimizedTileSize = useMemo(() => {
		if (!zoomLevelOptimization) return tileSize;

		const map = manager.getGlobalMap();
		if (!map) return tileSize;

		const currentZoom = map.getZoom();

		// Calculate optimal tile size based on zoom level
		// Use very large tiles to reduce the number of tiles needed
		if (currentZoom <= 4) return 2048; // World view - massive tiles
		if (currentZoom <= 6) return 1536; // Continent view - very large tiles
		if (currentZoom <= 8) return 1024; // Country view - large tiles
		if (currentZoom <= 10) return 768; // Region view - medium-large tiles
		if (currentZoom <= 12) return 512; // City view - medium tiles
		if (currentZoom <= 14) return 384; // District view - smaller tiles
		return 256; // Street view - standard tiles
	}, [tileSize, zoomLevelOptimization, manager]);

	// More aggressive zoom level optimization to prevent excessive tile loading
	const optimizedMaxZoom = useMemo(() => {
		if (!adaptiveZoomLevels) return maxZoom;

		const map = manager.getGlobalMap();
		if (!map) return maxZoom;

		const currentZoom = map.getZoom();

		// Very conservative zoom limits to prevent the 4-tile side-by-side issue
		if (currentZoom <= 6) return Math.min(maxZoom, 10); // Very limited zoom for world view
		if (currentZoom <= 10) return Math.min(maxZoom, 13); // Limited zoom for country view
		if (currentZoom <= 13) return Math.min(maxZoom, 15); // Limited zoom for city view
		if (currentZoom <= 15) return Math.min(maxZoom, 17); // Limited zoom for district view
		return maxZoom;
	}, [maxZoom, adaptiveZoomLevels, manager]);

	useEffect(() => {
		const map = manager.getGlobalMap();
		if (!map) return;

		try {
			// Create optimized tile layer with performance settings
			const tileLayerOptions: L.TileLayerOptions = {
				attribution,
				maxZoom: optimizedMaxZoom,
				minZoom,
				keepBuffer: Math.max(keepBuffer, 4), // Ensure minimum buffer
				updateWhenIdle: true,
				updateWhenZooming: false,
				updateInterval: Math.max(updateInterval, 300), // Slower updates for stability
				crossOrigin: crossOrigin ? 'anonymous' : undefined,
				errorTileUrl,
				maxNativeZoom: Math.min(maxNativeZoom, optimizedMaxZoom),
				tileSize: optimizedTileSize,
				zoomOffset: Math.max(zoomOffset, -2), // Adjust zoom offset for larger tiles
				detectRetina: false, // Disable retina for better performance with large tiles
				bounds,
				noWrap,
				pane,
				className,
				zIndex,
				opacity,
			};

			// Create tile layer
			tileLayerRef.current = L.tileLayer(url, tileLayerOptions);

			// Add performance event listeners
			tileLayerRef.current.on('loading', () => {
				console.log(
					'Tile layer loading started with tile size:',
					optimizedTileSize
				);
			});

			tileLayerRef.current.on('load', () => {
				console.log('Tile layer loading completed');
			});

			tileLayerRef.current.on(
				'tileerror',
				(e: { coords: unknown; tile: unknown }) => {
					console.warn('Tile load error:', e.coords, e.tile);
				}
			);

			// Add to map
			tileLayerRef.current.addTo(map);
		} catch (error) {
			console.error('Error creating optimized tile layer:', error);
		}

		return () => {
			if (tileLayerRef.current) {
				try {
					tileLayerRef.current.remove();
					tileLayerRef.current = null;
					retryCountRef.current.clear();
				} catch (error) {
					console.warn('Error removing optimized tile layer:', error);
				}
			}
		};
	}, [
		url,
		attribution,
		optimizedMaxZoom,
		minZoom,
		keepBuffer,
		updateWhenIdle,
		updateWhenZooming,
		updateInterval,
		crossOrigin,
		errorTileUrl,
		maxNativeZoom,
		optimizedTileSize,
		zoomOffset,
		detectRetina,
		bounds,
		noWrap,
		pane,
		className,
		zIndex,
		opacity,
		tileLoadingTimeout,
		maxTileLoadAttempts,
		tileRetryDelay,
		manager,
	]);

	return null;
};

// Ultra-high-performance tile layer with massive tiles
export const HighPerformanceTileLayer: React.FC<OptimizedTileLayerProps> = (
	props
) => {
	return (
		<OptimizedTileLayer
			{...props}
			tileSize={1536} // Massive default tile size
			keepBuffer={8} // Very aggressive caching
			updateWhenIdle={true}
			updateWhenZooming={false}
			updateInterval={400} // Slower updates for stability
			tileLoadingTimeout={15000} // Much longer timeout for massive tiles
			maxTileLoadAttempts={2} // Fewer retries for speed
			tileRetryDelay={1200} // Faster retry
			zoomLevelOptimization={true}
			adaptiveZoomLevels={true}
			detectRetina={false} // Disable retina for better performance
		/>
	);
};

// Lightweight tile layer for low-end devices with smaller tiles
export const LightweightTileLayer: React.FC<OptimizedTileLayerProps> = (
	props
) => {
	return (
		<OptimizedTileLayer
			{...props}
			tileSize={512} // Moderate tiles for low-end devices
			keepBuffer={3} // Moderate caching
			updateWhenIdle={true}
			updateWhenZooming={false}
			updateInterval={500} // Slower updates
			maxZoom={Math.min(props.maxZoom || 18, 15)} // Lower max zoom
			zoomLevelOptimization={true}
			adaptiveZoomLevels={true}
		/>
	);
};
