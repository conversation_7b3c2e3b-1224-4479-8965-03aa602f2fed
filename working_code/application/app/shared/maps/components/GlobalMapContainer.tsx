/** @format */

'use client';

import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import React, { ReactNode, useEffect, useRef, useState } from 'react';

// Fix for default markers in React Leaflet - ensure this runs before any map initialization
// eslint-disable-next-line @typescript-eslint/no-explicit-any
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
	iconRetinaUrl:
		'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
	iconUrl:
		'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
	shadowUrl:
		'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface GlobalMapContainerProps {
	center: [number, number];
	zoom: number;
	children?: ReactNode;
	className?: string;
	style?: React.CSSProperties;
	onMapReady?: (map: L.Map) => void;
	onBoundsChange?: (
		bounds: L.LatLngBounds,
		center: L.LatLng,
		zoom: number
	) => void;
}

const GlobalMapContainer: React.FC<GlobalMapContainerProps> = ({
	center,
	zoom,
	children,
	className = '',
	style = { height: '100%', width: '100%' },
	onMapReady,
	onBoundsChange,
}) => {
	const containerRef = useRef<HTMLDivElement>(null);
	const mapRef = useRef<L.Map | null>(null);
	const [isReady, setIsReady] = useState(false);
	const [isLoading, setIsLoading] = useState(true);
	const boundsChangeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

	// Initialize map once when component mounts
	useEffect(() => {
		if (!containerRef.current || mapRef.current) return;

		try {
			// Create map instance with optimized settings
			const mapInstance = L.map(containerRef.current, {
				center,
				zoom,
				minZoom: 3,
				maxZoom: 18,
				zoomControl: true,
				scrollWheelZoom: true,
				doubleClickZoom: true,
				touchZoom: true,
				dragging: true,
				keyboard: true,
				boxZoom: true,
				zoomSnap: 1,
				zoomDelta: 1,
				preferCanvas: true,
				fadeAnimation: true,
				zoomAnimation: true,
				markerZoomAnimation: true,
				attributionControl: false,
				worldCopyJump: false,
			});

			// Add optimized tile layer
			L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
				attribution:
					'&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
				maxZoom: 18,
				keepBuffer: 2,
				updateWhenIdle: true,
				updateWhenZooming: false,
				crossOrigin: true,
				tileSize: 256,
			}).addTo(mapInstance);

			mapRef.current = mapInstance;

			// Set up debounced bounds change handler
			const handleBoundsChange = () => {
				if (boundsChangeTimeoutRef.current) {
					clearTimeout(boundsChangeTimeoutRef.current);
				}

				boundsChangeTimeoutRef.current = setTimeout(() => {
					if (onBoundsChange && mapRef.current) {
						const bounds = mapRef.current.getBounds();
						const center = mapRef.current.getCenter();
						const zoom = mapRef.current.getZoom();
						onBoundsChange(bounds, center, zoom);
					}
				}, 300); // Debounce for 300ms
			};

			// Add event listeners
			mapInstance.on('moveend', handleBoundsChange);
			mapInstance.on('zoomend', handleBoundsChange);

			// Map is ready
			setIsLoading(false);
			setIsReady(true);
			onMapReady?.(mapInstance);
		} catch (error) {
			console.error('Error initializing map:', error);
			setIsLoading(false);
		}

		// Cleanup function
		return () => {
			if (boundsChangeTimeoutRef.current) {
				clearTimeout(boundsChangeTimeoutRef.current);
			}
			if (mapRef.current) {
				mapRef.current.remove();
				mapRef.current = null;
			}
		};
	}, []); // Empty dependency array - only run once

	// Update map view when center/zoom props change (but only if significant change)
	useEffect(() => {
		if (!mapRef.current || !isReady) return;

		const currentCenter = mapRef.current.getCenter();
		const currentZoom = mapRef.current.getZoom();

		// Only update if there's a significant difference
		const centerDistance = currentCenter.distanceTo(
			L.latLng(center[0], center[1])
		);
		const zoomDifference = Math.abs(currentZoom - zoom);

		if (centerDistance > 100 || zoomDifference > 0.5) {
			// 100 meters or 0.5 zoom levels
			mapRef.current.setView(center, zoom, {
				animate: true,
				duration: 0.5,
			});
		}
	}, [center, zoom, isReady]);

	return (
		<div
			ref={containerRef}
			className={`relative ${className}`}
			style={style}>
			{isLoading && (
				<div className='absolute inset-0 flex items-center justify-center bg-white/95 z-10'>
					<div className='text-center'>
						<div className='w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin'></div>
						<p className='text-sm text-gray-600 font-medium mt-2'>
							Loading Map...
						</p>
					</div>
				</div>
			)}

			{isReady && mapRef.current && children}
		</div>
	);
};

export default GlobalMapContainer;
