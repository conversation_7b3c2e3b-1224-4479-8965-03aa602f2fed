/** @format */

'use client';

import { MediaUploadComponent } from '@/app/shared/media/components';
import { MediaItem, UploadResult } from '@/app/shared/media/types';
import { useSession } from 'next-auth/react';
import React, { useCallback, useEffect, useState } from 'react';
import { FiCalendar, FiRefreshCw, FiUpload, FiUser, FiX } from 'react-icons/fi';

/**
 * POI media gallery component props
 */
interface POIMediaGalleryProps {
	poiId: string | number;
	poiType: string;
	poiName: string;
	className?: string;
	showUploadButton?: boolean;
	allowUploads?: boolean;
	maxDisplayItems?: number;
}

/**
 * Media filter options for POI
 */
interface POIMediaFilters {
	mediaType: 'all' | 'photo' | 'video';
	sortBy: 'created_at' | 'like_count' | 'favorite_count';
	sortOrder: 'DESC' | 'ASC';
	userId?: string; // Filter by specific user
}

/**
 * POI-specific media gallery with user attribution
 */
export const POIMediaGallery: React.FC<POIMediaGalleryProps> = ({
	poiId,
	poiType,
	poiName,
	className = '',
	showUploadButton = true,
	allowUploads = true,
	maxDisplayItems,
}) => {
	const { data: session } = useSession();
	const [media, setMedia] = useState<MediaItem[]>([]);
	const [loading, setLoading] = useState(true);
	const [showUpload, setShowUpload] = useState(false);
	const filters: POIMediaFilters = {
		mediaType: 'all',
		sortBy: 'created_at',
		sortOrder: 'DESC', // Latest first
	};
	const [pagination, setPagination] = useState({
		total: 0,
		limit: maxDisplayItems || 20,
		offset: 0,
		hasMore: false,
	});

	/**
	 * Fetch POI media with filters and pagination
	 */
	const fetchMedia = useCallback(
		async (loadMore: boolean = false, customOffset?: number) => {
			setLoading(true);
			try {
				const currentLimit = maxDisplayItems || 20;
				const currentOffset =
					customOffset !== undefined
						? customOffset
						: loadMore
						? pagination.offset
						: 0;

				const params = new URLSearchParams({
					poiId: poiId.toString(),
					poiType,
					mediaType: filters.mediaType,
					sortBy: filters.sortBy,
					sortOrder: filters.sortOrder,
					limit: currentLimit.toString(),
					offset: currentOffset.toString(),
				});

				if (filters.userId) {
					params.append('userId', filters.userId);
				}

				const response = await fetch(`/api/pois/media?${params}`);
				const data = await response.json();

				if (data.success) {
					const newMedia = data.media || [];
					setMedia((prev) => (loadMore ? [...prev, ...newMedia] : newMedia));
					setPagination(() => ({
						total: data.pagination?.total || data.count || 0,
						limit: data.pagination?.limit || currentLimit,
						offset: loadMore ? currentOffset + currentLimit : 0,
						hasMore: data.pagination?.hasMore || false,
					}));
				} else {
					console.error('Failed to fetch POI media:', data.error);
				}
			} catch (error) {
				console.error('Error fetching POI media:', error);
			} finally {
				setLoading(false);
			}
		},
		[
			poiId,
			poiType,
			filters.mediaType,
			filters.sortBy,
			filters.sortOrder,
			filters.userId,
			maxDisplayItems,
		]
	);

	useEffect(() => {
		fetchMedia();
	}, [fetchMedia]);

	/**
	 * Load more media
	 */
	const loadMore = () => {
		if (!loading && pagination.hasMore) {
			const nextOffset = pagination.offset + pagination.limit;
			fetchMedia(true, nextOffset);
		}
	};

	/**
	 * Handle upload completion
	 */
	const handleUploadComplete = (results: UploadResult[]) => {
		console.log('POI media upload completed:', results);
		setShowUpload(false);
		fetchMedia(); // Refresh media list
	};

	/**
	 * Handle upload error
	 */
	const handleUploadError = (error: string) => {
		console.error('POI media upload error:', error);
		alert(error);
	};

	/**
	 * Handle media interactions
	 */
	const handleLike = async (mediaId: string) => {
		// TODO: Implement like functionality
		console.log('Like media:', mediaId);
	};

	const handleComment = async (mediaId: string) => {
		// TODO: Implement comment functionality
		console.log('Comment on media:', mediaId);
	};

	const handleDeleteMedia = async (mediaId: string) => {
		if (
			!confirm(
				'Are you sure you want to delete this media? This action cannot be undone.'
			)
		) {
			return;
		}

		try {
			const response = await fetch('/api/media/delete', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ mediaId }),
			});

			const result = await response.json();

			if (result.success) {
				console.log('Media deleted successfully');
				fetchMedia(); // Refresh the gallery
			} else {
				throw new Error(result.error || 'Delete failed');
			}
		} catch (error) {
			console.error('Delete error:', error);
			alert(
				'Failed to delete media: ' +
					(error instanceof Error ? error.message : 'Unknown error')
			);
		}
	};

	return (
		<div className={`space-y-8 p-8 ${className}`}>
			{/* Modern Header */}
			<div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6'>
				<div className='space-y-2'>
					<div className='flex items-center gap-3'>
						<div className='w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center'>
							<span className='text-white text-lg'>📸</span>
						</div>
						<h3 className='text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent'>
							Photo Gallery
						</h3>
					</div>
					<p className='text-gray-600 font-medium'>
						{pagination.total} {pagination.total === 1 ? 'photo' : 'photos'} •
						Latest first
					</p>
				</div>

				<div className='flex items-center gap-4'>
					{/* Upload Button */}
					{showUploadButton && allowUploads && (
						<button
							onClick={() => setShowUpload(true)}
							className='flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-[#33C2FF] via-[#01034F] to-[#80ED99] hover:from-[#01034F] hover:via-[#33C2FF] hover:to-[#80ED99] text-white font-semibold rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 hover:scale-105'>
							<FiUpload className='h-5 w-5' />
							<span>Add Photos</span>
						</button>
					)}
				</div>
			</div>

			{/* Modern Upload Modal */}
			{showUpload && (
				<div className='fixed inset-0 bg-gradient-to-br from-black/40 via-black/60 to-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4'>
					<div className='bg-gradient-to-br from-white via-white to-gray-50 backdrop-blur-sm rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-100'>
						<div className='p-8'>
							<div className='flex items-center justify-between mb-8'>
								<div className='flex items-center gap-3'>
									<div className='w-10 h-10 bg-gradient-to-br from-[#33C2FF] via-[#01034F] to-[#80ED99] rounded-xl flex items-center justify-center shadow-lg'>
										<FiUpload className='h-5 w-5 text-white' />
									</div>
									<div>
										<h3 className='text-xl font-bold bg-gradient-to-r from-[#01034F] via-[#33C2FF] to-[#01034F] bg-clip-text text-transparent'>
											Add Photos
										</h3>
										<p className='text-sm text-gray-600'>
											Share your experience at {poiName}
										</p>
									</div>
								</div>
								<button
									onClick={() => setShowUpload(false)}
									className='p-2 rounded-xl bg-gradient-to-r from-gray-50 via-gray-100 to-gray-50 hover:from-gray-100 hover:via-gray-200 hover:to-gray-100 text-gray-600 hover:text-gray-800 transition-all duration-200 border border-gray-200 hover:border-gray-300'>
									<FiX className='h-6 w-6' />
								</button>
							</div>

							<MediaUploadComponent
								uploadType='poi_media'
								onUploadComplete={handleUploadComplete}
								onUploadError={handleUploadError}
								maxFiles={10}
								showPOISearch={false} // POI is already selected
								preSelectedPOI={{
									poi_id: poiId,
									poi_type: poiType,
									name: poiName,
								}}
							/>
						</div>
					</div>
				</div>
			)}

			{/* Modern Media Content */}
			{loading && media.length === 0 ? (
				<div className='flex flex-col items-center justify-center py-20'>
					<div className='relative'>
						<FiRefreshCw className='h-12 w-12 animate-spin text-blue-500' />
						<div className='absolute inset-0 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full blur-xl opacity-30 animate-pulse'></div>
					</div>
					<p className='text-gray-600 font-medium mt-4'>
						Loading amazing photos...
					</p>
				</div>
			) : media.length === 0 ? (
				<div className='text-center py-20'>
					<div className='relative inline-block mb-8'>
						<div className='text-8xl mb-4 relative z-10'>📷</div>
						<div className='absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-2xl opacity-20 animate-pulse'></div>
					</div>
					<div className='max-w-md mx-auto space-y-4'>
						<h3 className='text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent'>
							No photos yet
						</h3>
						<p className='text-gray-600 leading-relaxed'>
							Be the first to capture the beauty of {poiName}. Share your
							experience with the community!
						</p>
						{allowUploads && (
							<button
								onClick={() => setShowUpload(true)}
								className='inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-[#80ED99] via-[#33C2FF] to-[#01034F] hover:from-[#01034F] hover:via-[#80ED99] hover:to-[#33C2FF] text-white font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 hover:scale-105'>
								<FiUpload className='w-5 h-5' />
								Add First Photo
							</button>
						)}
					</div>
				</div>
			) : (
				<>
					{/* Modern Photo Grid - Latest first, left to right, then next row */}
					<div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6'>
						{media.map((item, index) => (
							<div
								key={item.id}
								className='group relative bg-gradient-to-br from-white via-white to-gray-50 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-100 hover:border-[#33C2FF] overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 hover:scale-[1.02]'
								style={{
									animationDelay: `${index * 100}ms`,
									animation: 'fadeInUp 0.6s ease-out forwards',
								}}>
								{/* Media Content */}
								<div className='relative aspect-square overflow-hidden'>
									{item.media_type === 'photo' ? (
										<img
											src={item.thumbnail_url || item.media_url}
											alt={item.caption || 'Photo'}
											className='w-full h-full object-cover group-hover:scale-110 transition-transform duration-500'
											loading='lazy'
										/>
									) : (
										<div className='relative'>
											<video
												src={item.media_url}
												className='w-full h-full object-cover'
												poster={item.thumbnail_url}
											/>
											<div className='absolute top-3 left-3 bg-black/70 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-xs font-medium'>
												📹 Video
											</div>
										</div>
									)}

									{/* Hover Overlay */}
									<div className='absolute inset-0 bg-gradient-to-t from-[#01034F]/60 via-[#33C2FF]/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300'></div>

									{/* Action Buttons */}
									<div className='absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 transition-all duration-300'>
										<div className='flex items-center justify-between'>
											<div className='flex items-center gap-2'>
												<button
													onClick={() => handleLike(item.id)}
													className='flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-white/90 via-white/95 to-white/90 backdrop-blur-sm rounded-lg text-xs font-medium text-gray-700 hover:text-red-500 hover:bg-gradient-to-r hover:from-red-50 hover:via-white hover:to-red-50 transition-all duration-200 border border-white/50 hover:border-red-200'>
													❤️ {item.like_count || 0}
												</button>
												<button
													onClick={() => handleComment(item.id)}
													className='px-2 py-1 bg-gradient-to-r from-white/90 via-white/95 to-white/90 backdrop-blur-sm rounded-lg text-xs font-medium text-gray-700 hover:text-[#33C2FF] hover:bg-gradient-to-r hover:from-blue-50 hover:via-white hover:to-blue-50 transition-all duration-200 border border-white/50 hover:border-blue-200'>
													💬
												</button>
											</div>
											{/* Show delete button only for media owned by current user */}
											{session?.user?.id === (item.userId || item.user_id) && (
												<div className='relative'>
													<button
														onClick={() => handleDeleteMedia(item.id)}
														className='w-6 h-6 p-1 bg-gradient-to-br from-red-500 via-red-600 to-red-500 hover:from-red-600 hover:via-red-700 hover:to-red-600 text-white rounded-full transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg hover:scale-105'
														title='Delete media'>
														🗑️
													</button>
												</div>
											)}
										</div>
									</div>
								</div>

								{/* Caption */}
								{item.caption && (
									<div className='p-3'>
										<p className='text-sm text-gray-700 line-clamp-2 leading-relaxed'>
											{item.caption}
										</p>
									</div>
								)}

								{/* User Info */}
								<div className='px-3 pb-3'>
									<div className='flex items-center gap-2 text-xs text-gray-500'>
										<FiUser className='w-3 h-3' />
										<span>
											{item.uploaded_by_username ||
												item.uploaded_by_name ||
												'Anonymous'}
										</span>
										<span>•</span>
										<FiCalendar className='w-3 h-3' />
										<span>
											{new Date(item.created_at ?? '').toLocaleDateString()}
										</span>
									</div>
								</div>
							</div>
						))}
					</div>

					{/* Modern Load More Button */}
					{pagination.hasMore && (
						<div className='flex justify-center pt-8'>
							<button
								onClick={loadMore}
								disabled={loading}
								className='flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-[#33C2FF] via-white to-[#80ED99] hover:from-[#80ED99] hover:via-white hover:to-[#33C2FF] border border-gray-200 hover:border-[#33C2FF] text-gray-700 hover:text-[#01034F] font-semibold rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed'>
								{loading ? (
									<>
										<FiRefreshCw className='w-5 h-5 animate-spin' />
										Loading more photos...
									</>
								) : (
									<>
										<span>📸</span>
										Load More Photos
									</>
								)}
							</button>
						</div>
					)}
				</>
			)}
		</div>
	);
};

export default POIMediaGallery;
