/** @format */

'use client';

import { colors, getColorWithOpacity, gradients } from '@/app/colors';
import { BasePOI } from '@/app/shared/cards';
import POICard from '@/app/shared/cards/components/POICard';
import { useBatchMedia } from '@/app/shared/hooks/useBatchMedia';
import { POIListView } from '@/app/shared/poi/components/POIListView';
import POISimpleFilter from '@/app/shared/poi/components/POISimpleFilter';
import { LoadingSpinner } from '@/app/shared/system';
import { useBatchInteractions } from '@/app/shared/userInteractions/hooks/useBatchInteractions';
import { useRouter } from 'next/navigation';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import './poi-page.css';

interface POI {
	poi_type: string;
	poi_id: number | null;
	temp_id: number | null;
	approved_id: number | null;
	name?: string;
	name_en?: string;
	name_tr?: string;
	name_uk?: string;
	name_de?: string;
	name_ru?: string;
	name_ar?: string;
	category?: string;
	subcategory?: string;
	latitude?: number;
	longitude?: number;
	address?: string;
	city?: string;
	district?: string;
	country?: string;
	phone_number?: string;
	opening_hours?: string;
	is_favorite?: boolean;
	neighborhood?: string;
	// Interaction data from API
	like_count?: number;
	favorite_count?: number;
	visit_count?: number;
	review_count?: number;
	user_has_liked?: boolean;
	user_has_favorited?: boolean;
	user_has_visited?: boolean;
}

const DEFAULT_LIMIT = 20; // Reduced for better lazy loading performance

const POIPage: React.FC = () => {
	const router = useRouter();
	const [pois, setPois] = useState<POI[]>([]);
	const [loading, setLoading] = useState(true);
	const [loadingMore, setLoadingMore] = useState(false);
	const [hasMore, setHasMore] = useState(true);
	const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

	const [totalCount, setTotalCount] = useState(0);
	const [page, setPage] = useState(1);

	// Batch interactions hook for optimized loading
	// Note: useBatchInteractions, useBatchMedia, and POICard are imported at the top
	const {
		interactions,
		loadInteractions,
		getInteractionData,
		refreshInteractions,
		// isLoading: interactionsLoading, // Unused
		// error: interactionsError, // Unused
	} = useBatchInteractions();
	const {
		// mediaData, // Unused - using getMediaForPoi instead
		loadMedia,
		getMediaForPoi,
		// loading: mediaLoading, // Unused
		// error: mediaError, // Unused
	} = useBatchMedia();
	const [filters, setFilters] = useState({
		searchTerm: '',
		category: '',
		subcategory: '',
		city: '',
		district: '',
		neighborhood: '',
		country: '',
	});

	// Refs for infinite scroll and request deduplication
	const observerRef = useRef<IntersectionObserver | null>(null);
	const loadingRef = useRef<HTMLDivElement | null>(null);
	const isInitialLoad = useRef(true);
	const isLoadingRef = useRef(false);

	// Note: User location is not automatically requested since POI filtering
	// works without location. Location can be optionally requested through filters.

	// Load POIs function with lazy loading support
	const loadPOIs = useCallback(
		async (resetList: boolean = false, pageToLoad: number = 1) => {
			// Prevent duplicate requests with simple loading flag
			if (isLoadingRef.current) {
				return;
			}

			isLoadingRef.current = true;

			if (resetList) {
				setLoading(true);
				setPois([]);
				setPage(1);
				setHasMore(true);
			} else {
				setLoadingMore(true);
			}

			try {
				const params = new URLSearchParams();

				if (filters.city) params.append('city', filters.city);
				if (filters.district) params.append('district', filters.district);
				if (filters.country) params.append('country', filters.country);
				if (filters.category) params.append('category', filters.category);
				if (filters.subcategory)
					params.append('subcategory', filters.subcategory);
				if (filters.neighborhood)
					params.append('neighborhood', filters.neighborhood);
				if (filters.searchTerm) params.append('name', filters.searchTerm);
				params.append('limit', String(DEFAULT_LIMIT));
				params.append('page', String(pageToLoad));

				// 🚀 OPTIMIZATION: Load POIs with interactions in single call
				params.append('includeInteractions', 'true');

				const response = await fetch(`/api/pois/filter?${params.toString()}`);
				const data = await response.json();

				if (data.success) {
					const newPois = data.pois || [];

					if (resetList) {
						setPois(newPois);
					} else {
						setPois((prev) => [...prev, ...newPois]);
					}

					setTotalCount(data.totalCount);
					setHasMore(
						newPois.length === DEFAULT_LIMIT &&
							(resetList ? newPois.length : pois.length + newPois.length) <
								data.totalCount
					);

					if (!resetList) {
						setPage(pageToLoad);
					}
				}
			} catch (error) {
				console.error('Failed to load POIs:', error);
			} finally {
				isLoadingRef.current = false;
				setLoading(false);
				setLoadingMore(false);
			}
		},
		[filters, pois.length]
	);

	// Load more POIs for infinite scroll
	const loadMorePOIs = useCallback(() => {
		if (!loadingMore && hasMore) {
			loadPOIs(false, page + 1);
		}
	}, [loadPOIs, loadingMore, hasMore, page]);

	// Initial load and filter changes
	useEffect(() => {
		if (isInitialLoad.current) {
			isInitialLoad.current = false;
			loadPOIs(true, 1);
		} else {
			// Reset list when filters change
			loadPOIs(true, 1);
		}
	}, [filters]);

	// Handle filter changes from the filter component
	const handleFiltersChange = useCallback(
		(newFilters: {
			searchTerm?: string;
			category?: string;
			subcategory?: string;
			city?: string;
			district?: string;
			neighborhood?: string;
			country?: string;
		}) => {
			setFilters({
				searchTerm: newFilters.searchTerm || '',
				category: newFilters.category || '',
				subcategory: newFilters.subcategory || '',
				city: newFilters.city || '',
				district: newFilters.district || '',
				neighborhood: newFilters.neighborhood || '',
				country: newFilters.country || '',
			});
		},
		[]
	);

	// Intersection Observer for infinite scroll
	useEffect(() => {
		if (observerRef.current) {
			observerRef.current.disconnect();
		}

		observerRef.current = new IntersectionObserver(
			(entries) => {
				if (entries[0].isIntersecting && hasMore && !loading && !loadingMore) {
					loadMorePOIs();
				}
			},
			{ threshold: 0.1 }
		);

		if (loadingRef.current) {
			observerRef.current.observe(loadingRef.current);
		}

		return () => {
			if (observerRef.current) {
				observerRef.current.disconnect();
			}
		};
	}, [hasMore, loading, loadingMore, loadMorePOIs]);

	// toggleFavorite function removed as it was unused

	// Note: Pagination logic removed in favor of infinite scroll

	// Helper to get the best available name
	const getBestPOIName = (poi: POI) => {
		return (
			poi.name ||
			poi.name_en ||
			poi.name_tr ||
			poi.name_uk ||
			poi.name_de ||
			poi.name_ru ||
			poi.name_ar ||
			'Unnamed Location'
		);
	};

	// Helper function to convert POI to BasePOI format
	const convertToBasePOI = (poi: POI): BasePOI => {
		return {
			id: poi.poi_id || poi.approved_id || poi.temp_id || 0,
			poi_type: poi.poi_type,
			poi_id: poi.poi_id || undefined,
			temp_id: poi.temp_id || undefined,
			approved_id: poi.approved_id || undefined,
			name: getBestPOIName(poi),
			category: poi.category || '',
			subcategory: poi.subcategory,
			city: poi.city,
			district: poi.district,
			neighborhood: poi.neighborhood,
			country: poi.country,
			latitude: poi.latitude || 0,
			longitude: poi.longitude || 0,
			phone_number: poi.phone_number,
			opening_hours: poi.opening_hours,
			is_favorite: poi.is_favorite,
		};
	};

	// Batch interaction handlers - optimized to prevent unnecessary re-renders
	const handleLoadInteractions = useCallback(
		(poisToLoad: Array<{ poi_id: number; poi_type: string }>) => {
			if (poisToLoad.length === 0) return; // Skip empty arrays

			console.log(
				`📡 Batch loading interactions for ${poisToLoad.length} POIs`
			);
			loadInteractions(poisToLoad);

			// Also load media for these POIs
			const poisForMedia = poisToLoad.map((poi) => ({
				poi_id: poi.poi_id.toString(),
				poi_type: poi.poi_type,
			}));
			loadMedia(poisForMedia);
		},
		[loadInteractions, loadMedia]
	);

	const handleToggleInteraction = useCallback(
		async (
			poiId: number,
			poiType: string,
			interactionType: 'like' | 'favorite' | 'visit',
			action: 'add' | 'remove'
		) => {
			try {
				const response = await fetch('/api/pois/interactions', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						poiId,
						poiType,
						interactionType,
						action,
					}),
				});

				if (response.ok) {
					// Reload interactions for this POI to get updated counts
					loadInteractions([{ poi_id: poiId, poi_type: poiType }]);
				}
			} catch (error) {
				console.error('Error toggling interaction:', error);
			}
		},
		[loadInteractions]
	);

	// 🚀 OPTIMIZATION: No longer needed - POIs come with interactions from API
	// Track which POIs have had interactions loaded to prevent double loading
	// const loadedInteractionPOIs = useRef<Set<string>>(new Set());

	// 🚀 REMOVED: Batch interaction loading - now handled by POI API
	// Load batch interactions when POIs are first loaded
	// useEffect(() => {
	//   if (pois.length > 0) {
	//     // Only load interactions for POIs that haven't been loaded yet
	//     const poisToLoad = pois
	//       .map((poi) => ({
	//         poi_id: poi.poi_id || poi.approved_id || poi.temp_id || 0,
	//         poi_type: poi.poi_type,
	//       }))
	//       .filter((poi) => {
	//         const key = `${poi.poi_type}_${poi.poi_id}`;
	//         if (loadedInteractionPOIs.current.has(key)) {
	//           return false; // Skip already loaded POIs
	//         }
	//         loadedInteractionPOIs.current.add(key);
	//         return true;
	//       });

	//     if (poisToLoad.length > 0) {
	//       console.log(
	//         `🔄 Loading interactions for ${poisToLoad.length} new POIs`
	//       );
	//       handleLoadInteractions(poisToLoad);
	//     }
	//   }
	// }, [pois, handleLoadInteractions]);

	// Handle POI navigation
	const handlePOINavigate = (basePoi: BasePOI) => {
		const poiId = basePoi.poi_id || basePoi.id;
		const poiType = basePoi.poi_type;
		router.push(`/pois/${poiType}/${poiId}`);
	};

	// handlePOIFavoriteToggle function removed as it was unused

	return (
		<div
			className='min-h-screen relative'
			style={{
				background: `${gradients.background}, ${colors.neutral.cloudWhite}`,
			}}>
			{/* Enhanced Parallax Background Effects */}
			<div className='fixed inset-0 pointer-events-none overflow-hidden'>
				{/* Floating geometric shapes with enhanced animations */}
				{Array.from({ length: 15 }).map((_, i) => (
					<div
						key={i}
						className='absolute animate-pulse'
						style={{
							left: `${2 + i * 6.5}%`,
							top: `${5 + (i % 4) * 25}%`,
							width: `${12 + i * 2}px`,
							height: `${12 + i * 2}px`,
							borderRadius: i % 4 === 0 ? '50%' : i % 4 === 1 ? '20%' : '10px',
							background:
								i % 4 === 0
									? `linear-gradient(135deg, ${getColorWithOpacity(
											colors.brand.blue,
											0.3
									  )} 0%, ${getColorWithOpacity(colors.brand.blue, 0.1)} 100%)`
									: i % 4 === 1
									? `linear-gradient(135deg, ${getColorWithOpacity(
											colors.brand.green,
											0.3
									  )} 0%, ${getColorWithOpacity(
											colors.brand.green,
											0.1
									  )} 100%)`
									: i % 4 === 2
									? `linear-gradient(135deg, ${getColorWithOpacity(
											colors.supporting.lightBlue,
											0.3
									  )} 0%, ${getColorWithOpacity(
											colors.supporting.lightBlue,
											0.1
									  )} 100%)`
									: `linear-gradient(135deg, ${getColorWithOpacity(
											colors.brand.navy,
											0.2
									  )} 0%, ${getColorWithOpacity(
											colors.brand.navy,
											0.05
									  )} 100%)`,
							animationDelay: `${i * 0.4}s`,
							animationDuration: `${4 + i * 0.3}s`,
							transform: `rotate(${i * 15}deg)`,
						}}
					/>
				))}

				{/* Additional decorative elements */}
				<div
					className='absolute top-20 right-20 w-32 h-32 rounded-full opacity-10 animate-pulse'
					style={{
						background: `radial-gradient(circle, ${getColorWithOpacity(
							colors.brand.blue,
							0.4
						)} 0%, transparent 70%)`,
						animationDuration: '6s',
					}}
				/>
				<div
					className='absolute bottom-32 left-16 w-24 h-24 rounded-full opacity-10 animate-pulse'
					style={{
						background: `radial-gradient(circle, ${getColorWithOpacity(
							colors.brand.green,
							0.4
						)} 0%, transparent 70%)`,
						animationDuration: '8s',
						animationDelay: '2s',
					}}
				/>
			</div>

			{/* Modern Full-Width Hero Header */}
			<div className='relative w-full max-w-7xl mx-auto'>
				{/* Hero Section with Full-Width Design */}
				<div className='relative w-full overflow-hidden'>
					{/* Dynamic Background with Animated Elements */}
					<div className='absolute inset-0'>
						{/* Enhanced Multi-layer Gradient Overlay */}
						<div
							className='absolute inset-0'
							style={{
								background: `
									linear-gradient(135deg,
										${getColorWithOpacity(colors.brand.navy, 0.08)} 0%,
										${getColorWithOpacity(colors.brand.blue, 0.12)} 25%,
										${getColorWithOpacity(colors.brand.green, 0.08)} 50%,
										${getColorWithOpacity(colors.supporting.lightBlue, 0.1)} 75%,
										${getColorWithOpacity(colors.brand.navy, 0.05)} 100%
									),
									radial-gradient(circle at 20% 30%, ${getColorWithOpacity(
										colors.brand.blue,
										0.15
									)} 0%, transparent 50%),
									radial-gradient(circle at 80% 70%, ${getColorWithOpacity(
										colors.brand.green,
										0.15
									)} 0%, transparent 50%)
								`,
							}}
						/>

						{/* Enhanced Floating Geometric Elements */}
						<div className='absolute inset-0 overflow-hidden'>
							{Array.from({ length: 10 }).map((_, i) => (
								<div
									key={i}
									className='absolute opacity-15 animate-pulse'
									style={{
										left: `${8 + i * 9}%`,
										top: `${15 + (i % 4) * 20}%`,
										width: `${18 + i * 3}px`,
										height: `${18 + i * 3}px`,
										borderRadius:
											i % 3 === 0 ? '50%' : i % 3 === 1 ? '25%' : '15%',
										background:
											i % 4 === 0
												? `linear-gradient(45deg, ${getColorWithOpacity(
														colors.brand.blue,
														0.6
												  )} 0%, ${getColorWithOpacity(
														colors.brand.blue,
														0.2
												  )} 100%)`
												: i % 4 === 1
												? `linear-gradient(45deg, ${getColorWithOpacity(
														colors.brand.green,
														0.6
												  )} 0%, ${getColorWithOpacity(
														colors.brand.green,
														0.2
												  )} 100%)`
												: i % 4 === 2
												? `linear-gradient(45deg, ${getColorWithOpacity(
														colors.supporting.lightBlue,
														0.6
												  )} 0%, ${getColorWithOpacity(
														colors.supporting.lightBlue,
														0.2
												  )} 100%)`
												: `linear-gradient(45deg, ${getColorWithOpacity(
														colors.brand.navy,
														0.4
												  )} 0%, ${getColorWithOpacity(
														colors.brand.navy,
														0.1
												  )} 100%)`,
										animationDelay: `${i * 0.6}s`,
										animationDuration: `${4 + i * 0.4}s`,
										transform: `rotate(${i * 18}deg)`,
									}}
								/>
							))}

							{/* Decorative gradient lines */}
							<div
								className='absolute top-1/3 left-0 w-full h-px opacity-8'
								style={{
									background: `linear-gradient(90deg, transparent 0%, ${getColorWithOpacity(
										colors.brand.blue,
										0.4
									)} 50%, transparent 100%)`,
								}}
							/>
							<div
								className='absolute bottom-1/3 left-0 w-full h-px opacity-8'
								style={{
									background: `linear-gradient(90deg, transparent 0%, ${getColorWithOpacity(
										colors.brand.green,
										0.4
									)} 50%, transparent 100%)`,
								}}
							/>
						</div>
					</div>

					{/* Main Header Content */}
					<div className='relative z-10 w-full'>
						{/* Title Section */}
						<div className='w-full px-4 md:px-6 lg:px-12 py-8 md:py-12 lg:py-16'>
							<div className='flex flex-col gap-6'>
								{/* Title and Stats */}
								<div className='flex flex-col md:flex-row md:items-center md:justify-between gap-4 md:gap-6'>
									<div className='flex items-center gap-4'>
										<div
											className='w-10 h-10 md:w-12 md:h-12 rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0'
											style={{
												background: gradients.secondary,
											}}>
											<span className='text-xl md:text-2xl'>🗺️</span>
										</div>
										<div>
											<h1
												className='text-2xl md:text-3xl lg:text-4xl font-black tracking-tight leading-tight'
												style={{
													backgroundImage: gradients.primary,
													WebkitBackgroundClip: 'text',
													WebkitTextFillColor: 'transparent',
													backgroundClip: 'text',
												}}>
												Discover Places
											</h1>
											<p
												className='text-sm md:text-base font-medium mt-1'
												style={{ color: colors.neutral.slateGray }}>
												Explore amazing locations worldwide
											</p>
										</div>
									</div>

									{/* Stats Badge */}
									<div className='flex items-center gap-3'>
										<div
											className='px-4 py-2 rounded-xl backdrop-blur-sm border'
											style={{
												background: 'rgba(255, 255, 255, 0.9)',
												borderColor: colors.ui.gray200,
											}}>
											<div className='flex items-center gap-2'>
												<div
													className='w-2 h-2 rounded-full animate-pulse'
													style={{
														background: gradients.secondary,
													}}
												/>
												<span
													className='text-xs md:text-sm font-semibold'
													style={{ color: colors.neutral.textBlack }}>
													{loading
														? 'Discovering...'
														: `${totalCount.toLocaleString()} locations`}
												</span>
											</div>
										</div>
									</div>
								</div>

								{/* Filter Section */}
								<div className='w-full'>
									<POISimpleFilter
										onFiltersChange={handleFiltersChange}
										showSearch={true}
										showCategory={true}
										showLocation={true}
										layout='compact'
										placeholder='Search places...'
									/>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Modern Full-Width Content Area */}
			<div className='w-full max-w-7xl mx-auto px-4 md:px-6 lg:px-12 py-8 md:py-12 lg:py-16'>
				{loading ? (
					<div className='flex flex-col justify-center items-center h-64 space-y-4 w-full'>
						<div className='relative'>
							<div
								className='w-16 h-16 rounded-full border-4 border-t-transparent animate-spin'
								style={{
									borderColor: colors.brand.blue,
									borderTopColor: 'transparent',
								}}
							/>
							<div
								className='absolute inset-0 rounded-full blur-xl opacity-20 animate-pulse'
								style={{ backgroundColor: colors.brand.blue }}
							/>
						</div>
						<p
							className='text-base md:text-lg font-medium'
							style={{ color: colors.neutral.slateGray }}>
							Discovering amazing places...
						</p>
					</div>
				) : pois.length === 0 ? (
					<div className='text-center py-12 md:py-20'>
						<div className='relative inline-block mb-8'>
							<div className='text-6xl md:text-8xl mb-4 relative z-10'>🗺️</div>
							<div
								className='absolute inset-0 rounded-full blur-2xl opacity-20 animate-pulse'
								style={{
									background: gradients.soft,
								}}></div>
						</div>
						<div className='max-w-md mx-auto space-y-4'>
							<h3
								className='text-xl md:text-2xl font-bold'
								style={{
									backgroundImage: gradients.primary,
									WebkitBackgroundClip: 'text',
									WebkitTextFillColor: 'transparent',
									backgroundClip: 'text',
								}}>
								No locations found
							</h3>
							<p
								className='text-sm md:text-base leading-relaxed'
								style={{ color: colors.neutral.slateGray }}>
								{filters.searchTerm
									? "We couldn't find any places matching your search. Try different keywords or explore our categories."
									: 'Adjust your filters to discover amazing places around you.'}
							</p>
							{filters.searchTerm && (
								<button
									onClick={() =>
										setFilters((prev) => ({ ...prev, searchTerm: '' }))
									}
									className='inline-flex items-center gap-2 px-6 py-3 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105'
									style={{
										background: gradients.secondary,
									}}>
									<span>✨</span>
									Clear Search & Explore
								</button>
							)}
						</div>
					</div>
				) : (
					<>
						{/* Enhanced Results Header */}
						<div className='mb-6 md:mb-8'>
							<div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
								<div className='flex items-center gap-4'>
									<div
										className='w-1 h-6 md:h-8 rounded-full'
										style={{
											background: gradients.secondary,
										}}
									/>
									<h2
										className='text-xl md:text-2xl lg:text-3xl font-bold'
										style={{ color: colors.neutral.textBlack }}>
										Discovered Places
									</h2>
								</div>

								<div className='flex items-center gap-3'>
									<div
										className='text-xs md:text-sm font-semibold backdrop-blur-sm px-4 py-2 rounded-full border'
										style={{
											backgroundColor: colors.ui.blue100,
											color: colors.brand.navy,
											borderColor: colors.ui.gray200,
										}}>
										{totalCount.toLocaleString()} results
									</div>

									{/* View Toggle */}
									<div
										className='flex items-center gap-1 p-1 rounded-lg'
										style={{ backgroundColor: colors.ui.gray100 }}>
										<button
											onClick={() => setViewMode('grid')}
											className='px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2'
											style={{
												backgroundColor:
													viewMode === 'grid'
														? colors.brand.blue
														: 'transparent',
												color:
													viewMode === 'grid'
														? 'white'
														: colors.neutral.slateGray,
											}}>
											<svg
												className='w-4 h-4'
												fill='currentColor'
												viewBox='0 0 20 20'>
												<path d='M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z' />
											</svg>
											<span className='hidden sm:inline'>Grid</span>
										</button>
										<button
											onClick={() => setViewMode('list')}
											className='px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2'
											style={{
												backgroundColor:
													viewMode === 'list'
														? colors.brand.blue
														: 'transparent',
												color:
													viewMode === 'list'
														? 'white'
														: colors.neutral.slateGray,
											}}>
											<svg
												className='w-4 h-4'
												fill='currentColor'
												viewBox='0 0 20 20'>
												<path
													fillRule='evenodd'
													d='M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z'
													clipRule='evenodd'
												/>
											</svg>
											<span className='hidden sm:inline'>List</span>
										</button>
									</div>
								</div>
							</div>
						</div>

						{/* Dynamic Layout - Grid or List */}
						{viewMode === 'grid' ? (
							<div className='poi-grid grid auto-rows-fr gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'>
								{pois.map((poi, index) => (
									<div
										key={`${poi.poi_type}-${
											poi.poi_id || poi.approved_id || poi.temp_id
										}-${index}-${Object.keys(interactions).length}`}
										className='poi-card group'
										style={{
											animationDelay: `${index * 50}ms`,
											animation: 'fadeInUp 0.6s ease-out forwards',
										}}>
										<POICard
											poi={convertToBasePOI(poi)}
											isVisible={true}
											onClose={() => {}}
											onNavigate={handlePOINavigate}
											variant='inline'
											showActions={true}
											onLoadInteractions={handleLoadInteractions}
											disableInteractionAutoLoad={true} // Disable auto-loading to prevent loops
											interactionData={(() => {
												// 🚀 OPTIMIZATION: Use interaction data from POI API response
												const poiId =
													poi.poi_id || poi.approved_id || poi.temp_id || 0;

												// Check if POI has interaction data from API
												if (poi.like_count !== undefined) {
													const interactionData = {
														poi_id: poiId,
														poi_type: poi.poi_type,
														like_count: poi.like_count || 0,
														favorite_count: poi.favorite_count || 0,
														visit_count: poi.visit_count || 0,
														review_count: poi.review_count || 0,
														media_count: 0, // Not included in POI API
														user_has_liked: poi.user_has_liked || false,
														user_has_favorited: poi.user_has_favorited || false,
														user_has_visited: poi.user_has_visited || false,
													};

													return interactionData;
												}

												// Fallback to batch hook (for backward compatibility)
												const fallbackData = getInteractionData({
													poi_id: poiId,
													poi_type: poi.poi_type,
												});

												// Add missing media_count field to match POIInteractionData interface
												return fallbackData
													? {
															...fallbackData,
															media_count: 0, // Default value for media_count
													  }
													: null;
											})()}
											onToggleInteraction={handleToggleInteraction}
											useBatchLoading={true}
											useSimpleUI={true}
											showPoiId={false}
											onRefreshInteractions={refreshInteractions}
											mediaData={getMediaForPoi(
												(
													poi.poi_id ||
													poi.approved_id ||
													poi.temp_id ||
													0
												).toString()
											)}
										/>
									</div>
								))}
							</div>
						) : (
							<POIListView
								pois={pois}
								onPOIClick={(poi) => {
									const basePoi = convertToBasePOI(poi);
									handlePOINavigate(basePoi);
								}}
								onToggleFavorite={(poi) => {
									const poiId = Number(
										poi.poi_id || poi.approved_id || poi.temp_id || 0
									);
									handleToggleInteraction(
										poiId,
										poi.poi_type,
										'favorite',
										'add'
									);
								}}
								interactions={interactions}
							/>
						)}

						{/* Modern Loading Indicator */}
						{hasMore && (
							<div
								ref={loadingRef}
								className='flex justify-center items-center py-12'>
								{loadingMore ? (
									<div className='flex flex-col items-center gap-4'>
										<div className='relative'>
											<LoadingSpinner />
											<div className='absolute inset-0 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full blur-xl opacity-30 animate-pulse'></div>
										</div>
										<div className='text-center'>
											<p className='text-gray-600 font-medium'>
												Discovering more places...
											</p>
											<p className='text-sm text-gray-500 mt-1'>
												Finding hidden gems for you
											</p>
										</div>
									</div>
								) : (
									<div className='text-center py-4'>
										<div className='inline-flex items-center gap-2 px-4 py-2 bg-white/60 backdrop-blur-sm rounded-full border border-white/20 text-gray-600'>
											<div className='w-2 h-2 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full animate-pulse'></div>
											<span className='text-sm font-medium'>
												Scroll to discover more
											</span>
										</div>
									</div>
								)}
							</div>
						)}

						{/* Modern End Indicator */}
						{!hasMore && pois.length > 0 && (
							<div className='text-center py-12'>
								<div className='inline-flex flex-col items-center gap-3 px-6 py-4 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20'>
									<div className='w-12 h-12 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center'>
										<span className='text-2xl'>🎉</span>
									</div>
									<div className='text-center'>
										<p className='font-semibold text-gray-800'>
											You've explored everything!
										</p>
										<p className='text-sm text-gray-600 mt-1'>
											Discovered all {totalCount.toLocaleString()} amazing
											location{totalCount !== 1 ? 's' : ''}
										</p>
									</div>
								</div>
							</div>
						)}
					</>
				)}
			</div>

			{/* Add custom CSS for animations */}
			<style jsx>{`
				@keyframes fadeInUp {
					from {
						opacity: 0;
						transform: translateY(30px);
					}
					to {
						opacity: 1;
						transform: translateY(0);
					}
				}
			`}</style>
		</div>
	);
};

export default POIPage;
