/** @format */

'use client';

import { POI_EDITABLE_FIELDS } from '@/app/shared/poi';
import { logger } from '@/lib/logger';
import { Session } from 'next-auth';
import { useEffect, useState } from 'react';
import {
	FaCheck,
	FaChevronLeft,
	FaChevronRight,
	FaClock,
	FaEdit,
	FaExclamationTriangle,
	FaEye,
	FaFilter,
	FaInfoCircle,
	FaMapMarkerAlt,
	FaPlus,
	FaSort,
	FaSpinner,
	FaTimes,
	FaTimesCircle,
	FaUser,
} from 'react-icons/fa';

interface UserPOISubmission {
	id: number; // BIGINT from spatial_schema.user_pois_temp.id
	temp_poi_id: number; // BIGINT from spatial_schema.user_pois_temp.id (same as id)
	submitted_by_user_id: string; // UUID from backend_schema.nextauth_users.id
	submitter_name: string;
	submitter_email: string;
	name: string;
	category: string;
	subcategory: string;
	cuisine: string;
	latitude: number;
	longitude: number;
	city: string;
	district: string;
	neighborhood: string;
	full_address: string;
	phone_number: string;
	opening_hours: string;
	description: string;
	submission_notes: string;
	submission_reason: string;
	submission_type?: string; // New field: 'new_poi' | 'info_update' | 'closure_request'
	target_poi_id?: number; // BIGINT from spatial_schema.pois.id (for updates/closures)
	admin_review_status: 'pending' | 'reviewing' | 'approved' | 'rejected';
	admin_review_notes: string;
	reviewed_by: string;
	reviewed_at: string;
	created_at: string;
	updated_at: string;
	original_poi_type?: string;
	original_poi_id?: string; // BIGINT from spatial_schema.pois.id (legacy field)
	[key: string]: unknown; // For dynamic field access
}

interface DashboardStats {
	total_pending: number;
	total_reviewing: number;
	total_approved: number;
	total_rejected: number;
	pending_this_week: number;
	approved_this_week: number;
}

interface POIDetails {
	id: number;
	name: string;
	category: string;
	subcategory?: string;
	latitude: number;
	longitude: number;
	city: string;
	district?: string;
	neighborhood?: string;
	full_address?: string;
	phone_number?: string;
	opening_hours?: string;
	description?: string;
	[key: string]: unknown; // For dynamic field access
}

const statusColors = {
	pending: 'bg-yellow-100 text-yellow-800',
	reviewing: 'bg-blue-100 text-blue-800',
	approved: 'bg-green-100 text-green-800',
	rejected: 'bg-red-100 text-red-800',
};

const statusIcons = {
	pending: FaClock,
	reviewing: FaEye,
	approved: FaCheck,
	rejected: FaTimes,
};

interface AgentDashboardClientProps {
	session: Session;
}

export default function AgentDashboardClient({
	session,
}: AgentDashboardClientProps) {
	// State
	const [submissions, setSubmissions] = useState<UserPOISubmission[]>([]);
	const [stats, setStats] = useState<DashboardStats | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [selectedSubmissions, setSelectedSubmissions] = useState<Set<number>>(
		new Set()
	);
	const [bulkAction, setBulkAction] = useState<string>('');
	const [isProcessingBulk, setIsProcessingBulk] = useState(false);

	// Filters and pagination
	const [statusFilter, setStatusFilter] = useState<string>('all');
	const [sortBy, setSortBy] = useState<string>('created_at');
	const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
	const [currentPage, setCurrentPage] = useState(1);
	const [itemsPerPage] = useState(20);

	const [activeTab, setActiveTab] = useState<
		'new_poi' | 'updates' | 'closures'
	>('new_poi');
	const [submissionTypeFilter, setSubmissionTypeFilter] =
		useState<string>('new_poi');

	const [showDetailModal, setShowDetailModal] = useState(false);
	const [detailSubmission, setDetailSubmission] =
		useState<UserPOISubmission | null>(null);
	const [detailPoi, setDetailPoi] = useState<POIDetails | null>(null);
	const [detailLoading, setDetailLoading] = useState(false);

	// Confirmation modal state
	const [showConfirmModal, setShowConfirmModal] = useState(false);
	const [confirmAction, setConfirmAction] = useState<{
		type: 'approve' | 'reject';
		submissionId: number;
		operationType: string;
		title: string;
		message: string;
		confirmText: string;
		onConfirm: () => void;
	} | null>(null);

	// Operation type helpers
	const getOperationType = (submission: UserPOISubmission) => {
		if (submission.submission_reason === 'info_update') return 'update';
		if (submission.submission_reason === 'closed') return 'closure';
		return 'new_poi';
	};

	const getOperationIcon = (operationType: string) => {
		switch (operationType) {
			case 'new_poi':
				return FaPlus;
			case 'update':
				return FaEdit;
			case 'closure':
				return FaTimesCircle;
			default:
				return FaInfoCircle;
		}
	};

	const getOperationColor = (operationType: string) => {
		switch (operationType) {
			case 'new_poi':
				return 'bg-green-100 text-green-800 border-green-200';
			case 'update':
				return 'bg-blue-100 text-blue-800 border-blue-200';
			case 'closure':
				return 'bg-red-100 text-red-800 border-red-200';
			default:
				return 'bg-gray-100 text-gray-800 border-gray-200';
		}
	};

	const getOperationLabel = (operationType: string) => {
		switch (operationType) {
			case 'new_poi':
				return 'NEW POI';
			case 'update':
				return 'UPDATE';
			case 'closure':
				return 'CLOSURE';
			default:
				return 'UNKNOWN';
		}
	};

	// Confirmation modal helpers
	const showConfirmation = (action: {
		type: 'approve' | 'reject';
		submissionId: number;
		operationType: string;
		onConfirm: () => void;
	}) => {
		const { type, submissionId, operationType, onConfirm } = action;

		let title = '';
		let message = '';
		let confirmText = '';

		switch (type) {
			case 'approve':
				title = `Approve ${operationType}`;
				message = `Are you sure you want to approve this ${operationType.toLowerCase()} submission? This action will move the POI to the main database.`;
				confirmText = 'Approve';
				break;
			case 'reject':
				title = `Reject ${operationType}`;
				message = `Are you sure you want to reject this ${operationType.toLowerCase()} submission? This will remove it from the system and cannot be undone.`;
				confirmText = 'Reject';
				break;
		}

		setConfirmAction({
			type,
			submissionId,
			operationType,
			title,
			message,
			confirmText,
			onConfirm,
		});
		setShowConfirmModal(true);
	};

	const handleConfirmAction = () => {
		if (confirmAction) {
			confirmAction.onConfirm();
			setShowConfirmModal(false);
			setConfirmAction(null);
		}
	};

	const handleCancelAction = () => {
		setShowConfirmModal(false);
		setConfirmAction(null);
	};

	useEffect(() => {
		loadDashboardData();
	}, [statusFilter, sortBy, sortOrder, currentPage, submissionTypeFilter]);

	const loadDashboardData = async () => {
		setIsLoading(true);
		setError(null);

		try {
			// Load submissions with submission type filter
			const submissionsResponse = await fetch(
				`/api/agent/submissions?status=${statusFilter}&sortBy=${sortBy}&sortOrder=${sortOrder}&page=${currentPage}&limit=${itemsPerPage}&submission_reason=${submissionTypeFilter}`
			);

			if (!submissionsResponse.ok) {
				throw new Error('Failed to load submissions');
			}

			const submissionsData = await submissionsResponse.json();
			setSubmissions(submissionsData.submissions || []);

			// Load stats
			const statsResponse = await fetch('/api/agent/stats');
			if (statsResponse.ok) {
				const statsData = await statsResponse.json();
				setStats(statsData.stats);
			}
		} catch (error) {
			setError('Failed to load dashboard data');
			logger.error('Error loading agent dashboard', { error });
		} finally {
			setIsLoading(false);
		}
	};

	const handleSubmissionAction = async (
		submissionId: number,
		action: string,
		notes?: string
	) => {
		try {
			const response = await fetch('/api/agent/submissions/action', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					submission_id: submissionId,
					action,
					notes,
				}),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to process action');
			}

			// Show success message
			setError(null);

			// Reload data
			loadDashboardData();
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : 'Failed to process action';
			setError(errorMessage);
			logger.error('Error processing submission action', {
				error: error instanceof Error ? error.message : String(error),
				submissionId,
				action,
			});
		}
	};

	const handleBulkAction = async () => {
		if (!bulkAction || selectedSubmissions.size === 0) return;

		setIsProcessingBulk(true);
		try {
			const response = await fetch('/api/agent/submissions/bulk-action', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					submission_ids: Array.from(selectedSubmissions),
					action: bulkAction,
				}),
			});

			if (!response.ok) {
				throw new Error('Failed to process bulk action');
			}

			// Clear selections and reload
			setSelectedSubmissions(new Set());
			setBulkAction('');
			loadDashboardData();
		} catch (error) {
			setError('Failed to process bulk action');
			logger.error('Error processing bulk action', { error, bulkAction });
		} finally {
			setIsProcessingBulk(false);
		}
	};

	const toggleSubmissionSelection = (id: number) => {
		const newSelection = new Set(selectedSubmissions);
		if (newSelection.has(id)) {
			newSelection.delete(id);
		} else {
			newSelection.add(id);
		}
		setSelectedSubmissions(newSelection);
	};

	const selectAllSubmissions = () => {
		if (selectedSubmissions.size === submissions.length) {
			setSelectedSubmissions(new Set());
		} else {
			setSelectedSubmissions(new Set(submissions.map((s) => s.id)));
		}
	};

	const handleViewDetails = async (submission: UserPOISubmission) => {
		setShowDetailModal(true);
		setDetailSubmission(submission);
		setDetailPoi(null);
		setDetailLoading(true);
		// Only fetch main POI if this is an update or removal
		if (
			submission.submission_reason === 'info_update' ||
			submission.submission_reason === 'closed'
		) {
			try {
				// DEBUG: Log the original_poi_id
				console.log('DEBUG: original_poi_id', submission.original_poi_id);
				if (submission.original_poi_id) {
					const res = await fetch(
						`/api/pois/official/${encodeURIComponent(
							submission.original_poi_id
						)}`
					);
					// DEBUG: Log the raw response
					const raw = await res.clone().text();
					console.log('DEBUG: fetch response', raw);
					if (res.ok) {
						const data = await res.json();
						setDetailPoi(data.poi);
					}
				} else {
					setDetailPoi(null);
					logger.warn(
						'original_poi_id is missing for this submission. Cannot fetch old POI data.'
					);
				}
			} catch {
				setDetailPoi(null);
				logger.error('Failed to fetch old POI data.');
			}
		}
		setDetailLoading(false);
	};

	if (isLoading) {
		return (
			<div className='min-h-screen flex items-center justify-center'>
				<FaSpinner className='animate-spin text-4xl text-blue-500' />
			</div>
		);
	}

	return (
		<div className='min-h-screen bg-gray-50'>
			{/* Header */}
			<div className='bg-white shadow-sm border-b'>
				<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6'>
					<div className='flex justify-between items-center'>
						<div>
							<h1 className='text-3xl font-bold text-gray-900'>
								Agent Dashboard
							</h1>
							<p className='text-gray-600 mt-1'>
								Review and manage user-submitted POIs
							</p>
						</div>
						<div className='flex items-center space-x-4'>
							<span className='text-sm text-gray-500'>
								Welcome, {session.user?.name || session.user?.email}
							</span>
						</div>
					</div>
				</div>
			</div>

			{/* Tabs */}
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6'>
				<div className='flex space-x-4 border-b mb-6'>
					<button
						className={`px-4 py-2 font-medium border-b-2 transition-colors ${
							activeTab === 'new_poi'
								? 'border-green-600 text-green-700'
								: 'border-transparent text-gray-500 hover:text-green-600'
						}`}
						onClick={() => {
							setActiveTab('new_poi');
							setSubmissionTypeFilter('new_poi');
						}}>
						<FaPlus className='inline mr-1 mb-0.5' /> New POI Submissions
					</button>
					<button
						className={`px-4 py-2 font-medium border-b-2 transition-colors ${
							activeTab === 'updates'
								? 'border-blue-600 text-blue-700'
								: 'border-transparent text-gray-500 hover:text-blue-600'
						}`}
						onClick={() => {
							setActiveTab('updates');
							setSubmissionTypeFilter('info_update');
						}}>
						<FaEdit className='inline mr-1 mb-0.5' /> Update Requests
					</button>
					<button
						className={`px-4 py-2 font-medium border-b-2 transition-colors ${
							activeTab === 'closures'
								? 'border-red-600 text-red-700'
								: 'border-transparent text-gray-500 hover:text-red-600'
						}`}
						onClick={() => {
							setActiveTab('closures');
							setSubmissionTypeFilter('closed');
						}}>
						<FaTimesCircle className='inline mr-1 mb-0.5' /> Closure Requests
					</button>
				</div>

				{/* Tab Description */}
				<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-6'>
					<div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
						<div className='flex items-start'>
							<FaInfoCircle className='text-blue-500 mt-0.5 mr-3 flex-shrink-0' />
							<div className='text-sm text-blue-800'>
								{activeTab === 'new_poi' && (
									<>
										<strong>New POI Submissions:</strong> Users are requesting
										to add completely new Points of Interest to the database.
										Approving these will create new POI entries with unique IDs.
									</>
								)}
								{activeTab === 'updates' && (
									<>
										<strong>Update Requests:</strong> Users are suggesting
										changes to existing POI information. Approving these will
										update the data in the main POI table while preserving the
										original POI ID.
									</>
								)}
								{activeTab === 'closures' && (
									<>
										<strong>Closure Requests:</strong> Users are reporting that
										POIs are closed, no longer exist, or have major issues.
										Approving these will mark the POI as inactive or remove it
										from the database.
									</>
								)}
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Tab Content */}
			<>
				{/* Stats Cards */}
				{stats && (
					<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6'>
						<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
							<div className='bg-white rounded-lg shadow p-6'>
								<div className='flex items-center'>
									<FaClock className='text-yellow-500 text-2xl mr-3' />
									<div>
										<p className='text-sm font-medium text-gray-600'>
											Pending Review
										</p>
										<p className='text-2xl font-bold text-gray-900'>
											{stats.total_pending}
										</p>
									</div>
								</div>
							</div>
							<div className='bg-white rounded-lg shadow p-6'>
								<div className='flex items-center'>
									<FaEye className='text-blue-500 text-2xl mr-3' />
									<div>
										<p className='text-sm font-medium text-gray-600'>
											Under Review
										</p>
										<p className='text-2xl font-bold text-gray-900'>
											{stats.total_reviewing}
										</p>
									</div>
								</div>
							</div>
							<div className='bg-white rounded-lg shadow p-6'>
								<div className='flex items-center'>
									<FaCheck className='text-green-500 text-2xl mr-3' />
									<div>
										<p className='text-sm font-medium text-gray-600'>
											Approved
										</p>
										<p className='text-2xl font-bold text-gray-900'>
											{stats.total_approved}
										</p>
									</div>
								</div>
							</div>
							<div className='bg-white rounded-lg shadow p-6'>
								<div className='flex items-center'>
									<FaTimes className='text-red-500 text-2xl mr-3' />
									<div>
										<p className='text-sm font-medium text-gray-600'>
											Rejected
										</p>
										<p className='text-2xl font-bold text-gray-900'>
											{stats.total_rejected}
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				)}

				{/* Error Message */}
				{error && (
					<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-6'>
						<div className='bg-red-50 border border-red-200 rounded-lg p-4 flex items-center'>
							<FaTimes className='text-red-500 mr-3' />
							<span className='text-red-700'>{error}</span>
						</div>
					</div>
				)}

				{/* Main Content */}
				<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12'>
					{/* Filters and Controls */}
					<div className='bg-white rounded-lg shadow mb-6 p-6'>
						<div className='flex flex-wrap items-center justify-between gap-4'>
							<div className='flex items-center space-x-4'>
								<div className='flex items-center space-x-2'>
									<FaFilter className='text-gray-400' />
									<select
										value={statusFilter}
										onChange={(e) => setStatusFilter(e.target.value)}
										className='border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500'>
										<option value='all'>All Status</option>
										<option value='pending'>Pending</option>
										<option value='reviewing'>Under Review</option>
										<option value='approved'>Approved</option>
										<option value='rejected'>Rejected</option>
									</select>
								</div>

								<div className='flex items-center space-x-2'>
									<FaSort className='text-gray-400' />
									<select
										value={`${sortBy}-${sortOrder}`}
										onChange={(e) => {
											const [field, order] = e.target.value.split('-');
											setSortBy(field);
											setSortOrder(order as 'asc' | 'desc');
										}}
										className='border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500'>
										<option value='created_at-desc'>Newest First</option>
										<option value='created_at-asc'>Oldest First</option>
										<option value='name-asc'>Name A-Z</option>
										<option value='name-desc'>Name Z-A</option>
									</select>
								</div>
							</div>

							{/* Bulk Actions */}
							{selectedSubmissions.size > 0 && (
								<div className='flex items-center space-x-2'>
									<span className='text-sm text-gray-600'>
										{selectedSubmissions.size} selected
									</span>
									<select
										value={bulkAction}
										onChange={(e) => setBulkAction(e.target.value)}
										className='border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500'>
										<option value=''>Bulk Actions</option>
										<option value='approve'>Approve Selected</option>
										<option value='reject'>Reject Selected</option>
										<option value='review'>Mark as Under Review</option>
									</select>
									<button
										onClick={handleBulkAction}
										disabled={!bulkAction || isProcessingBulk}
										className='bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center'>
										{isProcessingBulk ? (
											<FaSpinner className='animate-spin mr-2' />
										) : null}
										Apply
									</button>
								</div>
							)}
						</div>
					</div>

					{/* Submissions Table */}
					<div className='bg-white rounded-lg shadow overflow-hidden'>
						<div className='overflow-x-auto'>
							<table className='min-w-full divide-y divide-gray-200'>
								<thead className='bg-gray-50'>
									<tr>
										<th className='px-6 py-3 text-left'>
											<input
												type='checkbox'
												checked={
													selectedSubmissions.size === submissions.length &&
													submissions.length > 0
												}
												onChange={selectAllSubmissions}
												className='rounded border-gray-300 text-blue-600 focus:ring-blue-500'
											/>
										</th>
										<th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
											Operation Type
										</th>
										<th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
											POI Details
										</th>
										<th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
											Location
										</th>
										<th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
											Submitter
										</th>
										<th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
											Status
										</th>
										<th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
											Submitted
										</th>
										<th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
											Actions
										</th>
									</tr>
								</thead>
								<tbody className='bg-white divide-y divide-gray-200'>
									{submissions.map((submission) => {
										const StatusIcon =
											statusIcons[submission.admin_review_status];
										const operationType = getOperationType(submission);
										const OperationIcon = getOperationIcon(operationType);
										const operationColor = getOperationColor(operationType);
										const operationLabel = getOperationLabel(operationType);

										return (
											<tr
												key={submission.id}
												className='hover:bg-gray-50'>
												<td className='px-6 py-4'>
													<input
														type='checkbox'
														checked={selectedSubmissions.has(submission.id)}
														onChange={() =>
															toggleSubmissionSelection(submission.id)
														}
														className='rounded border-gray-300 text-blue-600 focus:ring-blue-500'
													/>
												</td>
												<td className='px-6 py-4'>
													<div
														className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${operationColor}`}>
														<OperationIcon className='mr-1.5' />
														{operationLabel}
													</div>
													{submission.original_poi_id && (
														<div className='text-xs text-gray-500 mt-1'>
															<span className='font-medium'>Target POI:</span> #
															{submission.original_poi_id}
														</div>
													)}
													{operationType === 'update' && (
														<div className='text-xs text-blue-600 mt-1'>
															📝 Updating existing POI data
														</div>
													)}
													{operationType === 'closure' && (
														<div className='text-xs text-red-600 mt-1'>
															🚫 Requesting POI removal
														</div>
													)}
													{operationType === 'new_poi' && (
														<div className='text-xs text-green-600 mt-1'>
															✨ Adding new POI
														</div>
													)}
												</td>
												<td className='px-6 py-4'>
													<div>
														<div className='text-sm font-medium text-gray-900'>
															{submission.name}
														</div>
														<div className='text-sm text-gray-500'>
															{submission.subcategory
																? `${submission.category} • ${submission.subcategory}`
																: submission.category}
														</div>
														{submission.cuisine && (
															<div className='text-xs text-gray-400'>
																{submission.cuisine}
															</div>
														)}
													</div>
												</td>
												<td className='px-6 py-4'>
													<div className='text-sm text-gray-900 flex items-center'>
														<FaMapMarkerAlt className='mr-1 text-gray-400' />
														{submission.city}, {submission.district}
													</div>
													<div className='text-xs text-gray-500'>
														{typeof submission.latitude === 'number' &&
														typeof submission.longitude === 'number'
															? `${submission.latitude.toFixed(
																	4
															  )}, ${submission.longitude.toFixed(4)}`
															: submission.latitude && submission.longitude
															? `${submission.latitude}, ${submission.longitude}`
															: '-'}
													</div>
												</td>
												<td className='px-6 py-4'>
													<div className='flex items-center'>
														<FaUser className='mr-2 text-gray-400' />
														<div>
															<div className='text-sm text-gray-900'>
																{submission.submitter_name || 'Anonymous'}
															</div>
															<div className='text-xs text-gray-500'>
																{submission.submitter_email ||
																	'No email provided'}
															</div>
														</div>
													</div>
												</td>
												<td className='px-6 py-4'>
													<span
														className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
															statusColors[submission.admin_review_status]
														}`}>
														<StatusIcon className='mr-1' />
														{submission.admin_review_status}
													</span>
												</td>
												<td className='px-6 py-4 text-sm text-gray-500'>
													{new Date(submission.created_at).toLocaleDateString()}
												</td>
												<td className='px-6 py-4'>
													<div className='flex flex-col space-y-2'>
														{/* Safe Action - View Details */}
														<button
															onClick={() => handleViewDetails(submission)}
															className='inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors'
															title='View Details'>
															<FaEye className='mr-1.5' />
															View
														</button>

														{/* Dangerous Actions - Only for pending submissions */}
														{submission.admin_review_status === 'pending' && (
															<div className='flex space-x-2'>
																<button
																	onClick={() =>
																		showConfirmation({
																			type: 'approve',
																			submissionId: submission.id,
																			operationType: operationLabel,
																			onConfirm: () =>
																				handleSubmissionAction(
																					submission.id,
																					'approve'
																				),
																		})
																	}
																	className='inline-flex items-center px-3 py-1.5 text-sm font-medium text-green-700 bg-green-50 border border-green-200 rounded-md hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1 transition-colors'
																	title={`Approve ${operationLabel}`}>
																	<FaCheck className='mr-1.5' />
																	Approve
																</button>
																<button
																	onClick={() =>
																		showConfirmation({
																			type: 'reject',
																			submissionId: submission.id,
																			operationType: operationLabel,
																			onConfirm: () =>
																				handleSubmissionAction(
																					submission.id,
																					'reject'
																				),
																		})
																	}
																	className='inline-flex items-center px-3 py-1.5 text-sm font-medium text-red-700 bg-red-50 border border-red-200 rounded-md hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1 transition-colors'
																	title={`Reject ${operationLabel}`}>
																	<FaTimes className='mr-1.5' />
																	Reject
																</button>
															</div>
														)}
													</div>
												</td>
											</tr>
										);
									})}
								</tbody>
							</table>
						</div>

						{submissions.length === 0 && !isLoading && (
							<div className='text-center py-12'>
								<FaMapMarkerAlt className='mx-auto text-gray-400 text-4xl mb-4' />
								<h3 className='text-lg font-medium text-gray-900 mb-2'>
									No submissions found
								</h3>
								<p className='text-gray-500'>
									No POI submissions match your current filters.
								</p>
							</div>
						)}

						{/* Pagination */}
						{submissions.length > 0 && (
							<div className='bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6'>
								<div className='flex-1 flex justify-between sm:hidden'>
									<button
										onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
										disabled={currentPage === 1}
										className='relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50'>
										Previous
									</button>
									<button
										onClick={() => setCurrentPage(currentPage + 1)}
										disabled={submissions.length < itemsPerPage}
										className='ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50'>
										Next
									</button>
								</div>
								<div className='hidden sm:flex-1 sm:flex sm:items-center sm:justify-between'>
									<div>
										<p className='text-sm text-gray-700'>
											Showing page{' '}
											<span className='font-medium'>{currentPage}</span>
										</p>
									</div>
									<div>
										<nav className='relative z-0 inline-flex rounded-md shadow-sm -space-x-px'>
											<button
												onClick={() =>
													setCurrentPage(Math.max(1, currentPage - 1))
												}
												disabled={currentPage === 1}
												className='relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50'>
												<FaChevronLeft />
											</button>
											<button
												onClick={() => setCurrentPage(currentPage + 1)}
												disabled={submissions.length < itemsPerPage}
												className='relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50'>
												<FaChevronRight />
											</button>
										</nav>
									</div>
								</div>
							</div>
						)}
					</div>
				</div>
			</>

			{/* Detail Modal */}
			{showDetailModal && detailSubmission && (
				<div className='fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm'>
					<div className='bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto'>
						<div className='flex items-center justify-between p-6 border-b'>
							<h2 className='text-xl font-bold'>Submission Details</h2>
							<button
								onClick={() => setShowDetailModal(false)}
								className='p-2 rounded-lg hover:bg-gray-100'>
								<FaTimes className='w-5 h-5' />
							</button>
						</div>
						<div className='p-6'>
							{detailLoading ? (
								<div className='flex items-center justify-center py-8'>
									<FaSpinner className='animate-spin text-2xl text-blue-500' />
								</div>
							) : (
								<>
									<div className='mb-6'>
										{/* Operation Type Badge */}
										<div className='flex items-center mb-3'>
											{(() => {
												const operationType =
													getOperationType(detailSubmission);
												const OperationIcon = getOperationIcon(operationType);
												const operationColor = getOperationColor(operationType);
												const operationLabel = getOperationLabel(operationType);

												return (
													<div
														className={`inline-flex items-center px-4 py-2 rounded-lg text-sm font-semibold border ${operationColor}`}>
														<OperationIcon className='mr-2' />
														{operationLabel} SUBMISSION
													</div>
												);
											})()}
										</div>

										{/* Operation Context */}
										<div className='bg-gray-50 rounded-lg p-4 mb-4'>
											<h4 className='font-semibold text-gray-900 mb-2'>
												Operation Context
											</h4>
											{detailSubmission.submission_reason === 'info_update' && (
												<div className='text-sm text-gray-700'>
													<p>
														📝 <strong>Updating existing POI:</strong> #
														{detailSubmission.original_poi_id}
													</p>
													<p className='mt-1'>
														This submission contains updated information for an
														existing POI in the database.
													</p>
												</div>
											)}
											{detailSubmission.submission_reason === 'closed' && (
												<div className='text-sm text-gray-700'>
													<p>
														🚫 <strong>Requesting closure of POI:</strong> #
														{detailSubmission.original_poi_id}
													</p>
													<p className='mt-1'>
														User is reporting that this POI is closed, no longer
														exists, or has incorrect information.
													</p>
												</div>
											)}
											{(!detailSubmission.submission_reason ||
												detailSubmission.submission_reason === 'new_poi') && (
												<div className='text-sm text-gray-700'>
													<p>
														✨ <strong>Adding new POI to database</strong>
													</p>
													<p className='mt-1'>
														This submission will create a completely new POI
														entry in the main database.
													</p>
												</div>
											)}
										</div>

										{/* Submitter Info */}
										<div className='text-sm text-gray-600'>
											<span className='font-medium'>Submitted by:</span>{' '}
											{detailSubmission.submitter_name || 'Anonymous'}
											{detailSubmission.submitter_email && (
												<span className='text-gray-500'>
													{' '}
													({detailSubmission.submitter_email})
												</span>
											)}
											<span className='ml-4 text-gray-500'>
												on{' '}
												{new Date(
													detailSubmission.created_at
												).toLocaleDateString()}
											</span>
										</div>
									</div>
									{/* Info Update: Side-by-side diff */}
									{detailSubmission.submission_reason === 'info_update' && (
										<div>
											<h3 className='font-semibold mb-2'>Field Changes</h3>
											<table className='min-w-full text-sm mb-4 border rounded'>
												<thead className='bg-gray-50'>
													<tr>
														<th className='text-left pr-4 py-2'>Field</th>
														<th className='text-left pr-4 py-2'>Old</th>
														<th className='text-left py-2'>New</th>
													</tr>
												</thead>
												<tbody>
													{POI_EDITABLE_FIELDS.map((k) => (
														<tr
															key={k}
															className='border-t'>
															<td className='pr-4 py-1 font-medium text-gray-700'>
																{k}
															</td>
															<td className='pr-4 py-1 text-gray-500'>
																{detailPoi &&
																detailPoi[k] !== undefined &&
																detailPoi[k] !== null &&
																typeof detailPoi[k] !== 'object' ? (
																	String(detailPoi[k])
																) : (
																	<span className='text-gray-300'>-</span>
																)}
															</td>
															<td className='py-1'>
																{detailSubmission[k] !== undefined &&
																detailSubmission[k] !== null &&
																typeof detailSubmission[k] !== 'object' ? (
																	String(detailSubmission[k])
																) : (
																	<span className='text-gray-300'>-</span>
																)}
															</td>
														</tr>
													))}
												</tbody>
											</table>
										</div>
									)}
									{/* Removal: Reason and all current POI data */}
									{detailSubmission.submission_reason === 'closed' && (
										<div>
											<h3 className='font-semibold mb-2'>Removal Request</h3>
											<div className='mb-2 text-sm'>
												<span className='font-medium'>Reason:</span>{' '}
												{detailSubmission.submission_notes}
											</div>
											<h4 className='font-semibold mt-4 mb-1'>
												Current POI Data
											</h4>
											<table className='min-w-full text-sm mb-4 border rounded'>
												<tbody>
													{detailPoi &&
														Object.entries(detailPoi).map(([k, v]) => (
															<tr
																key={k}
																className='border-t'>
																<td className='pr-4 py-1 font-medium text-gray-700'>
																	{k}
																</td>
																<td className='py-1'>
																	{v === null || v === undefined ? (
																		<span className='text-gray-300'>-</span>
																	) : typeof v === 'object' ? (
																		<span className='text-gray-300'>-</span>
																	) : (
																		String(v)
																	)}
																</td>
															</tr>
														))}
												</tbody>
											</table>
										</div>
									)}
									{/* New POI: All fields in a table */}
									{(!detailSubmission.submission_reason ||
										detailSubmission.submission_reason === 'new_poi') && (
										<div>
											<h3 className='font-semibold mb-2'>New POI Data</h3>
											<table className='min-w-full text-sm mb-4 border rounded'>
												<tbody>
													{Object.entries(detailSubmission)
														.filter(
															([k]) => typeof detailSubmission[k] !== 'object'
														)
														.map(([k, v]) => (
															<tr
																key={k}
																className='border-t'>
																<td className='pr-4 py-1 font-medium text-gray-700'>
																	{k}
																</td>
																<td className='py-1'>
																	{v === null || v === undefined ? (
																		<span className='text-gray-300'>-</span>
																	) : typeof v === 'object' ? (
																		<span className='text-gray-300'>-</span>
																	) : (
																		String(v)
																	)}
																</td>
															</tr>
														))}
												</tbody>
											</table>
										</div>
									)}
								</>
							)}
						</div>
					</div>
				</div>
			)}

			{/* Confirmation Modal */}
			{showConfirmModal && confirmAction && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
					<div className='bg-white rounded-lg shadow-xl max-w-md w-full mx-4'>
						<div className='p-6'>
							<div className='flex items-center mb-4'>
								<FaExclamationTriangle className='text-yellow-500 text-2xl mr-3' />
								<h3 className='text-lg font-semibold text-gray-900'>
									{confirmAction.title}
								</h3>
							</div>

							<p className='text-gray-600 mb-6'>{confirmAction.message}</p>

							<div className='flex justify-end space-x-3'>
								<button
									onClick={handleCancelAction}
									className='px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1 transition-colors'>
									Cancel
								</button>
								<button
									onClick={handleConfirmAction}
									className={`px-4 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-1 transition-colors ${
										confirmAction.type === 'approve'
											? 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
											: 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
									}`}>
									{confirmAction.confirmText}
								</button>
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
