/** @format */

'use client';

import { useEffect } from 'react';

interface AuthPageLayoutProps {
	children: React.ReactNode;
}

export default function AuthPageLayout({ children }: AuthPageLayoutProps) {
	useEffect(() => {
		// Override body styles for auth page to ensure scrolling works
		const originalBodyStyle = document.body.style.cssText;
		const originalHtmlStyle = document.documentElement.style.cssText;

		// Apply scrollable styles
		document.body.style.height = 'auto';
		document.body.style.minHeight = '100vh';
		document.body.style.overflowY = 'auto';
		document.documentElement.style.height = 'auto';
		document.documentElement.style.minHeight = '100vh';

		// Cleanup on unmount
		return () => {
			document.body.style.cssText = originalBodyStyle;
			document.documentElement.style.cssText = originalHtmlStyle;
		};
	}, []);

	return (
		<div className='min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4 py-8 sm:py-12 overflow-y-auto'>
			<div className='w-full max-w-md mx-auto min-h-0'>
				{children}
			</div>
		</div>
	);
}
