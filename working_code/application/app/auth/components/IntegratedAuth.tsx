/** @format */

'use client';

import { Button, Card, Input } from '@/app/shared/ui';
import { signIn, useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

type AuthMode = 'signin' | 'signup';

interface IntegratedAuthProps {
	defaultMode?: AuthMode;
	onSuccess?: () => void;
	className?: string;
}

interface PasswordValidation {
	length: boolean;
	uppercase: boolean;
	lowercase: boolean;
	number: boolean;
}

export default function IntegratedAuth({
	defaultMode = 'signup',
	onSuccess,
	className = '',
}: IntegratedAuthProps) {
	const router = useRouter();
	const { status } = useSession();

	const [mode, setMode] = useState<AuthMode>(defaultMode);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState('');

	const [formData, setFormData] = useState({
		email: '',
		password: '',
		confirmPassword: '',
		username: '',
		name: '',
		age: '',
	});
	const [acceptTerms, setAcceptTerms] = useState(false);
	const [allowDataUsage, setAllowDataUsage] = useState(false);
	const [usernameStatus, setUsernameStatus] = useState<{
		checking: boolean;
		available: boolean | null;
		message: string;
	}>({ checking: false, available: null, message: '' });

	// Password validation state
	const [passwordValidation, setPasswordValidation] =
		useState<PasswordValidation>({
			length: false,
			uppercase: false,
			lowercase: false,
			number: false,
		});

	const validatePassword = (password: string): PasswordValidation => {
		return {
			length: password.length >= 8,
			uppercase: /[A-Z]/.test(password),
			lowercase: /[a-z]/.test(password),
			number: /\d/.test(password),
		};
	};

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setFormData((prev) => ({
			...prev,
			[e.target.name]: e.target.value,
		}));
		// Clear error when user starts typing
		if (error) setError('');

		// Check username availability for signup mode
		if (e.target.name === 'username' && mode === 'signup') {
			checkUsernameAvailability(e.target.value);
		}

		// Update password validation for signup mode
		if (e.target.name === 'password' && mode === 'signup') {
			setPasswordValidation(validatePassword(e.target.value));
		}
	};

	const checkUsernameAvailability = async (username: string) => {
		if (!username || username.length < 3) {
			setUsernameStatus({ checking: false, available: null, message: '' });
			return;
		}

		setUsernameStatus({
			checking: true,
			available: null,
			message: 'Checking availability...',
		});

		try {
			const response = await fetch('/api/auth/check-username', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ username }),
			});

			const data = await response.json();

			if (response.ok) {
				setUsernameStatus({
					checking: false,
					available: data.available,
					message: data.message,
				});
			} else {
				setUsernameStatus({
					checking: false,
					available: false,
					message: data.error || 'Error checking username',
				});
			}
		} catch {
			setUsernameStatus({
				checking: false,
				available: false,
				message: 'Error checking username availability',
			});
		}
	};

	const validateForm = () => {
		if (!formData.email || !formData.password) {
			setError('Email and password are required');
			return false;
		}

		if (mode === 'signup') {
			if (!formData.name || !formData.age || !formData.username) {
				setError('Name, age, and username are required');
				return false;
			}

			if (!acceptTerms) {
				setError('You must accept the Terms & Conditions to create an account');
				return false;
			}

			if (formData.password !== formData.confirmPassword) {
				setError('Passwords do not match');
				return false;
			}

			if (parseInt(formData.age) < 13) {
				setError('You must be at least 13 years old');
				return false;
			}

			if (!usernameStatus.available) {
				setError('Please choose an available username');
				return false;
			}
		}

		return true;
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!validateForm()) return;

		setIsLoading(true);
		setError('');

		try {
			if (mode === 'signup') {
				// Call our signup API to create the user
				const res = await fetch('/api/auth/signup', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						email: formData.email,
						password: formData.password,
						username: formData.username,
						name: formData.name,
						age: parseInt(formData.age),
						allowDataUsage,
					}),
				});
				const data = await res.json();
				if (!res.ok) {
					setError(data.error || 'Signup failed');
					setIsLoading(false);
					return;
				}
				// After signup, sign in with credentials
				const signInResult = await signIn('credentials', {
					redirect: false,
					email: formData.email,
					password: formData.password,
				});
				if (signInResult?.error) {
					setError(signInResult.error);
					setIsLoading(false);
					return;
				}
				router.push('/welcome');
				onSuccess?.();
			} else {
				// Sign in with credentials
				const signInResult = await signIn('credentials', {
					redirect: false,
					email: formData.email,
					password: formData.password,
				});
				if (signInResult?.error) {
					setError(signInResult.error);
					setIsLoading(false);
					return;
				}
				router.push('/chat');
				onSuccess?.();
			}
		} catch (error) {
			console.error('Auth error:', error);
			setError('Something went wrong. Please try again.');
		} finally {
			setIsLoading(false);
		}
	};

	const toggleMode = () => {
		setMode((prev) => (prev === 'signin' ? 'signup' : 'signin'));
		setError('');
		setFormData({
			email: '',
			password: '',
			confirmPassword: '',
			username: '',
			name: '',
			age: '',
		});
		setAcceptTerms(false);
		setAllowDataUsage(false);
		setPasswordValidation({
			length: false,
			uppercase: false,
			lowercase: false,
			number: false,
		});
	};

	// If already authenticated, redirect
	if (status === 'authenticated') {
		router.push('/chat');
		return null;
	}

	return (
		<div className={`w-full max-w-md mx-auto ${className}`}>
			<Card
				padding='lg'
				className='max-h-none overflow-visible w-full'>
				{/* Mode Toggle */}
				<div className='flex bg-gray-100 rounded-lg p-1 mb-6'>
					<button
						type='button'
						onClick={() => setMode('signup')}
						className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
							mode === 'signup'
								? 'bg-white text-blue-600 shadow-sm'
								: 'text-gray-600 hover:text-gray-900'
						}`}>
						Sign Up
					</button>
					<button
						type='button'
						onClick={() => setMode('signin')}
						className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
							mode === 'signin'
								? 'bg-white text-blue-600 shadow-sm'
								: 'text-gray-600 hover:text-gray-900'
						}`}>
						Sign In
					</button>
				</div>

				{/* Header */}
				<div className='text-center mb-6'>
					<h2 className='text-2xl font-bold text-gray-900'>
						{mode === 'signup' ? 'Create Account' : 'Welcome Back'}
					</h2>
					<p className='text-gray-600 mt-1'>
						{mode === 'signup'
							? 'Start your global exploration journey'
							: 'Continue your exploration'}
					</p>
				</div>

				{/* Error Message */}
				{error && (
					<div className='mb-4 p-3 bg-red-50 border border-red-200 rounded-lg'>
						<p className='text-red-800 text-sm'>{error}</p>
					</div>
				)}

				{/* Form */}
				<form
					onSubmit={handleSubmit}
					className='space-y-4'>
					{/* Signup-only fields */}
					{mode === 'signup' && (
						<>
							<Input
								label='Username'
								name='username'
								type='text'
								value={formData.username}
								onChange={handleInputChange}
								required
								placeholder='johndoe123'
								helperText={
									usernameStatus.checking
										? usernameStatus.message
										: usernameStatus.available === true
										? usernameStatus.message
										: usernameStatus.available === false
										? usernameStatus.message
										: '3-50 characters, letters, numbers, underscore, and hyphen only'
								}
								error={
									usernameStatus.available === false
										? usernameStatus.message
										: undefined
								}
							/>
							<div className='grid grid-cols-2 gap-4'>
								<Input
									label='Full Name'
									name='name'
									type='text'
									value={formData.name}
									onChange={handleInputChange}
									required
									placeholder='John Doe'
								/>
								<Input
									label='Age'
									name='age'
									type='number'
									min='13'
									max='120'
									value={formData.age}
									onChange={handleInputChange}
									required
									placeholder='25'
								/>
							</div>
						</>
					)}

					{/* Common fields */}
					<Input
						label='Email'
						name='email'
						type='email'
						value={formData.email}
						onChange={handleInputChange}
						required
						placeholder='<EMAIL>'
					/>

					<Input
						label='Password'
						name='password'
						type='password'
						value={formData.password}
						onChange={handleInputChange}
						required
						minLength={8}
						placeholder='Enter your password'
						helperText={
							mode === 'signup'
								? 'Must be at least 8 characters with uppercase, lowercase, and numbers'
								: undefined
						}
					/>

					{/* Password validation indicators for signup */}
					{mode === 'signup' && formData.password && (
						<div className='space-y-2'>
							<div className='text-sm font-medium text-gray-700'>
								Password requirements:
							</div>
							<div className='space-y-1'>
								<div
									className={`flex items-center gap-2 text-sm ${
										passwordValidation.length
											? 'text-green-600'
											: 'text-gray-500'
									}`}>
									{passwordValidation.length ? (
										<svg
											className='w-4 h-4'
											fill='currentColor'
											viewBox='0 0 20 20'>
											<path
												fillRule='evenodd'
												d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
												clipRule='evenodd'
											/>
										</svg>
									) : (
										<svg
											className='w-4 h-4'
											fill='currentColor'
											viewBox='0 0 20 20'>
											<path
												fillRule='evenodd'
												d='M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z'
												clipRule='evenodd'
											/>
										</svg>
									)}
									At least 8 characters
								</div>
								<div
									className={`flex items-center gap-2 text-sm ${
										passwordValidation.uppercase
											? 'text-green-600'
											: 'text-gray-500'
									}`}>
									{passwordValidation.uppercase ? (
										<svg
											className='w-4 h-4'
											fill='currentColor'
											viewBox='0 0 20 20'>
											<path
												fillRule='evenodd'
												d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
												clipRule='evenodd'
											/>
										</svg>
									) : (
										<svg
											className='w-4 h-4'
											fill='currentColor'
											viewBox='0 0 20 20'>
											<path
												fillRule='evenodd'
												d='M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z'
												clipRule='evenodd'
											/>
										</svg>
									)}
									One uppercase letter
								</div>
								<div
									className={`flex items-center gap-2 text-sm ${
										passwordValidation.lowercase
											? 'text-green-600'
											: 'text-gray-500'
									}`}>
									{passwordValidation.lowercase ? (
										<svg
											className='w-4 h-4'
											fill='currentColor'
											viewBox='0 0 20 20'>
											<path
												fillRule='evenodd'
												d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
												clipRule='evenodd'
											/>
										</svg>
									) : (
										<svg
											className='w-4 h-4'
											fill='currentColor'
											viewBox='0 0 20 20'>
											<path
												fillRule='evenodd'
												d='M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z'
												clipRule='evenodd'
											/>
										</svg>
									)}
									One lowercase letter
								</div>
								<div
									className={`flex items-center gap-2 text-sm ${
										passwordValidation.number
											? 'text-green-600'
											: 'text-gray-500'
									}`}>
									{passwordValidation.number ? (
										<svg
											className='w-4 h-4'
											fill='currentColor'
											viewBox='0 0 20 20'>
											<path
												fillRule='evenodd'
												d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
												clipRule='evenodd'
											/>
										</svg>
									) : (
										<svg
											className='w-4 h-4'
											fill='currentColor'
											viewBox='0 0 20 20'>
											<path
												fillRule='evenodd'
												d='M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z'
												clipRule='evenodd'
											/>
										</svg>
									)}
									One number
								</div>
							</div>
						</div>
					)}

					{/* Signup-only password confirmation */}
					{mode === 'signup' && (
						<Input
							label='Confirm Password'
							name='confirmPassword'
							type='password'
							value={formData.confirmPassword}
							onChange={handleInputChange}
							required
							minLength={8}
							placeholder='Confirm your password'
						/>
					)}

					{/* Submit Button */}
					<Button
						type='submit'
						disabled={isLoading}
						loading={isLoading}
						className='w-full'
						size='lg'>
						{mode === 'signup' ? 'Create Account' : 'Sign In'}
					</Button>
				</form>

				{/* Footer */}
				<div className='mt-6 text-center'>
					<p className='text-sm text-gray-600'>
						{mode === 'signup'
							? 'Already have an account?'
							: "Don't have an account?"}{' '}
						<button
							type='button'
							onClick={toggleMode}
							className='text-blue-600 hover:text-blue-700 font-medium'>
							{mode === 'signup' ? 'Sign in' : 'Sign up'}
						</button>
					</p>
				</div>

				{/* Terms & Data Usage (signup only) */}
				{mode === 'signup' && (
					<div className='mt-6 space-y-4 border-t pt-4'>
						{/* Terms Acceptance - Required */}
						<div className='flex items-start gap-3'>
							<input
								type='checkbox'
								id='acceptTerms'
								checked={acceptTerms}
								onChange={(e) => setAcceptTerms(e.target.checked)}
								className='mt-1 w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 flex-shrink-0'
								required
							/>
							<label
								htmlFor='acceptTerms'
								className='text-sm text-gray-700 leading-relaxed'>
								<span className='font-medium'>I accept the </span>
								<button
									type='button'
									onClick={() => router.push('/settings?tab=legal')}
									className='text-blue-600 hover:text-blue-700 underline'>
									Terms & Conditions
								</button>
								<span className='font-medium'> and </span>
								<button
									type='button'
									onClick={() => router.push('/settings?tab=legal')}
									className='text-blue-600 hover:text-blue-700 underline'>
									Privacy Policy
								</button>
								<span className='text-red-500 ml-1'>*</span>
							</label>
						</div>

						{/* Data Usage - Optional */}
						<div className='flex items-start gap-3'>
							<input
								type='checkbox'
								id='allowDataUsage'
								checked={allowDataUsage}
								onChange={(e) => setAllowDataUsage(e.target.checked)}
								className='mt-1 w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 flex-shrink-0'
							/>
							<label
								htmlFor='allowDataUsage'
								className='text-sm text-gray-700 leading-relaxed'>
								<span className='font-medium'>Allow anonymous data usage</span>{' '}
								to help improve our AI
								<span className='block text-xs text-gray-500 mt-1 leading-relaxed'>
									Your chat interactions (without personal info) may be used to
									train our AI. You can change this anytime in settings.
								</span>
							</label>
						</div>

						<div className='text-xs text-gray-500 mt-2'>
							<span className='text-red-500'>*</span> Required field
						</div>
					</div>
				)}
			</Card>
		</div>
	);
}
