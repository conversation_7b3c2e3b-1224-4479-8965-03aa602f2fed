import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { handleApiError, AuthenticationError } from '@/lib/errors'
import { logger } from '@/lib/logger'
import { withPerformanceMonitoring } from '@/lib/performance'
import { authRateLimit } from '@/lib/rateLimit'
import bcrypt from 'bcryptjs'

async function signinHandler(request: NextRequest) {
  // Apply rate limiting
  const rateLimitResponse = await authRateLimit(request)
  if (rateLimitResponse) {
    return rateLimitResponse
  }

  try {
    const { email, password } = await request.json()

    // Validate input
    if (!email || !password) {
      throw new AuthenticationError('Email and password are required')
    }

    // Find user by email
    const user = await db.getOne(
      'SELECT id, email, password_hash FROM users WHERE email = $1',
      [email]
    )

    if (!user) {
      logger.authEvent('signin', email, false, { reason: 'user_not_found' })
      throw new AuthenticationError('Invalid credentials')
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash)

    if (!isValidPassword) {
      logger.authEvent('signin', user.id, false, { reason: 'invalid_password' })
      throw new AuthenticationError('Invalid credentials')
    }

    logger.authEvent('signin', user.id, true)

    // Return user without password
    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email
      }
    })

  } catch (error) {
    return handleApiError(error)
  }
}

export const POST = withPerformanceMonitoring(signinHandler, 'auth/signin')
