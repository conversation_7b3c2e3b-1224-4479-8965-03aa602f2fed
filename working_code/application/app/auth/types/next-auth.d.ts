import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      email: string
      username: string
      name?: string | null
      image?: string | null
      profile_picture_url?: string | null
      role: 'user' | 'agent' | 'superuser'
      permissions: string[]
    }
  }

  interface User {
    id: string
    email: string
    username: string
    name?: string | null
    image?: string | null
    profile_picture_url?: string | null
    role: 'user' | 'agent' | 'superuser'
    permissions: string[]
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string
    username: string
    profile_picture_url?: string | null
    role: 'user' | 'agent' | 'superuser'
    permissions: string[]
  }
}
