/** @format */

import { config } from '@/lib/config';
import { deductCredits, hasCredits } from '@/lib/credits';
import { db } from '@/lib/database';
import {
	handleApiError,
	handleLLMEngineError,
	ValidationError,
} from '@/lib/errors';
import { logger } from '@/lib/logger';
import {
	monitorLLMRequest,
	withPerformanceMonitoring,
} from '@/lib/performance';
import { chatRateLimit, withRateLimit } from '@/lib/rateLimit';
import { sanitizeObject, validateMessage } from '@/lib/security-headers';
import { serverAuthHelpers } from '@/lib/server-auth';
import { resolveToUsername } from '@/lib/username-helpers';
import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';

async function handleChatMessage(request: NextRequest) {
	const requestId = uuidv4();
	const requestLogger = logger;

	try {
		const body = await request.json();

		// Sanitize all input
		const sanitizedBody = sanitizeObject(body);
		const { sessionId, message, userId, userLat, userLng } = sanitizedBody;

		// Enhanced validation
		if (!sessionId || !message || !userId) {
			throw new ValidationError(
				'Session ID, message, and user ID are required'
			);
		}

		// Enhanced message validation
		const messageValidation = validateMessage(message);
		if (!messageValidation.isValid) {
			throw new ValidationError(messageValidation.error!);
		}

		// Resolve userId to username for the LLM request
		const resolvedUsername = await resolveToUsername(userId);
		if (!resolvedUsername) {
			throw new ValidationError('User not found');
		}

		// Check if user has credits for this request (1 credit per request)
		const userHasCredits = await hasCredits(userId, 1);
		if (!userHasCredits) {
			return NextResponse.json(
				{
					error: 'Insufficient credits. You need 1 credit to send a message.',
					code: 'INSUFFICIENT_CREDITS',
					required_credits: 1,
				},
				{ status: 402 } // Payment Required
			);
		}

		// Get user profile and settings using user ID (direct database call, no HTTP request)
		const { data: profile } = await serverAuthHelpers.getProfile(userId);
		const locationSettings = await db.getOne(
			'SELECT * FROM backend_schema.user_location_settings WHERE user_id = $1',
			[userId]
		);

		// Enhanced location validation
		if (
			userLat !== null &&
			userLat !== undefined &&
			userLng !== null &&
			userLng !== undefined
		) {
			if (typeof userLat !== 'number' || typeof userLng !== 'number') {
				throw new ValidationError('Invalid location coordinates format');
			}
			if (userLat < -90 || userLat > 90 || userLng < -180 || userLng > 180) {
				throw new ValidationError('Location coordinates out of valid range');
			}
		}

		// Location settings should exist (created during signup), but create if missing
		let finalLocationSettings = locationSettings;
		if (!locationSettings) {
			requestLogger.info('Creating missing location settings for user', {
				userId,
				username: resolvedUsername,
			});
			finalLocationSettings = await db.insert(
				'backend_schema.user_location_settings',
				{
					user_id: userId,
					search_radius: 5000,
					num_candidates: 3,
				}
			);
		}

		// Use default values if somehow not set (safety check)
		const searchRadius = finalLocationSettings.search_radius || 5000;
		const numCandidates = finalLocationSettings.num_candidates || 3;

		requestLogger.info('Chat message received', {
			sessionId,
			messageLength: message.length,
			userId,
			username: resolvedUsername,
			hasProfile: !!profile,
			hasLocationSettings: !!locationSettings,
		});

		// Send message to LLM engine with monitoring
		const responseData = await monitorLLMRequest(
			'send_message',
			async () => {
				const response = await fetch(`${config.llmEngine.url}/message`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
						...(config.llmEngine.apiKey && {
							Authorization: `Bearer ${config.llmEngine.apiKey}`,
						}),
					},
					body: JSON.stringify({
						user_id: resolvedUsername, // Use username instead of user ID for LLM requests
						session_id: sessionId,
						message: message,
						latitude: userLat || null,
						longitude: userLng || null,
						search_radius: searchRadius,
						num_candidates: numCandidates,
					}),
					signal: AbortSignal.timeout(config.llmEngine.timeout),
				});

				if (!response.ok) {
					const errorText = await response.text();
					throw { status: response.status, message: errorText };
				}

				return response.json();
			},
			{
				sessionId,
				userId,
				username: resolvedUsername,
				messageLength: message.length,
			}
		);

		requestLogger.info('Chat message processed successfully', {
			sessionId,
			userId,
			username: resolvedUsername,
			responseLength: responseData.reply?.length || 0,
		});

		// Deduct 1 credit for the successful request
		const creditDeducted = await deductCredits(
			userId,
			1,
			'chat_request',
			`Chat message request in session ${sessionId}`,
			'chat_message',
			sessionId
		);

		if (!creditDeducted) {
			requestLogger.warn('Failed to deduct credits after successful request', {
				userId,
				sessionId,
			});
		}

		return NextResponse.json({
			reply:
				responseData.response || responseData.reply || responseData.message,
			topCandidates: responseData.top_candidates || null,
			sessionTitle: responseData.session_title || null,
			messageId: responseData.message_id || `msg-${Date.now()}`,
			timestamp: responseData.timestamp || new Date().toISOString(),
			requestId,
		});
	} catch (error) {
		requestLogger.error('Chat message processing failed', {
			error: error instanceof Error ? error.message : 'Unknown error',
			requestId,
		});

		// Handle LLM engine specific errors
		if (error && typeof error === 'object' && 'status' in error) {
			throw handleLLMEngineError(error);
		}

		throw error;
	}
}

export const POST = withRateLimit(
	withPerformanceMonitoring(async (request: NextRequest) => {
		try {
			return await handleChatMessage(request);
		} catch (error) {
			return handleApiError(error, uuidv4());
		}
	}, 'chat_message'),
	chatRateLimit
);
