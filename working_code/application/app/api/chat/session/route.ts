import { NextRequest, NextResponse } from 'next/server'
import { config } from '@/lib/config'
import { resolveToUsername } from '@/lib/username-helpers'

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json()

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Convert userId to username for LLM engine
    const resolvedUsername = await resolveToUsername(userId)
    if (!resolvedUsername) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Create session with LLM engine using username
    const response = await fetch(`${config.llmEngine.url}/session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: resolvedUsername // Use username instead of user ID
      }),
      signal: AbortSignal.timeout(config.llmEngine.timeout)
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`LLM Engine session creation failed: ${response.status} - ${errorText}`)
    }

    const sessionData = await response.json()

    return NextResponse.json({
      sessionId: sessionData.session_id,
      message: 'Session created successfully'
    })

  } catch (error) {
    console.error('Session creation error:', error)
    return NextResponse.json(
      { error: 'Failed to create chat session' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      )
    }

    // Get session info from LLM engine
    const response = await fetch(`${config.llmEngine.url}/session/${sessionId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(config.llmEngine.timeout)
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`LLM Engine session fetch failed: ${response.status} - ${errorText}`)
    }

    const sessionData = await response.json()
    return NextResponse.json(sessionData)

  } catch (error) {
    console.error('Session fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch session' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { sessionId, userId } = await request.json()

    if (!sessionId || !userId) {
      return NextResponse.json(
        { error: 'Session ID and User ID are required' },
        { status: 400 }
      )
    }

    // Convert userId to username for LLM engine
    const resolvedUsername = await resolveToUsername(userId)
    if (!resolvedUsername) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Delete session from LLM engine using username
    const response = await fetch(`${config.llmEngine.url}/delete`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: resolvedUsername, // Use username instead of user ID
        session_id: sessionId
      }),
      signal: AbortSignal.timeout(config.llmEngine.timeout)
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`LLM Engine session deletion failed: ${response.status} - ${errorText}`)
    }

    return NextResponse.json({
      success: true,
      message: 'Session deleted successfully'
    })

  } catch (error) {
    console.error('Session deletion error:', error)
    return NextResponse.json(
      { error: 'Failed to delete chat session' },
      { status: 500 }
    )
  }
}
