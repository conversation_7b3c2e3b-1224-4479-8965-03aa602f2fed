import { NextRequest, NextResponse } from 'next/server'
import { config } from '@/lib/config'
import { resolveToUsername } from '@/lib/username-helpers'

export async function POST(request: NextRequest) {
  try {
    const { userId, sessionId } = await request.json()

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      )
    }

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Convert userId to username for LLM engine
    const resolvedUsername = await resolveToUsername(userId)
    if (!resolvedUsername) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Get messages from LLM engine
    const response = await fetch(`${config.llmEngine.url}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: resolvedUsername,
        session_id: sessionId
      }),
      signal: AbortSignal.timeout(config.llmEngine.timeout)
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`LLM Engine messages fetch failed: ${response.status} - ${errorText}`)
    }

    const messagesData = await response.json()
    return NextResponse.json(messagesData)

  } catch (error) {
    console.error('Messages fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch session messages' },
      { status: 500 }
    )
  }
}
