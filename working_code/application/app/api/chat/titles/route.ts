import { NextRequest, NextResponse } from 'next/server'
import { config } from '@/lib/config'
import { resolveToUsername } from '@/lib/username-helpers'

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json()

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Convert userId to username for LLM engine
    const resolvedUsername = await resolveToUsername(userId)
    if (!resolvedUsername) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    try {
      // Get session titles from LLM engine
      const response = await fetch(`${config.llmEngine.url}/titles`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: resolvedUsername
        }),
        signal: AbortSignal.timeout(config.llmEngine.timeout)
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`LLM Engine titles fetch failed: ${response.status} - ${errorText}`)
      }

      const titlesData = await response.json()
      return NextResponse.json(titlesData)

    } catch (llmError) {
      // Graceful fallback when LLM Engine is not available
      const errorMessage = llmError instanceof Error ? llmError.message : 'Unknown error'
      console.warn('LLM Engine not available, returning empty titles:', errorMessage)
      
      return NextResponse.json({
        titles: [],
        message: 'LLM Engine temporarily unavailable, showing empty chat history'
      })
    }

  } catch (error) {
    console.error('Titles fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch session titles' },
      { status: 500 }
    )
  }
}
