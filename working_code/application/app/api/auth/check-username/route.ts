import { NextRequest, NextResponse } from 'next/server'
import { db, table } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    const { username } = await request.json()

    // Validate input
    if (!username) {
      return NextResponse.json(
        { error: 'Username is required' },
        { status: 400 }
      )
    }

    // Validate username format
    const usernameRegex = /^[a-zA-Z0-9_-]{3,50}$/
    if (!usernameRegex.test(username)) {
      return NextResponse.json(
        { 
          available: false, 
          error: 'Username must be 3-50 characters and contain only letters, numbers, underscores, and hyphens' 
        },
        { status: 400 }
      )
    }

    // Check if username already exists in nextauth_users table
    const existingUser = await db.getOne(
      `SELECT id FROM ${table('nextauth_users')} WHERE username = $1`,
      [username.toLowerCase()] // Store usernames in lowercase for consistency
    )

    const available = !existingUser

    return NextResponse.json({
      available,
      username: username.toLowerCase(),
      message: available ? 'Username is available' : 'Username is already taken'
    })

  } catch (error) {
    console.error('Check username error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
