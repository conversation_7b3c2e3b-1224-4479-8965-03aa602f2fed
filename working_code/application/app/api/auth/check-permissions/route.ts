import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/nextauth-options'
import { db } from '@/lib/database'
import { logger } from '@/lib/logger'

// POST /api/auth/check-permissions - Check user permissions
export async function POST(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { 
          success: false, 
          hasAccess: false, 
          error: 'Authentication required',
          role: null,
          permissions: []
        },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { requiredRole, requiredPermissions = [] } = body

    // Get user role and permissions from database
    const userResult = await db.query(`
      SELECT 
        role,
        permissions
      FROM backend_schema.nextauth_users 
      WHERE id = $1
    `, [session.user.id])

    if (userResult.rows.length === 0) {
      logger.warn('User not found in database', { userId: session.user.id })
      return NextResponse.json(
        { 
          success: false, 
          hasAccess: false, 
          error: 'User not found',
          role: null,
          permissions: []
        },
        { status: 404 }
      )
    }

    const user = userResult.rows[0]
    const userRole = user.role
    const userPermissions = user.permissions || []

    // Check role requirement
    let hasRoleAccess = true
    if (requiredRole) {
      switch (requiredRole) {
        case 'superuser':
          hasRoleAccess = userRole === 'superuser'
          break
        case 'agent':
          hasRoleAccess = userRole === 'agent' || userRole === 'superuser'
          break
        case 'user':
          hasRoleAccess = true // All authenticated users have user access
          break
        default:
          hasRoleAccess = false
      }
    }

    // Check specific permissions
    let hasPermissionAccess = true
    if (requiredPermissions.length > 0) {
      // Superusers with full_access have all permissions
      if (userRole === 'superuser' && userPermissions.includes('full_access')) {
        hasPermissionAccess = true
      } else {
        // Check if user has all required permissions
        hasPermissionAccess = requiredPermissions.every((permission: string) => 
          userPermissions.includes(permission)
        )
      }
    }

    const hasAccess = hasRoleAccess && hasPermissionAccess

    // Log access check
    logger.info('Permission check', {
      userId: session.user.id,
      userRole,
      requiredRole,
      requiredPermissions,
      hasAccess,
      hasRoleAccess,
      hasPermissionAccess
    })

    // Log security event if access denied
    if (!hasAccess) {
      await db.query(`
        INSERT INTO backend_schema.agent_activity_log 
        (user_id, activity_type, target_type, target_id, activity_data, ip_address, user_agent)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [
        session.user.id,
        'permission_check_denied',
        'security',
        'permission_check',
        JSON.stringify({
          requiredRole,
          requiredPermissions,
          userRole,
          userPermissions: userPermissions
        }),
        request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        request.headers.get('user-agent') || 'unknown'
      ])
    }

    return NextResponse.json({
      success: true,
      hasAccess,
      role: userRole,
      permissions: userPermissions,
      checks: {
        hasRoleAccess,
        hasPermissionAccess,
        requiredRole,
        requiredPermissions
      }
    })

  } catch (error) {
    logger.error('Error checking permissions', { error })
    return NextResponse.json(
      { 
        success: false, 
        hasAccess: false, 
        error: 'Permission check failed',
        role: null,
        permissions: []
      },
      { status: 500 }
    )
  }
}
