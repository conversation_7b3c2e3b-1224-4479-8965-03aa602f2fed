/** @format */

import { MediaManager } from '@/app/shared/media/utils';
import { db, table } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
	try {
		// Get user session
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const userId = session.user.id;

		const profile = await db.getOne(
			`SELECT * FROM ${table('nextauth_users')} WHERE id = $1`,
			[userId]
		);

		return NextResponse.json({ profile });
	} catch (error) {
		console.error('Get profile error:', error);
		return NextResponse.json(
			{ error: 'Internal server error' },
			{ status: 500 }
		);
	}
}

export async function POST(request: NextRequest) {
	try {
		// Get user session
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ error: 'Authentication required' },
				{ status: 401 }
			);
		}

		if (request.headers.get('content-type')?.includes('multipart/form-data')) {
			try {
				const formData = await request.formData();
				const file = formData.get('avatar') as File;

				if (!file) {
					return NextResponse.json(
						{ error: 'No file uploaded' },
						{ status: 400 }
					);
				}

				logger.info('Processing profile picture upload', {
					userId: session.user.id,
					fileName: file.name,
					fileSize: file.size,
					fileType: file.type,
				});

				// Use MediaManager to process and save profile picture
				const result = await MediaManager.saveProfilePicture(
					session.user.id,
					file
				);

				if (!result.success) {
					return NextResponse.json(
						{ error: result.error || 'Failed to upload profile picture' },
						{ status: 400 }
					);
				}

				// Update user profile with new profile picture URL
				const profile = await db.update(
					table('nextauth_users'),
					session.user.id,
					{
						profile_picture_url: result.profilePictureUrl,
					}
				);

				logger.info('Profile picture uploaded successfully', {
					userId: session.user.id,
					profilePictureUrl: result.profilePictureUrl,
				});

				return NextResponse.json({
					success: true,
					profile,
					profilePictureUrl: result.profilePictureUrl,
					thumbnails: result.thumbnails,
					warnings: result.warnings,
				});
			} catch (error) {
				logger.error('Error uploading profile picture', {
					error,
					userId: session.user.id,
				});
				return NextResponse.json(
					{ error: 'Failed to upload profile picture' },
					{ status: 500 }
				);
			}
		}

		const { name, age, avatar_url, profile_completed } = await request.json();

		if (!name || !age) {
			return NextResponse.json(
				{ error: 'Name and age are required' },
				{ status: 400 }
			);
		}

		const userId = session.user.id;

		// Update user profile in nextauth_users table
		const profile = await db.update(table('nextauth_users'), userId, {
			name,
			age: parseInt(age),
			avatar_url,
			profile_completed:
				profile_completed !== undefined ? profile_completed : true,
		});

		return NextResponse.json({ profile });
	} catch (error) {
		console.error('Update profile error:', error);
		return NextResponse.json(
			{ error: 'Internal server error' },
			{ status: 500 }
		);
	}
}

export async function PUT(request: NextRequest) {
	try {
		// Get user session
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const { bio, location, coverPhoto } = await request.json();
		const userId = session.user.id;

		// Build update object with only provided fields
		const updateData: Record<string, string> = {};
		if (bio !== undefined) updateData.bio = bio;
		if (location !== undefined) updateData.location = location;
		if (coverPhoto !== undefined) updateData.cover_photo_url = coverPhoto;

		if (Object.keys(updateData).length === 0) {
			return NextResponse.json(
				{ error: 'No valid fields to update' },
				{ status: 400 }
			);
		}

		// Update user profile
		const profile = await db.update(
			table('nextauth_users'),
			userId,
			updateData
		);

		logger.info('Profile updated successfully', {
			userId,
			updatedFields: Object.keys(updateData),
		});

		return NextResponse.json({
			success: true,
			profile,
			message: 'Profile updated successfully',
		});
	} catch (error) {
		console.error('Update profile error:', error);
		logger.error('Profile update failed', {
			error: error instanceof Error ? error.message : 'Unknown error',
		});

		return NextResponse.json(
			{ error: 'Internal server error' },
			{ status: 500 }
		);
	}
}
