import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/nextauth-options'
import { db } from '@/lib/database'
import { logger } from '@/lib/logger'

/**
 * GET /api/profile/stats
 * Get comprehensive user statistics
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    // For now, allow users to view any profile stats (public data)
    // In production, you might want to restrict this based on privacy settings

    // Get user basic info
    const userResult = await db.query(
      'SELECT name, created_at FROM backend_schema.nextauth_users WHERE id = $1',
      [userId]
    )

    if (userResult.rows.length === 0) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const user = userResult.rows[0]

    // Get media statistics
    const mediaStatsResult = await db.query(`
      SELECT
        COUNT(*) as total_media,
        COUNT(CASE WHEN media_type = 'photo' THEN 1 END) as total_images,
        COUNT(CASE WHEN media_type = 'video' THEN 1 END) as total_videos,
        COALESCE(SUM(like_count), 0) as likes_received
      FROM spatial_schema.poi_media
      WHERE user_id = $1
    `, [userId])

    // Get favorites received (count of POIs that users have favorited that belong to this user)
    const favoritesReceivedResult = await db.query(`
      SELECT COUNT(*) as favorites_received
      FROM spatial_schema.poi_media_favorites pmf
      JOIN spatial_schema.poi_media pm ON pmf.media_id = pm.id
      WHERE pm.user_id = $1
    `, [userId])

    const mediaStats = mediaStatsResult.rows[0] || {}

    // Get user interaction statistics (likes given, favorites given, etc.)
    const likesGivenResult = await db.query(`
      SELECT COUNT(*) as total_likes
      FROM backend_schema.user_interactions
      WHERE user_id = $1 AND interaction_type = 'like'
    `, [userId])

    const favoritesGivenResult = await db.query(`
      SELECT
        (SELECT COUNT(*) FROM backend_schema.user_favorites WHERE user_id = $1) +
        (SELECT COUNT(*) FROM backend_schema.user_interactions WHERE user_id = $1 AND interaction_type = 'favorite')
        as total_favorites
    `, [userId])

    const visitsResult = await db.query(
      'SELECT COUNT(*) as total_visits FROM backend_schema.user_location_visits WHERE user_id = $1',
      [userId]
    )

    const reviewsResult = await db.query(
      'SELECT COUNT(*) as total_reviews FROM backend_schema.user_location_reviews WHERE user_id = $1',
      [userId]
    )

    // Get POI statistics (user-submitted POIs)
    const poisAddedResult = await db.query(`
      SELECT
        (SELECT COUNT(*) FROM spatial_schema.user_pois_temp WHERE submitted_by_user_id = $1) +
        (SELECT COUNT(*) FROM spatial_schema.user_pois_approved WHERE submitted_by_user_id = $1) as total_pois_added
    `, [userId])

    // Get likes received from POI interactions (not just media)
    const poiLikesReceivedResult = await db.query(`
      SELECT COUNT(*) as poi_likes_received
      FROM backend_schema.user_interactions ui
      WHERE ui.interaction_type = 'like'
      AND (
        (ui.poi_type = 'user_temp' AND ui.user_poi_temp_id IN (
          SELECT id FROM spatial_schema.user_pois_temp WHERE submitted_by_user_id = $1
        )) OR
        (ui.poi_type = 'user_approved' AND ui.user_poi_approved_id IN (
          SELECT id FROM spatial_schema.user_pois_approved WHERE submitted_by_user_id = $1
        ))
      )
    `, [userId])

    // Calculate adventurer level based on activity
    const totalActivity = 
      parseInt(mediaStats.total_media || '0') +
      parseInt(likesGivenResult.rows[0]?.total_likes || '0') +
      parseInt(visitsResult.rows[0]?.total_visits || '0') +
      parseInt(reviewsResult.rows[0]?.total_reviews || '0') +
      parseInt(poisAddedResult.rows[0]?.total_pois_added || '0')

    const adventurerLevel = Math.floor(totalActivity / 10) + 1 // Level up every 10 activities

    // Calculate streak (simplified - days since last activity)
    const lastActivityResult = await db.query(`
      SELECT MAX(created_at) as last_activity
      FROM (
        SELECT created_at FROM spatial_schema.poi_media WHERE user_id = $1
        UNION ALL
        SELECT created_at FROM backend_schema.user_location_visits WHERE user_id = $1
        UNION ALL
        SELECT created_at FROM backend_schema.user_location_reviews WHERE user_id = $1
        UNION ALL
        SELECT created_at FROM spatial_schema.poi_media_likes WHERE user_id = $1
        UNION ALL
        SELECT created_at FROM spatial_schema.poi_media_favorites WHERE user_id = $1
      ) activities
    `, [userId])

    const lastActivity = lastActivityResult.rows[0]?.last_activity
    const streakDays = lastActivity
      ? Math.max(0, 7 - Math.floor((Date.now() - new Date(lastActivity).getTime()) / (1000 * 60 * 60 * 24)))
      : 0

    const favoritesReceived = favoritesReceivedResult.rows[0]?.favorites_received || '0'

    // Compile comprehensive statistics
    const stats = {
      // Media stats
      totalMedia: parseInt(mediaStats.total_media || '0'),
      totalImages: parseInt(mediaStats.total_images || '0'),
      totalVideos: parseInt(mediaStats.total_videos || '0'),
      totalViews: 0, // Not tracked in current schema

      // Interaction stats (given)
      totalLikes: parseInt(likesGivenResult.rows[0]?.total_likes || '0'),
      totalFavorites: parseInt(favoritesGivenResult.rows[0]?.total_favorites || '0'),
      totalVisits: parseInt(visitsResult.rows[0]?.total_visits || '0'),
      totalReviews: parseInt(reviewsResult.rows[0]?.total_reviews || '0'),

      // Interaction stats (received)
      likesReceived: parseInt(mediaStats.likes_received || '0') + parseInt(poiLikesReceivedResult.rows[0]?.poi_likes_received || '0'),
      favoritesReceived: parseInt(favoritesReceived),

      // Activity stats
      totalPOIsAdded: parseInt(poisAddedResult.rows[0]?.total_pois_added || '0'),

      // Achievement stats
      adventurerLevel,
      creditsEarned: totalActivity * 10, // 10 credits per activity
      badgesEarned: Math.floor(adventurerLevel / 5), // Badge every 5 levels
      streakDays,

      // Social stats (placeholder - implement when social features are added)
      followersCount: 0,
      followingCount: 0,

      // Meta info
      joinedDate: user.created_at,
      totalActivity
    }

    logger.info('Profile statistics retrieved', {
      userId,
      totalActivity,
      adventurerLevel,
      rawData: {
        mediaStats: mediaStats,
        likesGiven: likesGivenResult.rows[0],
        favoritesGiven: favoritesGivenResult.rows[0],
        visits: visitsResult.rows[0],
        reviews: reviewsResult.rows[0],
        poisAdded: poisAddedResult.rows[0],
        poiLikesReceived: poiLikesReceivedResult.rows[0]
      }
    })

    return NextResponse.json({
      success: true,
      stats,
      user: {
        name: user.name,
        joinedDate: user.created_at
      }
    })

  } catch (error) {
    logger.error('Error retrieving profile statistics', {
      error: error instanceof Error ? error.message : 'Unknown error'
    })

    return NextResponse.json(
      { error: 'Failed to retrieve profile statistics' },
      { status: 500 }
    )
  }
}
