/**
 * Profile Picture Delete API
 * Handles deleting user profile pictures and cleaning up files
 *
 * @format
 */

import { MediaManager } from '@/app/shared/media/utils';
import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextResponse } from 'next/server';

/**
 * DELETE /api/profile/picture/delete
 * Delete user's profile picture and clean up files
 */
export async function DELETE() {
	try {
		// Check authentication
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const userId = session.user.id;

		logger.info('Starting profile picture deletion', { userId });

		// Get current profile picture URL before deletion
		const userQuery =
			'SELECT profile_picture_url FROM backend_schema.nextauth_users WHERE id = $1';
		const userResult = await db.query(userQuery, [userId]);
		const currentProfilePicture = userResult.rows[0]?.profile_picture_url;

		// Delete all profile picture files from filesystem
		const deleteResult = await MediaManager.deleteProfilePicture(userId);

		if (!deleteResult.success) {
			logger.error('Failed to delete profile picture files', {
				userId,
				error: deleteResult.error,
			});
			return NextResponse.json(
				{
					success: false,
					error: deleteResult.error || 'Failed to delete profile picture files',
				},
				{ status: 500 }
			);
		}

		// Update user profile in database to remove profile picture URL
		await db.query(
			'UPDATE backend_schema.nextauth_users SET profile_picture_url = NULL, updated_at = NOW() WHERE id = $1',
			[userId]
		);

		// Also delete from profile picture history if it exists
		try {
			await db.query(
				'DELETE FROM backend_schema.profile_picture_history WHERE user_id = $1',
				[userId]
			);
		} catch (error) {
			// History table might not exist or be empty, that's okay
			logger.warn('Could not delete from profile picture history', {
				userId,
				error,
			});
		}

		logger.info('Profile picture deleted successfully', {
			userId,
			deletedFiles: deleteResult.deletedFiles?.length || 0,
			cleanedUpDirectories: deleteResult.cleanedUpDirectories?.length || 0,
			previousUrl: currentProfilePicture,
		});

		return NextResponse.json({
			success: true,
			message: 'Profile picture deleted successfully',
			deletedFiles: deleteResult.deletedFiles?.length || 0,
			cleanedUpDirectories: deleteResult.cleanedUpDirectories?.length || 0,
			previousUrl: currentProfilePicture,
		});
	} catch (error) {
		logger.error('Error deleting profile picture', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to delete profile picture' },
			{ status: 500 }
		);
	}
}

/**
 * POST /api/profile/picture/delete
 * Alternative endpoint for frameworks that don't support DELETE method well
 */
export async function POST() {
	return DELETE();
}
