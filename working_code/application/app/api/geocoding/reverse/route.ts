import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { logger } from '@/lib/logger'

// GET /api/geocoding/reverse?lat=41.0463&lng=28.5139
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const lat = parseFloat(searchParams.get('lat') || '0')
    const lng = parseFloat(searchParams.get('lng') || '0')

    if (!lat || !lng || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      return NextResponse.json(
        { success: false, error: 'Valid latitude and longitude are required' },
        { status: 400 }
      )
    }

    logger.info('Reverse geocoding request', { lat, lng })

    // Get administrative boundaries using spatial containment
    const adminQuery = `
      SELECT name AS boundary_name, admin_level
      FROM spatial_schema.admin_boundaries
      WHERE ST_Contains(
        ST_SetSRID(geom, 4326),
        ST_SetSRID(ST_Point($1, $2), 4326)
      )
      ORDER BY admin_level ASC
    `

    // Get nearest street
    const streetQuery = `
      SELECT name
      FROM spatial_schema.roads
      WHERE name IS NOT NULL AND name != ''
      ORDER BY ST_Distance(
        geom,
        ST_SetSRID(ST_Point($1, $2), 4326)
      ) ASC
      LIMIT 1
    `

    const [adminResult, streetResult] = await Promise.all([
      db.query(adminQuery, [lng, lat]),
      db.query(streetQuery, [lng, lat])
    ])

    // Parse administrative boundaries
    let province = ''
    let city = ''
    let district = ''
    let neighborhood = ''

    for (const row of adminResult.rows) {
      if (row.admin_level === 4 && !province) {
        province = row.boundary_name
        // For Istanbul, city is the same as province
        city = row.boundary_name
      } else if (row.admin_level === 6 && !district) {
        district = row.boundary_name
      } else if (row.admin_level === 8 && !neighborhood) {
        neighborhood = row.boundary_name
      }
    }

    // Get nearest street
    const street = streetResult.rows[0]?.name || ''

    // Build full address
    const addressParts = [street, neighborhood, district, city, province].filter(Boolean)
    const full_address = addressParts.join(', ')

    const result = {
      province,
      city,
      district,
      neighborhood,
      street,
      full_address
    }

    logger.info('Reverse geocoding result', result)

    return NextResponse.json({
      success: true,
      ...result
    })

  } catch (error) {
    logger.error('Error in reverse geocoding', { error })
    return NextResponse.json(
      { success: false, error: 'Failed to perform reverse geocoding' },
      { status: 500 }
    )
  }
}
