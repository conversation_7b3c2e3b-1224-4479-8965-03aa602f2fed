/** @format */

import {
	AuthenticatedAgentRequest,
	withS<PERSON><PERSON>erAuth,
} from '@/lib/agent-middleware';
import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { NextResponse } from 'next/server';

// GET /api/admin/stats - Get admin dashboard statistics
export const GET = withSuperuserAuth()(
	async (request: AuthenticatedAgentRequest) => {
		try {
			logger.info('Admin fetching dashboard stats', {
				adminId: request.agent.id,
			});

			// Get user role counts
			const roleStatsResult = await db.query(`
      SELECT 
        role,
        COUNT(*) as count
      FROM backend_schema.nextauth_users 
      GROUP BY role
    `);

			// Get new users this week
			const newUsersResult = await db.query(`
      SELECT COUNT(*) as count
      FROM backend_schema.nextauth_users 
      WHERE created_at >= NOW() - INTERVAL '7 days'
    `);

			// Get total users
			const totalUsersResult = await db.query(`
      SELECT COUNT(*) as count
      FROM backend_schema.nextauth_users
    `);

			// Process role stats
			const roleStats = roleStatsResult.rows.reduce((acc, row) => {
				acc[`total_${row.role}s`] = parseInt(row.count);
				return acc;
			}, {});

			const stats = {
				total_users: parseInt(totalUsersResult.rows[0].count),
				total_agents: roleStats.total_agents || 0,
				total_superusers: roleStats.total_superusers || 0,
				new_users_this_week: parseInt(newUsersResult.rows[0].count),
				...roleStats,
			};

			logger.info('Admin stats fetched successfully', {
				adminId: request.agent.id,
				stats,
			});

			return NextResponse.json({
				success: true,
				stats,
			});
		} catch (error) {
			logger.error('Error fetching admin stats', { error });
			return NextResponse.json(
				{ success: false, error: 'Failed to fetch statistics' },
				{ status: 500 }
			);
		}
	}
);
