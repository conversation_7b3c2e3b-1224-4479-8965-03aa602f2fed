/** @format */

import {
	AuthenticatedAgentRequest,
	withS<PERSON>ruserAuth,
} from '@/lib/agent-middleware';
import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import bcrypt from 'bcryptjs';
import { NextResponse } from 'next/server';

// GET /api/admin/users - Get all users with search and filtering
export const GET = withSuperuserAuth()(
	async (request: AuthenticatedAgentRequest) => {
		try {
			const { searchParams } = new URL(request.url);
			const search = searchParams.get('search') || '';
			const role = searchParams.get('role') || 'all';
			const page = parseInt(searchParams.get('page') || '1');
			const limit = parseInt(searchParams.get('limit') || '20');
			const offset = (page - 1) * limit;

			logger.info('Admin fetching users', {
				adminId: request.agent.id,
				search,
				role,
				page,
				limit,
			});

			// Build query with filters
			let whereClause = 'WHERE 1=1';
			const queryParams: (string | number)[] = [];
			let paramIndex = 1;

			// Search filter
			if (search) {
				whereClause += ` AND (
        email ILIKE $${paramIndex} OR 
        name ILIKE $${paramIndex} OR 
        username ILIKE $${paramIndex} OR 
        id::text ILIKE $${paramIndex}
      )`;
				queryParams.push(`%${search}%`);
				paramIndex++;
			}

			// Role filter
			if (role !== 'all' && ['user', 'agent', 'superuser'].includes(role)) {
				whereClause += ` AND role = $${paramIndex}`;
				queryParams.push(role);
				paramIndex++;
			}

			const query = `
      SELECT 
        id,
        email,
        name,
        username,
        role,
        permissions,
        created_at
      FROM backend_schema.nextauth_users u
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

			queryParams.push(limit, offset);

			const result = await db.query(query, queryParams);

			// Get total count
			const countQuery = `
      SELECT COUNT(*) as total
      FROM backend_schema.nextauth_users
      ${whereClause}
    `;

			const countParams = queryParams.slice(0, -2); // Remove limit and offset
			const countResult = await db.query(countQuery, countParams);
			const total = parseInt(countResult.rows[0].total);

			logger.info('Admin users fetched successfully', {
				adminId: request.agent.id,
				count: result.rows.length,
				total,
				page,
				limit,
			});

			return NextResponse.json({
				success: true,
				users: result.rows,
				pagination: {
					page,
					limit,
					total,
					totalPages: Math.ceil(total / limit),
				},
			});
		} catch (error) {
			logger.error('Error fetching users for admin', { error });
			return NextResponse.json(
				{ success: false, error: 'Failed to fetch users' },
				{ status: 500 }
			);
		}
	}
);

// POST /api/admin/users - Create new user (admin only)
export const POST = withSuperuserAuth()(
	async (request: AuthenticatedAgentRequest) => {
		try {
			const body = await request.json();
			const { email, name, username, password, role = 'user' } = body;

			// Validation
			if (!email || !name || !username || !password) {
				return NextResponse.json(
					{
						success: false,
						error: 'Email, name, username, and password are required',
					},
					{ status: 400 }
				);
			}

			const validRoles = ['user', 'agent', 'superuser'];
			if (!validRoles.includes(role)) {
				return NextResponse.json(
					{ success: false, error: 'Invalid role' },
					{ status: 400 }
				);
			}

			logger.info('Admin creating new user', {
				adminId: request.agent.id,
				email,
				username,
				role,
			});

			// Check if user already exists
			const existingUser = await db.query(
				'SELECT id FROM backend_schema.nextauth_users WHERE email = $1 OR username = $2',
				[email, username]
			);

			if (existingUser.rows.length > 0) {
				return NextResponse.json(
					{
						success: false,
						error: 'User with this email or username already exists',
					},
					{ status: 400 }
				);
			}

			// Hash password
			const passwordHash = await bcrypt.hash(password, 12);

			// Create user
			const userResult = await db.query(
				`
      INSERT INTO backend_schema.nextauth_users (email, name, username, role)
      VALUES ($1, $2, $3, $4)
      RETURNING id, email, name, username, role, created_at
    `,
				[email, name, username.toLowerCase(), role]
			);

			const newUser = userResult.rows[0];

			// Create user profile
			await db.query(
				`
      INSERT INTO backend_schema.user_profiles (user_id, username, name, password_hash, profile_completed)
      VALUES ($1, $2, $3, $4, $5)
    `,
				[newUser.id, username.toLowerCase(), name, passwordHash, true]
			);

			// Create default location settings
			await db.query(
				`
      INSERT INTO backend_schema.user_location_settings (user_id, search_radius, num_candidates)
      VALUES ($1, $2, $3)
    `,
				[newUser.id, 5000, 3]
			);

			logger.info('User created successfully by admin', {
				adminId: request.agent.id,
				newUserId: newUser.id,
				email,
				role,
			});

			return NextResponse.json({
				success: true,
				message: 'User created successfully',
				user: {
					id: newUser.id,
					email: newUser.email,
					name: newUser.name,
					username: newUser.username,
					role: newUser.role,
					created_at: newUser.created_at,
				},
			});
		} catch (error) {
			logger.error('Error creating user', { error });
			return NextResponse.json(
				{ success: false, error: 'Failed to create user' },
				{ status: 500 }
			);
		}
	}
);

// PUT /api/admin/users - Update user role or details
export const PUT = withSuperuserAuth()(
	async (request: AuthenticatedAgentRequest) => {
		try {
			const body = await request.json();
			const { user_id, role, name, email } = body;

			// Validation
			if (!user_id) {
				return NextResponse.json(
					{ success: false, error: 'User ID is required' },
					{ status: 400 }
				);
			}

			if (role) {
				const validRoles = ['user', 'agent', 'superuser'];
				if (!validRoles.includes(role)) {
					return NextResponse.json(
						{ success: false, error: 'Invalid role' },
						{ status: 400 }
					);
				}
			}

			logger.info('Admin updating user', {
				adminId: request.agent.id,
				userId: user_id,
				updates: { role, name, email },
			});

			// Check if user exists
			const existingResult = await db.query(
				'SELECT id, role FROM backend_schema.nextauth_users WHERE id = $1',
				[user_id]
			);

			if (existingResult.rows.length === 0) {
				return NextResponse.json(
					{ success: false, error: 'User not found' },
					{ status: 404 }
				);
			}

			// Build update query
			let updateQuery = 'UPDATE backend_schema.nextauth_users SET ';
			const updateParams: (string | number)[] = [];
			const updateFields: string[] = [];
			let paramIndex = 1;

			if (role) {
				updateFields.push(`role = $${paramIndex}`);
				updateParams.push(role);
				paramIndex++;
			}

			if (name) {
				updateFields.push(`name = $${paramIndex}`);
				updateParams.push(name);
				paramIndex++;
			}

			if (email) {
				updateFields.push(`email = $${paramIndex}`);
				updateParams.push(email);
				paramIndex++;
			}

			if (updateFields.length === 0) {
				return NextResponse.json(
					{ success: false, error: 'No updates provided' },
					{ status: 400 }
				);
			}

			updateQuery += updateFields.join(', ') + ` WHERE id = $${paramIndex}`;
			updateParams.push(user_id);

			await db.query(updateQuery, updateParams);

			logger.info('User updated successfully by admin', {
				adminId: request.agent.id,
				userId: user_id,
				updates: { role, name, email },
			});

			return NextResponse.json({
				success: true,
				message: 'User updated successfully',
			});
		} catch (error) {
			logger.error('Error updating user', { error });
			return NextResponse.json(
				{ success: false, error: 'Failed to update user' },
				{ status: 500 }
			);
		}
	}
);
