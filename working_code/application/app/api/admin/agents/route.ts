/** @format */

import {
	AuthenticatedAgentRequest,
	logAgentActivity,
	promoteUserToAgent,
	withAdminAuth,
} from '@/lib/agent-middleware';
import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { NextResponse } from 'next/server';

// GET /api/admin/agents - Get all agent users
export const GET = withAdminAuth()(
	async (request: AuthenticatedAgentRequest) => {
		try {
			const { searchParams } = new URL(request.url);
			const role = searchParams.get('role') || 'all';
			const page = parseInt(searchParams.get('page') || '1');
			const limit = parseInt(searchParams.get('limit') || '20');
			const offset = (page - 1) * limit;

			logger.info('Admin fetching agent users', {
				adminId: request.agent.id,
				role,
				page,
				limit,
			});

			// Build query with filters
			let whereClause = "WHERE role = 'agent'";
			const queryParams: (string | number)[] = [];
			let paramIndex = 1;

			if (role !== 'all' && ['agent'].includes(role)) {
				whereClause = `WHERE role = $${paramIndex}`;
				queryParams.push(role);
				paramIndex++;
			}

			const query = `
      SELECT 
        id,
        email,
        name,
        role,
        permissions,
        created_at
      FROM backend_schema.nextauth_users u
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

			queryParams.push(limit, offset);

			const result = await db.query(query, queryParams);

			// Get total count
			const countQuery = `
      SELECT COUNT(*) as total
      FROM backend_schema.nextauth_users
      ${whereClause}
    `;

			const countParams = whereClause.includes('$1') ? [queryParams[0]] : [];
			const countResult = await db.query(countQuery, countParams);
			const totalCount = parseInt(countResult.rows[0].total);

			logger.info(`Retrieved ${result.rows.length} agent users`);

			return NextResponse.json({
				success: true,
				agents: result.rows,
				pagination: {
					page,
					limit,
					total: totalCount,
					totalPages: Math.ceil(totalCount / limit),
					hasNext: offset + limit < totalCount,
					hasPrev: page > 1,
				},
			});
		} catch (error) {
			logger.error('Error fetching agent users', { error });
			return NextResponse.json(
				{ success: false, error: 'Failed to fetch agent users' },
				{ status: 500 }
			);
		}
	}
);

// POST /api/admin/agents - Promote user to agent or create new agent
export const POST = withAdminAuth()(
	async (request: AuthenticatedAgentRequest) => {
		try {
			const body = await request.json();
			const { user_id, email } = body;

			// Validation
			if (!user_id && !email) {
				return NextResponse.json(
					{ success: false, error: 'User ID or email is required' },
					{ status: 400 }
				);
			}

			// Find user by ID or email
			let targetUserId = user_id;
			if (!targetUserId && email) {
				const userResult = await db.query(
					'SELECT id FROM backend_schema.nextauth_users WHERE email = $1',
					[email]
				);

				if (userResult.rows.length === 0) {
					return NextResponse.json(
						{ success: false, error: 'User not found' },
						{ status: 404 }
					);
				}

				targetUserId = userResult.rows[0].id;
			}

			// Check if user already has agent role
			const existingResult = await db.query(
				'SELECT role FROM backend_schema.nextauth_users WHERE id = $1',
				[targetUserId]
			);

			if (existingResult.rows.length === 0) {
				return NextResponse.json(
					{ success: false, error: 'User not found' },
					{ status: 404 }
				);
			}

			const currentRole = existingResult.rows[0].role;
			if (currentRole === 'agent') {
				return NextResponse.json(
					{ success: false, error: `User is already ${currentRole}` },
					{ status: 400 }
				);
			}

			// Promote user to agent (role-aware, only full_access allowed)
			const success = await promoteUserToAgent(targetUserId, request.agent.id);

			if (!success) {
				return NextResponse.json(
					{ success: false, error: 'Failed to promote user to agent' },
					{ status: 500 }
				);
			}

			logger.info('User promoted to agent successfully', {
				targetUserId,
				adminId: request.agent.id,
			});

			return NextResponse.json({
				success: true,
				message: 'User promoted to agent successfully',
				agent: {
					id: targetUserId,
					role: 'agent',
					permissions: ['full_access'],
					promoted_by: request.agent.id,
					promoted_at: new Date().toISOString(),
				},
			});
		} catch (error) {
			logger.error('Error promoting user to agent', { error });
			return NextResponse.json(
				{ success: false, error: 'Failed to promote user to agent' },
				{ status: 500 }
			);
		}
	}
);

// PUT /api/admin/agents - Update agent permissions
export const PUT = withAdminAuth()(
	async (request: AuthenticatedAgentRequest) => {
		try {
			const body = await request.json();
			const { agent_id, permissions, agent_level } = body;

			// Validation
			if (!agent_id) {
				return NextResponse.json(
					{ success: false, error: 'Agent ID is required' },
					{ status: 400 }
				);
			}

			const validPermissions = [
				'poi.review',
				'poi.edit',
				'poi.delete',
				'poi.bulk_actions',
				'user.view',
				'user.edit',
				'user.suspend',
				'review.moderate',
				'review.delete',
				'analytics.view',
				'system.admin',
			];

			if (permissions) {
				const invalidPermissions = permissions.filter(
					(p: string) => !validPermissions.includes(p)
				);

				if (invalidPermissions.length > 0) {
					return NextResponse.json(
						{
							success: false,
							error: `Invalid permissions: ${invalidPermissions.join(', ')}`,
						},
						{ status: 400 }
					);
				}
			}

			if (agent_level) {
				const validLevels = ['junior', 'senior', 'lead', 'admin'];
				if (!validLevels.includes(agent_level)) {
					return NextResponse.json(
						{ success: false, error: 'Invalid agent level' },
						{ status: 400 }
					);
				}
			}

			logger.info('Admin updating agent permissions', {
				adminId: request.agent.id,
				agentId: agent_id,
				permissions,
				agentLevel: agent_level,
			});

			// Check if target user is agent
			const agentResult = await db.query(
				'SELECT role FROM backend_schema.nextauth_users WHERE id = $1',
				[agent_id]
			);

			if (agentResult.rows.length === 0) {
				return NextResponse.json(
					{ success: false, error: 'Agent not found' },
					{ status: 404 }
				);
			}

			const currentRole = agentResult.rows[0].role;
			if (currentRole !== 'agent' && currentRole !== 'admin') {
				return NextResponse.json(
					{ success: false, error: 'User is not an agent' },
					{ status: 400 }
				);
			}

			// Update permissions and/or level
			let updateQuery = 'UPDATE backend_schema.nextauth_users SET ';
			const updateParams: (string | number)[] = [];
			const updateFields: string[] = [];
			let paramIndex = 1;

			if (permissions) {
				updateFields.push(`permissions = $${paramIndex}`);
				updateParams.push(JSON.stringify(permissions));
				paramIndex++;
			}

			if (agent_level) {
				updateFields.push(`agent_level = $${paramIndex}`);
				updateParams.push(agent_level);
				paramIndex++;
			}

			if (updateFields.length === 0) {
				return NextResponse.json(
					{ success: false, error: 'No updates provided' },
					{ status: 400 }
				);
			}

			updateQuery += updateFields.join(', ') + ` WHERE id = $${paramIndex}`;
			updateParams.push(agent_id);

			await db.query(updateQuery, updateParams);

			// Log the activity
			await logAgentActivity(
				request.agent.id,
				'agent_permissions_updated',
				'user',
				agent_id,
				{ permissions, agent_level },
				request
			);

			logger.info('Agent permissions updated successfully', {
				agentId: agent_id,
				adminId: request.agent.id,
			});

			return NextResponse.json({
				success: true,
				message: 'Agent permissions updated successfully',
				agent: {
					id: agent_id,
					permissions,
					level: agent_level,
					updated_by: request.agent.id,
					updated_at: new Date().toISOString(),
				},
			});
		} catch (error) {
			logger.error('Error updating agent permissions', { error });
			return NextResponse.json(
				{ success: false, error: 'Failed to update agent permissions' },
				{ status: 500 }
			);
		}
	}
);
