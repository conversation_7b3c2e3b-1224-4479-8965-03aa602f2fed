/** @format */

import {
	AuthenticatedAgentRequest,
	promoteUserToSuperuser,
	withSuperuserAuth,
} from '@/lib/agent-middleware';
import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { NextResponse } from 'next/server';

// GET /api/admin/superusers - Get all superuser users
export const GET = withSuperuserAuth()(
	async (request: AuthenticatedAgentRequest) => {
		try {
			const { searchParams } = new URL(request.url);
			const page = parseInt(searchParams.get('page') || '1');
			const limit = parseInt(searchParams.get('limit') || '20');
			const offset = (page - 1) * limit;

			logger.info('Admin fetching superuser users', {
				adminId: request.agent.id,
				page,
				limit,
			});

			const query = `
      SELECT 
        id,
        email,
        name,
        role,
        permissions,
        created_at
      FROM backend_schema.nextauth_users u
      WHERE role = 'superuser'
      ORDER BY created_at DESC
      LIMIT $1 OFFSET $2
    `;

			const result = await db.query(query, [limit, offset]);

			// Get total count
			const countResult = await db.query(`
      SELECT COUNT(*) as total
      FROM backend_schema.nextauth_users
      WHERE role = 'superuser'
    `);

			const total = parseInt(countResult.rows[0].total);

			logger.info('Superuser users fetched successfully', {
				adminId: request.agent.id,
				count: result.rows.length,
				total,
			});

			return NextResponse.json({
				success: true,
				superusers: result.rows,
				pagination: {
					page,
					limit,
					total,
					totalPages: Math.ceil(total / limit),
				},
			});
		} catch (error) {
			logger.error('Error fetching superuser users', { error });
			return NextResponse.json(
				{ success: false, error: 'Failed to fetch superuser users' },
				{ status: 500 }
			);
		}
	}
);

// POST /api/admin/superusers - Promote user to superuser
export const POST = withSuperuserAuth()(
	async (request: AuthenticatedAgentRequest) => {
		try {
			const body = await request.json();
			const { user_id, permissions, email } = body;

			// Validation
			if (!user_id && !email) {
				return NextResponse.json(
					{ success: false, error: 'User ID or email is required' },
					{ status: 400 }
				);
			}

			// Find user by ID or email
			let targetUserId = user_id;
			if (!targetUserId && email) {
				const userResult = await db.query(
					'SELECT id FROM backend_schema.nextauth_users WHERE email = $1',
					[email]
				);

				if (userResult.rows.length === 0) {
					return NextResponse.json(
						{ success: false, error: 'User not found' },
						{ status: 404 }
					);
				}

				targetUserId = userResult.rows[0].id;
			}

			// Check if user exists and get current role
			const existingResult = await db.query(
				'SELECT role FROM backend_schema.nextauth_users WHERE id = $1',
				[targetUserId]
			);

			if (existingResult.rows.length === 0) {
				return NextResponse.json(
					{ success: false, error: 'User not found' },
					{ status: 404 }
				);
			}

			const currentRole = existingResult.rows[0].role;
			if (currentRole === 'superuser') {
				return NextResponse.json(
					{ success: false, error: 'User is already a superuser' },
					{ status: 400 }
				);
			}

			// Promote user to superuser
			const success = await promoteUserToSuperuser(
				targetUserId,
				request.agent.id,
				permissions
			);

			if (!success) {
				return NextResponse.json(
					{ success: false, error: 'Failed to promote user to superuser' },
					{ status: 500 }
				);
			}

			logger.info('User promoted to superuser successfully', {
				targetUserId,
				adminId: request.agent.id,
				permissions,
			});

			return NextResponse.json({
				success: true,
				message: 'User promoted to superuser successfully',
				superuser: {
					id: targetUserId,
					role: 'superuser',
					permissions,
					promoted_by: request.agent.id,
					promoted_at: new Date().toISOString(),
				},
			});
		} catch (error) {
			logger.error('Error promoting user to superuser', { error });
			return NextResponse.json(
				{ success: false, error: 'Failed to promote user to superuser' },
				{ status: 500 }
			);
		}
	}
);

// PUT /api/admin/superusers - Update superuser permissions
export const PUT = withSuperuserAuth()(
	async (request: AuthenticatedAgentRequest) => {
		try {
			const body = await request.json();
			const { superuser_id, permissions } = body;

			// Validation
			if (!superuser_id) {
				return NextResponse.json(
					{ success: false, error: 'Superuser ID is required' },
					{ status: 400 }
				);
			}

			const validPermissions = [
				'admin.full_access',
				'agent.manage',
				'user.manage',
				'system.configure',
				'audit.view',
			];

			if (permissions) {
				const invalidPermissions = permissions.filter(
					(p: string) => !validPermissions.includes(p)
				);

				if (invalidPermissions.length > 0) {
					return NextResponse.json(
						{
							success: false,
							error: `Invalid permissions: ${invalidPermissions.join(', ')}`,
						},
						{ status: 400 }
					);
				}
			}

			logger.info('Admin updating superuser permissions', {
				adminId: request.agent.id,
				superuserId: superuser_id,
				permissions,
			});

			// Check if superuser exists
			const existingResult = await db.query(
				'SELECT role FROM backend_schema.nextauth_users WHERE id = $1',
				[superuser_id]
			);

			if (existingResult.rows.length === 0) {
				return NextResponse.json(
					{ success: false, error: 'Superuser not found' },
					{ status: 404 }
				);
			}

			if (existingResult.rows[0].role !== 'superuser') {
				return NextResponse.json(
					{ success: false, error: 'User is not a superuser' },
					{ status: 400 }
				);
			}

			// Update permissions
			await db.query(
				`
      UPDATE backend_schema.nextauth_users 
      SET permissions = $1 
      WHERE id = $2 AND role = 'superuser'
    `,
				[JSON.stringify(permissions), superuser_id]
			);

			logger.info('Superuser permissions updated successfully', {
				adminId: request.agent.id,
				superuserId: superuser_id,
				newPermissions: permissions,
			});

			return NextResponse.json({
				success: true,
				message: 'Superuser permissions updated successfully',
			});
		} catch (error) {
			logger.error('Error updating superuser permissions', { error });
			return NextResponse.json(
				{ success: false, error: 'Failed to update superuser permissions' },
				{ status: 500 }
			);
		}
	}
);
