/** @format */

import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

// GET /api/user/location-settings - Get user's location settings
export async function GET() {
	try {
		// Get user session
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const userId = session.user.id;

		logger.info('Fetching user location settings', { userId });

		const settings = await db.getOne(
			'SELECT * FROM backend_schema.user_location_settings WHERE user_id = $1',
			[userId]
		);

		// If no settings exist, create default ones
		if (!settings) {
			const newSettings = await db.insert(
				'backend_schema.user_location_settings',
				{
					user_id: userId,
					search_radius: 5000,
					num_candidates: 3,
				}
			);

			return NextResponse.json({
				success: true,
				settings: newSettings,
			});
		}

		return NextResponse.json({
			success: true,
			settings,
		});
	} catch (error) {
		logger.error('Error fetching user location settings', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to fetch location settings' },
			{ status: 500 }
		);
	}
}

// POST /api/user/location-settings - Update user's location settings
export async function POST(request: NextRequest) {
	try {
		// Get user session
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const body = await request.json();
		const { searchRadius, numCandidates } = body;

		const userId = session.user.id;

		// Validate search radius
		if (searchRadius && (searchRadius < 100 || searchRadius > 10000)) {
			return NextResponse.json(
				{
					success: false,
					error: 'Search radius must be between 100 and 10000 meters',
				},
				{ status: 400 }
			);
		}

		// Validate number of candidates
		if (numCandidates && (numCandidates < 1 || numCandidates > 20)) {
			return NextResponse.json(
				{
					success: false,
					error: 'Number of candidates must be between 1 and 20',
				},
				{ status: 400 }
			);
		}

		logger.info('Updating user search preferences', {
			userId,
			searchRadius,
			numCandidates,
		});

		// Check if settings exist
		const existingSettings = await db.getOne(
			'SELECT id FROM backend_schema.user_location_settings WHERE user_id = $1',
			[userId]
		);

		let settings;
		if (existingSettings) {
			// Update existing search preferences only
			settings = await db.update(
				'backend_schema.user_location_settings',
				existingSettings.id,
				{
					search_radius: searchRadius,
					num_candidates: numCandidates,
				}
			);
		} else {
			// Create new settings (should not happen as they're created during signup)
			settings = await db.insert('backend_schema.user_location_settings', {
				user_id: userId,
				search_radius: searchRadius || 5000,
				num_candidates: numCandidates || 3,
			});
		}

		logger.info('User search preferences updated successfully', {
			userId,
			settingsId: settings.id,
		});

		return NextResponse.json({
			success: true,
			settings,
		});
	} catch (error) {
		logger.error('Error updating user search preferences', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to update search preferences' },
			{ status: 500 }
		);
	}
}

// Note: PUT endpoint removed - we no longer sync location data to database
// Coordinates and location choices are handled client-side only for privacy and security
// This API only handles search preferences (radius, candidates) that are stored in database
