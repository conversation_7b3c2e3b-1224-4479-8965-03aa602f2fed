/**
 * User Media Management API
 * Handles retrieving and managing user's media files
 *
 * @format
 */

import { MediaDatabaseService, MediaManager } from '@/app/shared/media/utils';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

// GET /api/media/user - Get user's media files
export async function GET(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const userId = session.user.id;
		const { searchParams } = new URL(request.url);

		// Parse query parameters
		const mediaType = searchParams.get('mediaType'); // 'photo', 'video', or null for all
		const poiId = searchParams.get('poiId'); // Filter by specific POI
		const limit = parseInt(searchParams.get('limit') || '20');
		const offset = parseInt(searchParams.get('offset') || '0');
		const sortBy = searchParams.get('sortBy') || 'created_at'; // 'created_at', 'like_count'
		const sortOrder = searchParams.get('sortOrder') || 'DESC'; // 'ASC' or 'DESC'

		// Use MediaDatabaseService to get user's media
		const result = await MediaDatabaseService.getMedia({
			userId,
			mediaType: mediaType as 'photo' | 'video' | undefined,
			poiId: poiId || undefined,
			limit,
			offset,
			sortBy: sortBy as 'created_at' | 'like_count' | 'favorite_count',
			sortOrder: sortOrder.toUpperCase() as 'ASC' | 'DESC',
		});

		logger.info('Retrieved user media', {
			userId,
			count: result.media.length,
			total: result.total,
			filters: { mediaType, poiId, limit, offset },
		});

		return NextResponse.json({
			success: true,
			media: result.media,
			pagination: {
				total: result.total,
				limit,
				offset,
				hasMore: offset + limit < result.total,
			},
			filters: {
				mediaType,
				poiId,
				sortBy,
				sortOrder,
			},
		});
	} catch (error) {
		logger.error('Error retrieving user media', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to retrieve media' },
			{ status: 500 }
		);
	}
}

// DELETE /api/media/user - Delete user's media
export async function DELETE(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const userId = session.user.id;
		const { searchParams } = new URL(request.url);
		const mediaId = searchParams.get('mediaId');

		if (!mediaId) {
			return NextResponse.json(
				{ success: false, error: 'Media ID is required' },
				{ status: 400 }
			);
		}

		// Get media record to verify ownership and get file paths
		const media = await MediaDatabaseService.getMediaById(mediaId, userId);

		if (!media) {
			return NextResponse.json(
				{ success: false, error: 'Media not found or access denied' },
				{ status: 404 }
			);
		}

		// Delete media files from filesystem
		if (media.media_url) {
			await MediaManager.deleteMedia(media.media_url);
		}

		// Delete thumbnail if exists
		if (media.thumbnail_url) {
			await MediaManager.deleteMedia(media.thumbnail_url);
		}

		// Delete database record
		const deleted = await MediaDatabaseService.deleteMedia(mediaId, userId);

		if (!deleted) {
			return NextResponse.json(
				{ success: false, error: 'Failed to delete media record' },
				{ status: 500 }
			);
		}

		logger.info('User media deleted successfully', {
			userId,
			mediaId,
			mediaUrl: media.media_url,
		});

		return NextResponse.json({
			success: true,
			message: 'Media deleted successfully',
			deletedMedia: {
				id: media.id,
				mediaUrl: media.media_url,
				poiId: media.poi_id,
			},
		});
	} catch (error) {
		logger.error('Error deleting user media', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to delete media' },
			{ status: 500 }
		);
	}
}

// PUT /api/media/user - Update media metadata (caption, etc.)
export async function PUT(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const userId = session.user.id;
		const body = await request.json();
		const { mediaId, caption } = body;

		if (!mediaId) {
			return NextResponse.json(
				{ success: false, error: 'Media ID is required' },
				{ status: 400 }
			);
		}

		// Update media using MediaDatabaseService
		const updatedMedia = await MediaDatabaseService.updateMedia(
			mediaId,
			userId,
			{
				caption: caption || '',
			}
		);

		if (!updatedMedia) {
			return NextResponse.json(
				{ success: false, error: 'Media not found or access denied' },
				{ status: 404 }
			);
		}

		logger.info('User media updated successfully', {
			userId,
			mediaId,
			caption,
		});

		return NextResponse.json({
			success: true,
			message: 'Media updated successfully',
			media: updatedMedia,
		});
	} catch (error) {
		logger.error('Error updating user media', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to update media' },
			{ status: 500 }
		);
	}
}
