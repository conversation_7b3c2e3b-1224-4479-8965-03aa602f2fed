/**
 * Unified Media Upload API
 * Handles both profile pictures and POI media uploads
 *
 * @format
 */

import { MediaDatabaseService, MediaManager } from '@/app/shared/media/utils';
import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

/**
 * Server-side POI validation function
 * Directly queries the database instead of making HTTP requests
 */
async function validatePOIForUpload(
	poiId: string,
	poiType: 'official' | 'user_temp' | 'user_approved' = 'official'
): Promise<{
	isValid: boolean;
	poi?: Record<string, unknown>;
	error?: string;
}> {
	try {
		let query = '';
		let params: (string | number)[] = [];

		if (poiType === 'official') {
			query = `
        SELECT
          'official' as poi_type,
          id as poi_id,
          null as temp_id,
          null as approved_id,
          name,
          profile_picture_url,
          name_en,
          name_tr,
          name_uk,
          name_de,
          name_ru,
          name_ar,
          category,
          subcategory,
          cuisine,
          city,
          district,
          neighborhood,
          street,
          full_address,
          province,
          latitude,
          longitude,
          phone_number,
          opening_hours,
          description
        FROM spatial_schema.pois
        WHERE id = $1
      `;
			params = [poiId];
		} else if (poiType === 'user_approved') {
			query = `
        SELECT
          'user_approved' as poi_type,
          id as poi_id,
          original_temp_id as temp_id,
          null as approved_id,
          name,
          null as profile_picture_url,
          name_en,
          name_tr,
          name_uk,
          name_de,
          name_ru,
          name_ar,
          category,
          subcategory,
          cuisine,
          city,
          district,
          neighborhood,
          street,
          full_address,
          province,
          latitude,
          longitude,
          phone_number,
          opening_hours,
          description
        FROM spatial_schema.user_pois_approved
        WHERE id = $1
      `;
			params = [poiId];
		} else if (poiType === 'user_temp') {
			query = `
        SELECT
          'user_temp' as poi_type,
          null as poi_id,
          id as temp_id,
          null as approved_id,
          name,
          null as profile_picture_url,
          name_en,
          name_tr,
          name_uk,
          name_de,
          name_ru,
          name_ar,
          category,
          subcategory,
          cuisine,
          city,
          district,
          neighborhood,
          street,
          full_address,
          province,
          latitude,
          longitude,
          phone_number,
          opening_hours,
          description
        FROM spatial_schema.user_pois_temp
        WHERE id = $1
      `;
			params = [poiId];
		} else {
			return {
				isValid: false,
				error: 'Unsupported POI type',
			};
		}

		const result = await db.query(query, params);

		if (result.rows.length === 0) {
			return {
				isValid: false,
				error: 'POI not found',
			};
		}

		return {
			isValid: true,
			poi: result.rows[0],
		};
	} catch (error) {
		return {
			isValid: false,
			error: error instanceof Error ? error.message : 'Database error',
		};
	}
}

// POST /api/media/upload - Unified media upload endpoint
export async function POST(request: NextRequest) {
	try {
		// Check authentication
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const userId = session.user.id;

		// Parse form data
		const formData = await request.formData();
		const file = formData.get('file') as File;
		const uploadType = formData.get('uploadType') as string; // 'profile' or 'poi_media'
		const poiId = formData.get('poiId') as string;
		const poiType = (formData.get('poiType') as string) || 'official';
		const caption = (formData.get('caption') as string) || '';

		// Validate required fields
		logger.info('Validating upload request', {
			hasFile: !!file,
			uploadType,
			poiId,
			poiType,
			fileName: file?.name,
			fileSize: file?.size,
		});

		if (!file) {
			logger.error('File validation failed: No file provided');
			return NextResponse.json(
				{ success: false, error: 'File is required' },
				{ status: 400 }
			);
		}

		if (!uploadType || !['profile', 'poi_media'].includes(uploadType)) {
			logger.error('Upload type validation failed', { uploadType });
			return NextResponse.json(
				{
					success: false,
					error: 'Valid upload type is required (profile or poi_media)',
				},
				{ status: 400 }
			);
		}

		logger.info('Processing media upload', {
			userId,
			uploadType,
			poiId,
			poiType,
			fileName: file.name,
			fileSize: file.size,
			fileType: file.type,
		});

		logger.info('About to check upload type', { uploadType });

		if (uploadType === 'profile') {
			logger.info('Entering profile upload section');
			// Handle profile picture upload
			logger.info('Starting profile picture processing', {
				userId,
				fileName: file.name,
			});

			let result;
			try {
				result = await MediaManager.saveProfilePicture(userId, file);
				logger.info('Profile picture processing result', {
					success: result.success,
					error: result.error,
					profilePictureUrl: result.profilePictureUrl,
				});
			} catch (profileError) {
				logger.error('Profile picture processing threw error', {
					userId,
					fileName: file.name,
					error:
						profileError instanceof Error
							? profileError.message
							: 'Unknown error',
					stack: profileError instanceof Error ? profileError.stack : undefined,
				});
				return NextResponse.json(
					{
						success: false,
						error: `Profile picture processing error: ${
							profileError instanceof Error
								? profileError.message
								: 'Unknown error'
						}`,
					},
					{ status: 500 }
				);
			}

			if (!result.success) {
				logger.error('Profile picture processing failed', {
					userId,
					fileName: file.name,
					error: result.error,
				});
				return NextResponse.json(
					{
						success: false,
						error: result.error || 'Failed to upload profile picture',
					},
					{ status: 400 }
				);
			}

			if (!result.success) {
				logger.error('Profile picture processing failed', {
					userId,
					fileName: file.name,
					error: result.error,
				});
				return NextResponse.json(
					{
						success: false,
						error: result.error || 'Failed to upload profile picture',
					},
					{ status: 400 }
				);
			}

			// Update user profile in database
			await db.query(
				'UPDATE backend_schema.nextauth_users SET profile_picture_url = $1 WHERE id = $2',
				[result.profilePictureUrl, userId]
			);

			logger.info('Profile picture uploaded successfully', {
				userId,
				profilePictureUrl: result.profilePictureUrl,
			});

			return NextResponse.json({
				success: true,
				uploadType: 'profile',
				mediaUrl: result.profilePictureUrl,
				thumbnails: result.thumbnails,
				warnings: result.warnings,
			});
		} else if (uploadType === 'cover_photo') {
			// Handle cover photo upload
			const result = await MediaManager.saveProfilePicture(userId, file); // Reuse profile picture logic for now

			if (!result.success) {
				return NextResponse.json(
					{
						success: false,
						error: result.error || 'Failed to upload cover photo',
					},
					{ status: 400 }
				);
			}

			// For now, we'll store cover photo URL in a custom field or use the same profile logic
			// In production, you might want a separate cover_photo_url field
			logger.info('Cover photo uploaded successfully', {
				userId,
				coverPhotoUrl: result.profilePictureUrl,
			});

			return NextResponse.json({
				success: true,
				uploadType: 'cover_photo',
				mediaUrl: result.profilePictureUrl,
				thumbnails: result.thumbnails,
				warnings: result.warnings,
			});
		} else if (uploadType === 'poi_media') {
			// Handle POI media upload
			logger.info('Processing POI media upload', { poiId, poiType });

			if (!poiId) {
				logger.error('POI ID missing for POI media upload');
				return NextResponse.json(
					{ success: false, error: 'POI ID is required for POI media uploads' },
					{ status: 400 }
				);
			}

			// Validate POI exists
			logger.info('Validating POI for upload', { poiId, poiType });
			const poiValidation = await validatePOIForUpload(
				poiId,
				poiType as 'official' | 'user_temp' | 'user_approved'
			);
			logger.info('POI validation result', {
				isValid: poiValidation.isValid,
				error: poiValidation.error,
				hasPOI: !!poiValidation.poi,
			});

			if (!poiValidation.isValid || !poiValidation.poi) {
				logger.error('POI validation failed', {
					poiId,
					poiType,
					error: poiValidation.error,
				});
				return NextResponse.json(
					{ success: false, error: poiValidation.error || 'Invalid POI' },
					{ status: 400 }
				);
			}

			const validatedPOI = poiValidation.poi;

			// Process and save media
			logger.info('Starting media processing', {
				userId,
				poiId,
				fileName: file.name,
			});

			let mediaResult;
			try {
				mediaResult = await MediaManager.savePostMedia(userId, poiId, file);
				logger.info('Media processing result', {
					success: mediaResult.success,
					error: mediaResult.error,
					mediaUrl: mediaResult.mediaUrl,
				});
			} catch (mediaError) {
				logger.error('Media processing threw error', {
					userId,
					poiId,
					fileName: file.name,
					error:
						mediaError instanceof Error ? mediaError.message : 'Unknown error',
					stack: mediaError instanceof Error ? mediaError.stack : undefined,
				});
				return NextResponse.json(
					{
						success: false,
						error: `Media processing error: ${
							mediaError instanceof Error ? mediaError.message : 'Unknown error'
						}`,
					},
					{ status: 500 }
				);
			}

			if (!mediaResult.success) {
				logger.error('Media processing failed', {
					userId,
					poiId,
					fileName: file.name,
					error: mediaResult.error,
				});
				return NextResponse.json(
					{
						success: false,
						error: mediaResult.error || 'Failed to save media',
					},
					{ status: 400 }
				);
			}

			// Save media record to database using MediaDatabaseService
			const metadata = {
				originalFileName: file.name,
				fileSize: file.size,
				mimeType: file.type,
				poiName: validatedPOI.name as string | undefined,
				poiLocation: [validatedPOI.city, validatedPOI.district]
					.filter(Boolean)
					.join(', '),
				uploadedAt: new Date().toISOString(),
				warnings: mediaResult.warnings || [],
			};

			const thumbnailUrl =
				mediaResult.thumbnails && mediaResult.thumbnails.length > 0
					? mediaResult.thumbnails[0]
					: undefined;

			// Map 'image' to 'photo' for database constraint compatibility
			const dbMediaType =
				mediaResult.mediaType === 'image'
					? 'photo'
					: (mediaResult.mediaType as 'photo' | 'video');

			const savedMedia = await MediaDatabaseService.createMedia({
				poiId,
				userId,
				mediaType: dbMediaType,
				mediaUrl: mediaResult.mediaUrl!,
				thumbnailUrl,
				caption,
				metadata,
				isVerified: false, // default to false for user uploads
			});

			logger.info('POI media uploaded successfully', {
				userId,
				poiId,
				mediaId: savedMedia.id,
				mediaUrl: mediaResult.mediaUrl,
			});

			return NextResponse.json({
				success: true,
				uploadType: 'poi_media',
				media: savedMedia,
				mediaUrl: mediaResult.mediaUrl,
				thumbnails: mediaResult.thumbnails,
				mediaType: mediaResult.mediaType,
				warnings: mediaResult.warnings,
				poi: {
					id: validatedPOI.poi_id,
					name: validatedPOI.name,
					location: [validatedPOI.city, validatedPOI.district]
						.filter(Boolean)
						.join(', '),
				},
			});
		}
	} catch (error) {
		logger.error('Error in media upload', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to upload media' },
			{ status: 500 }
		);
	}
}

// GET /api/media/upload - Get upload status or user storage info
export async function GET() {
	try {
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const userId = session.user.id;

		// Get user's storage usage
		const storageResult = await MediaManager.getUserStorageUsage(userId);

		if (!storageResult.success) {
			return NextResponse.json(
				{ success: false, error: 'Failed to get storage info' },
				{ status: 500 }
			);
		}

		// Get user's media count from database
		const mediaCountQuery = `
      SELECT 
        COUNT(*) as total_media,
        COUNT(CASE WHEN media_type = 'photo' THEN 1 END) as photo_count,
        COUNT(CASE WHEN media_type = 'video' THEN 1 END) as video_count
      FROM spatial_schema.poi_media 
      WHERE user_id = $1
    `;
		const mediaCountResult = await db.query(mediaCountQuery, [userId]);
		const mediaCounts = mediaCountResult.rows[0];

		return NextResponse.json({
			success: true,
			storage: storageResult.usage,
			mediaCounts: {
				total: parseInt(mediaCounts.total_media),
				photos: parseInt(mediaCounts.photo_count),
				videos: parseInt(mediaCounts.video_count),
			},
		});
	} catch (error) {
		logger.error('Error getting media upload info', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to get upload info' },
			{ status: 500 }
		);
	}
}
