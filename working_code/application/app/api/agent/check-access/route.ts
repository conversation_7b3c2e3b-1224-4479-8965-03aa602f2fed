/** @format */

import { canAccessAgentDashboard } from '@/lib/agent-middleware';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextResponse } from 'next/server';

// GET /api/agent/check-access - Check if user can access agent dashboard
export async function GET() {
	try {
		// Get user session
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, hasAccess: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		// Check if user can access agent dashboard (agent or superuser)
		const hasAccess = await canAccessAgentDashboard(session.user.id);

		logger.info('Agent dashboard access check', {
			userId: session.user.id,
			hasAccess,
		});

		return NextResponse.json({
			success: true,
			hasAccess,
			userId: session.user.id,
		});
	} catch (error) {
		logger.error('Error checking agent dashboard access', { error });
		return NextResponse.json(
			{ success: false, hasAccess: false, error: 'Access check failed' },
			{ status: 500 }
		);
	}
}
