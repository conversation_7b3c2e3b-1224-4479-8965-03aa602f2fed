/** @format */

import {
	AuthenticatedAgentRequest,
	logAgentActivity,
	withAgentAuth,
} from '@/lib/agent-middleware';
import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { NextResponse } from 'next/server';

// POST /api/agent/submissions/bulk-action - Process bulk submission actions
export const POST = withAgentAuth(['full_access'])(
	async (request: AuthenticatedAgentRequest) => {
		try {
			const body = await request.json();
			const { submission_ids, action, notes } = body;

			// Validation
			if (
				!submission_ids ||
				!Array.isArray(submission_ids) ||
				submission_ids.length === 0
			) {
				return NextResponse.json(
					{ success: false, error: 'Submission IDs array is required' },
					{ status: 400 }
				);
			}

			if (!action) {
				return NextResponse.json(
					{ success: false, error: 'Action is required' },
					{ status: 400 }
				);
			}

			const allowedActions = ['approve', 'reject', 'review', 'pending'];
			if (!allowedActions.includes(action)) {
				return NextResponse.json(
					{ success: false, error: 'Invalid action' },
					{ status: 400 }
				);
			}

			// Limit bulk operations to prevent abuse
			if (submission_ids.length > 100) {
				return NextResponse.json(
					{
						success: false,
						error: 'Maximum 100 submissions can be processed at once',
					},
					{ status: 400 }
				);
			}

			logger.info('Agent processing bulk submission action', {
				agentId: request.agent.id,
				agentRole: request.agent.role,
				submissionIds: submission_ids,
				action,
				count: submission_ids.length,
			});

			try {
				await db.query('BEGIN');

				// Map action to status
				const statusMap: { [key: string]: string } = {
					approve: 'approved',
					reject: 'rejected',
					review: 'reviewing',
					pending: 'pending',
				};

				const newStatus = statusMap[action];

				// Create placeholders for the IN clause
				const placeholders = submission_ids
					.map((_, index) => `$${index + 1}`)
					.join(',');

				// Update all submissions
				const updateQuery = `
        UPDATE spatial_schema.user_pois_temp 
        SET 
          admin_review_status = $${submission_ids.length + 1},
          admin_review_notes = $${submission_ids.length + 2},
          reviewed_by = $${submission_ids.length + 3},
          reviewed_at = NOW(),
          updated_at = NOW()
        WHERE id IN (${placeholders})
        RETURNING *
      `;

				const updateParams = [
					...submission_ids,
					newStatus,
					notes || `Bulk ${action} by agent`,
					request.agent.id,
				];

				const updateResult = await db.query(updateQuery, updateParams);
				const updatedSubmissions = updateResult.rows;

				logger.info(
					`Updated ${updatedSubmissions.length} submissions with bulk action`
				);

				// If approved, move to approved table
				if (action === 'approve') {
					for (const submission of updatedSubmissions) {
						// Map submission_reason to submission_type for database constraint
						const submissionTypeMap: { [key: string]: string } = {
							closed: 'closure_request',
							info_update: 'info_update',
							new_poi: 'new_poi',
						};
						const submissionType =
							submissionTypeMap[submission.submission_reason] || 'new_poi';

						const approveQuery = `
            INSERT INTO spatial_schema.user_pois_approved (
              original_temp_id,
              submitted_by_user_id,
              approved_by_admin_id,
              name,
              name_en,
              name_tr,
              name_uk,
              name_de,
              name_ru,
              name_ar,
              category,
              subcategory,
              cuisine,
              city,
              district,
              neighborhood,
              street,
              full_address,
              province,
              phone_number,
              opening_hours,
              description,
              latitude,
              longitude,
              geom,
              submission_notes,
              admin_review_notes,
              submission_type
            ) VALUES (
              $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29
            )
          `;

						await db.query(approveQuery, [
							submission.id,
							submission.submitted_by_user_id,
							request.agent.id,
							submission.name,
							submission.name_en,
							submission.name_tr,
							submission.name_uk,
							submission.name_de,
							submission.name_ru,
							submission.name_ar,
							submission.category,
							submission.subcategory,
							submission.cuisine,
							submission.city,
							submission.district,
							submission.neighborhood,
							submission.street,
							submission.full_address,
							submission.province,
							submission.phone_number,
							submission.opening_hours,
							submission.description,
							submission.latitude,
							submission.longitude,
							submission.geom,
							submission.submission_notes,
							notes || `Bulk ${action} by agent`,
							submissionType,
						]);
					}
				}

				// Update submission tracking in backend schema
				const trackingUpdateQuery = `
        UPDATE backend_schema.user_poi_submissions 
        SET 
          status = $${submission_ids.length + 1},
          admin_feedback = $${submission_ids.length + 2},
          reviewed_at = NOW()
        WHERE temp_poi_id IN (${placeholders})
      `;

				const trackingParams = [
					...submission_ids,
					newStatus,
					notes || `Bulk ${action} by agent`,
				];

				await db.query(trackingUpdateQuery, trackingParams);

				await db.query('COMMIT');

				// Log the bulk agent activity
				await logAgentActivity(
					request.agent.id,
					`poi_bulk_${action}`,
					'poi_submission',
					submission_ids.join(','),
					{
						action,
						newStatus,
						notes,
						processedCount: updatedSubmissions.length,
					},
					request
				);

				logger.info('Bulk submission action processed successfully', {
					submissionIds: submission_ids,
					action,
					newStatus,
					processedCount: updatedSubmissions.length,
					agentId: request.agent.id,
				});

				return NextResponse.json({
					success: true,
					message: `${updatedSubmissions.length} submissions ${action}d successfully`,
					processed_count: updatedSubmissions.length,
					action,
					status: newStatus,
				});
			} catch (dbError) {
				await db.query('ROLLBACK');
				throw dbError;
			}
		} catch (error) {
			logger.error('Error processing bulk submission action', { error });
			return NextResponse.json(
				{ success: false, error: 'Failed to process bulk action' },
				{ status: 500 }
			);
		}
	}
);
