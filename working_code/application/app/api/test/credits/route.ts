/** @format */

import { awardCredits, getAvailableCredits } from '@/lib/credits';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

// POST /api/test/credits - Test credit awarding (for debugging)
export async function POST(request: NextRequest) {
	try {
		// Get user session
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const body = await request.json();
		const { amount = 0.5, reason = 'test_credit_award' } = body;

		const userId = session.user.id;

		logger.info('Testing credit award', { userId, amount, reason });

		// Get current credits before
		const creditsBefore = await getAvailableCredits(userId);
		logger.info('Credits before award', { userId, creditsBefore });

		// Award credits
		const success = await awardCredits(
			userId,
			amount,
			reason,
			'Test credit award for debugging',
			'test',
			'test-' + Date.now()
		);

		// Get current credits after
		const creditsAfter = await getAvailableCredits(userId);
		logger.info('Credits after award', { userId, creditsAfter });

		return NextResponse.json({
			success,
			message: success ? 'Credits awarded successfully' : 'Failed to award credits',
			userId,
			amount,
			reason,
			creditsBefore,
			creditsAfter,
			difference: creditsAfter - creditsBefore,
		});
	} catch (error) {
		logger.error('Error in credit test', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to test credits' },
			{ status: 500 }
		);
	}
}

// GET /api/test/credits - Get current credit status (for debugging)
export async function GET() {
	try {
		// Get user session
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const userId = session.user.id;

		// Get current credits
		const availableCredits = await getAvailableCredits(userId);

		return NextResponse.json({
			success: true,
			userId,
			availableCredits,
		});
	} catch (error) {
		logger.error('Error getting credit status', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to get credit status' },
			{ status: 500 }
		);
	}
}
