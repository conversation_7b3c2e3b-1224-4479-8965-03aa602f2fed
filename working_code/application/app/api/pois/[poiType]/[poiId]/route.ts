/** @format */

import { db } from '@/lib/database';
import { NextRequest, NextResponse } from 'next/server';

// GET /api/pois/[poiType]/[poiId] - Get individual POI details
export async function GET(
	request: NextRequest,
	context: { params: Promise<{ poiType: string; poiId: string }> }
) {
	try {
		const { poiType, poiId } = await context.params;

		console.log('Fetching POI:', { poiType, poiId });

		// Fetch from the correct table based on poiType
		let query = '';
		let params: string[] = [];
		if (poiType === 'official') {
			query = `
        SELECT
          'official' as poi_type,
          id as poi_id,
          null as temp_id,
          null as approved_id,
          name,
          profile_picture_url,
          name_en,
          name_tr,
          name_uk,
          name_de,
          name_ru,
          name_ar,
          category,
          subcategory,
          cuisine,
          city,
          district,
          neighborhood,
          street,
          full_address,
          province,
          latitude,
          longitude,
          phone_number,
          opening_hours,
          description
        FROM spatial_schema.pois
        WHERE id = $1
      `;
			params = [poiId]; // BIGINT handled as string
		} else if (poiType === 'user_approved') {
			query = `
        SELECT
          'user_approved' as poi_type,
          id as poi_id,
          original_temp_id as temp_id,
          null as approved_id,
          name,
          null as profile_picture_url,
          name_en,
          name_tr,
          name_uk,
          name_de,
          name_ru,
          name_ar,
          category,
          subcategory,
          cuisine,
          city,
          district,
          neighborhood,
          street,
          full_address,
          province,
          latitude,
          longitude,
          phone_number,
          opening_hours,
          description
        FROM spatial_schema.user_pois_approved
        WHERE id = $1
      `;
			params = [poiId]; // BIGINT handled as string
		} else if (poiType === 'user_temp') {
			query = `
        SELECT
          'user_temp' as poi_type,
          id as poi_id,
          id as temp_id,
          null as approved_id,
          name,
          null as profile_picture_url,
          name_en,
          name_tr,
          name_uk,
          name_de,
          name_ru,
          name_ar,
          category,
          subcategory,
          cuisine,
          city,
          district,
          neighborhood,
          street,
          full_address,
          province,
          latitude,
          longitude,
          phone_number,
          opening_hours,
          description
        FROM spatial_schema.user_pois_temp
        WHERE id = $1
      `;
			params = [poiId]; // BIGINT handled as string
		} else {
			return NextResponse.json(
				{ success: false, error: 'Unsupported POI type' },
				{ status: 400 }
			);
		}
		try {
			const result = await db.query(query, params);
			if (result.rows.length === 0) {
				return NextResponse.json(
					{ success: false, error: 'POI not found' },
					{ status: 404 }
				);
			}
			return NextResponse.json({
				success: true,
				poi: result.rows[0],
			});
		} catch (dbError) {
			const errorMessage =
				dbError instanceof Error ? dbError.message : 'Unknown database error';
			return NextResponse.json(
				{ success: false, error: 'Database error: ' + errorMessage },
				{ status: 500 }
			);
		}
	} catch (error) {
		console.error('Error in POI route:', error);
		const errorMessage =
			error instanceof Error ? error.message : 'Unknown error';
		return NextResponse.json(
			{ success: false, error: 'Failed to fetch POI: ' + errorMessage },
			{ status: 500 }
		);
	}
}
