/** @format */
// Unified Interaction API - Handles all interaction types with batch support
// Consolidates: /api/pois/interactions, /api/pois/interactions/batch, /api/pois/favorite

import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

// Unified response interface for all interaction types (reserved for future use)
// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface UnifiedInteractionResponse {
	success: boolean;
	error?: string;
	message?: string;
	interaction_id?: string;
	already_exists?: boolean;
	// Batch response format
	likes?: { count: number; isLiked: boolean };
	visits?: { count: number; hasVisited: boolean };
	favorites?: { count: number; isFavorited: boolean };
	shares?: { count: number; hasShared: boolean };
	// Individual response format
	interactions?: Array<{
		id: string;
		user_id: string;
		poi_id?: number;
		user_poi_temp_id?: number;
		user_poi_approved_id?: number;
		poi_type: string;
		interaction_type: string;
		metadata: Record<string, unknown>;
		created_at: string;
		updated_at?: string;
	}>;
	total_count?: number;
	has_more?: boolean;
	pagination?: {
		limit: number;
		offset: number;
		total: number;
		page: number;
		total_pages: number;
	};
}

// Shared validation function
function validatePOIParameters(
	poiType: string,
	poiId?: string,
	userPoiTempId?: string,
	userPoiApprovedId?: string
) {
	// Validate POI type
	if (
		!poiType ||
		!['official', 'user_temp', 'user_approved'].includes(poiType)
	) {
		return { valid: false, error: 'Valid POI type is required' };
	}

	// Validate POI ID based on type
	if (poiType === 'official' && !poiId) {
		return { valid: false, error: 'POI ID is required for official POIs' };
	}

	if (poiType === 'user_temp' && !userPoiTempId) {
		return {
			valid: false,
			error: 'User POI temp ID is required for temp POIs',
		};
	}

	if (poiType === 'user_approved' && !userPoiApprovedId) {
		return {
			valid: false,
			error: 'User POI approved ID is required for approved POIs',
		};
	}

	return { valid: true };
}

// POST /api/pois/interactions - Handle all user interactions (like, visit, share, favorite)
export async function POST(request: NextRequest) {
	try {
		// Get user session
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const body = await request.json();
		const {
			poiId,
			userPoiTempId,
			userPoiApprovedId,
			poiType,
			interactionType,
			action,
			metadata = {},
			// Batch mode support
			batch = false,
			pois = [],
		} = body;

		const userId = session.user.id;

		// Handle batch operations
		if (batch && pois.length > 0) {
			return handleBatchInteractions(userId, pois);
		}

		// Validate single interaction
		const validation = validatePOIParameters(
			poiType,
			poiId,
			userPoiTempId,
			userPoiApprovedId
		);
		if (!validation.valid) {
			return NextResponse.json(
				{ success: false, error: validation.error },
				{ status: 400 }
			);
		}

		// Validate interaction type - now includes 'favorite'
		const allowedTypes = ['like', 'visit', 'share', 'favorite'];
		if (!interactionType || !allowedTypes.includes(interactionType)) {
			return NextResponse.json(
				{
					success: false,
					error:
						'Valid interaction type is required (like, visit, share, favorite)',
				},
				{ status: 400 }
			);
		}

		if (!action || !['add', 'remove'].includes(action)) {
			return NextResponse.json(
				{ success: false, error: 'Action must be "add" or "remove"' },
				{ status: 400 }
			);
		}

		logger.info(`${action} ${interactionType} interaction`, {
			userId,
			poiType,
			poiId,
			userPoiTempId,
			userPoiApprovedId,
			interactionType,
		});

		try {
			await db.query('BEGIN');

			if (action === 'add') {
				// NOTE: Daily limits removed for all interactions (likes, visits, favorites, shares)
				// Interactions are now unlimited for better user experience

				// Check if interaction already exists
				const existingQuery = `
					SELECT id FROM backend_schema.user_interactions
					WHERE user_id = $1 
					AND poi_type = $2
					AND interaction_type = $3
					AND (
						(poi_type = 'official' AND poi_id = $4) OR
						(poi_type = 'user_temp' AND user_poi_temp_id = $5) OR
						(poi_type = 'user_approved' AND user_poi_approved_id = $6)
					)
				`;

				const existing = await db.query(existingQuery, [
					userId,
					poiType,
					interactionType,
					poiId || null,
					userPoiTempId || null,
					userPoiApprovedId || null,
				]);

				if (existing.rows.length > 0) {
					await db.query('ROLLBACK');
					return NextResponse.json({
						success: true,
						message: `POI is already ${interactionType}d`,
						already_exists: true,
					});
				}

				// Create interaction record
				const insertQuery = `
					INSERT INTO backend_schema.user_interactions (
						user_id, poi_id, user_poi_temp_id, user_poi_approved_id,
						poi_type, interaction_type, metadata
					) VALUES ($1, $2, $3, $4, $5, $6, $7)
					RETURNING id
				`;

				const result = await db.query(insertQuery, [
					userId,
					poiId || null,
					userPoiTempId || null,
					userPoiApprovedId || null,
					poiType,
					interactionType,
					JSON.stringify(metadata),
				]);

				// Update spatial schema counters for official POIs
				if (poiType === 'official' && poiId) {
					const updateQuery = `
						UPDATE spatial_schema.pois 
						SET ${interactionType}_count = ${interactionType}_count + 1,
							popularity_score = popularity_score + 1
						WHERE id = $1
					`;
					await db.query(updateQuery, [poiId]);
				}

				// NOTE: Daily limit increment removed for all interactions

				await db.query('COMMIT');

				logger.info(`Added ${interactionType} interaction`, {
					interactionId: result.rows[0].id,
					userId,
					poiType,
					interactionType,
				});

				return NextResponse.json({
					success: true,
					message: `${interactionType} added successfully`,
					interaction_id: result.rows[0].id,
				});
			} else if (action === 'remove') {
				// Remove interaction
				const deleteQuery = `
					DELETE FROM backend_schema.user_interactions
					WHERE user_id = $1 
					AND poi_type = $2
					AND interaction_type = $3
					AND (
						(poi_type = 'official' AND poi_id = $4) OR
						(poi_type = 'user_temp' AND user_poi_temp_id = $5) OR
						(poi_type = 'user_approved' AND user_poi_approved_id = $6)
					)
					RETURNING id
				`;

				const result = await db.query(deleteQuery, [
					userId,
					poiType,
					interactionType,
					poiId || null,
					userPoiTempId || null,
					userPoiApprovedId || null,
				]);

				if (result.rows.length === 0) {
					await db.query('ROLLBACK');
					return NextResponse.json(
						{
							success: false,
							error: `No ${interactionType} interaction found to remove`,
						},
						{ status: 404 }
					);
				}

				// Update spatial schema counters for official POIs
				if (poiType === 'official' && poiId) {
					const updateQuery = `
						UPDATE spatial_schema.pois 
						SET ${interactionType}_count = GREATEST(${interactionType}_count - 1, 0),
							popularity_score = GREATEST(popularity_score - 1, 0)
						WHERE id = $1
					`;
					await db.query(updateQuery, [poiId]);
				}

				await db.query('COMMIT');

				logger.info(`Removed ${interactionType} interaction`, {
					interactionId: result.rows[0].id,
					userId,
					poiType,
					interactionType,
				});

				return NextResponse.json({
					success: true,
					message: `${interactionType} removed successfully`,
				});
			}
		} catch (dbError) {
			await db.query('ROLLBACK');
			logger.error('Database error in interaction operation:', {
				error: dbError instanceof Error ? dbError.message : String(dbError),
				stack: dbError instanceof Error ? dbError.stack : undefined,
			});
			throw dbError;
		}
	} catch (error) {
		logger.error('Error handling interaction:', {
			error: error instanceof Error ? error.message : String(error),
			stack: error instanceof Error ? error.stack : undefined,
		});
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to process interaction',
			},
			{ status: 500 }
		);
	}
}

// Batch GET handler (single POI, all interaction types)
async function handleBatchGet(
	currentUserId: string | null,
	params: {
		poiId?: string | null;
		userPoiTempId?: string | null;
		userPoiApprovedId?: string | null;
		poiType?: string | null;
		userStatesOnly?: boolean;
	}
): Promise<NextResponse> {
	try {
		const { poiId, userPoiTempId, userPoiApprovedId, poiType } = params;

		// Validate POI parameters
		if (
			!poiType ||
			!['official', 'user_temp', 'user_approved'].includes(poiType)
		) {
			return NextResponse.json(
				{ success: false, error: 'Valid POI type is required' },
				{ status: 400 }
			);
		}

		const validation = validatePOIParameters(
			poiType,
			poiId || undefined,
			userPoiTempId || undefined,
			userPoiApprovedId || undefined
		);
		if (!validation.valid) {
			return NextResponse.json(
				{ success: false, error: validation.error },
				{ status: 400 }
			);
		}

		// Build query for batch interaction data
		const whereConditions: string[] = ['poi_type = $1'];
		const queryParams: (string | number)[] = [poiType];
		let paramIndex = 2;

		if (poiType === 'official' && poiId) {
			whereConditions.push(`poi_id = $${paramIndex}`);
			queryParams.push(parseInt(poiId));
			paramIndex++;
		} else if (poiType === 'user_temp' && userPoiTempId) {
			whereConditions.push(`user_poi_temp_id = $${paramIndex}`);
			queryParams.push(parseInt(userPoiTempId));
			paramIndex++;
		} else if (poiType === 'user_approved' && userPoiApprovedId) {
			whereConditions.push(`user_poi_approved_id = $${paramIndex}`);
			queryParams.push(parseInt(userPoiApprovedId));
			paramIndex++;
		}

		const batchQuery = `
			SELECT
				interaction_type,
				COUNT(*) as total_count,
				${
					currentUserId
						? `BOOL_OR(CASE WHEN user_id = $${paramIndex} THEN true ELSE false END) as user_has_interaction`
						: 'false as user_has_interaction'
				}
			FROM backend_schema.user_interactions
			WHERE ${whereConditions.join(' AND ')}
				AND interaction_type IN ('like', 'favorite', 'visit', 'share')
			GROUP BY interaction_type
		`;

		if (currentUserId) {
			queryParams.push(currentUserId);
		}

		const result = await db.query(batchQuery, queryParams);

		// Process results into structured format
		const interactions: {
			[key: string]: { count: number; hasInteraction: boolean };
		} = {
			like: { count: 0, hasInteraction: false },
			visit: { count: 0, hasInteraction: false },
			favorite: { count: 0, hasInteraction: false },
			share: { count: 0, hasInteraction: false },
		};

		result.rows.forEach((row) => {
			const type = row.interaction_type;
			if (interactions[type]) {
				interactions[type].count = parseInt(row.total_count) || 0;
				interactions[type].hasInteraction = row.user_has_interaction || false;
			}
		});

		// Format response to match existing batch API expectations
		const response = {
			success: true,
			likes: {
				count: interactions.like.count,
				isLiked: interactions.like.hasInteraction,
			},
			visits: {
				count: interactions.visit.count,
				hasVisited: interactions.visit.hasInteraction,
			},
			favorites: {
				count: interactions.favorite.count,
				isFavorited: interactions.favorite.hasInteraction,
			},
			shares: {
				count: interactions.share.count,
				hasShared: interactions.share.hasInteraction,
			},
		};

		logger.info('Batched interactions loaded successfully', {
			poiId,
			userPoiTempId,
			userPoiApprovedId,
			poiType,
			userId: currentUserId,
			response,
		});

		return NextResponse.json(response);
	} catch (error) {
		logger.error('Error fetching batched interactions:', {
			error: error instanceof Error ? error.message : String(error),
			stack: error instanceof Error ? error.stack : undefined,
			...params,
			userId: currentUserId,
		});
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to load interactions',
				likes: { count: 0, isLiked: false },
				visits: { count: 0, hasVisited: false },
				favorites: { count: 0, isFavorited: false },
				shares: { count: 0, hasShared: false },
			},
			{ status: 500 }
		);
	}
}

// GET /api/pois/interactions - Get user interactions with batch support
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const poiId = searchParams.get('poiId');
		const userPoiTempId = searchParams.get('userPoiTempId');
		const userPoiApprovedId = searchParams.get('userPoiApprovedId');
		const poiType = searchParams.get('poiType');
		const interactionType = searchParams.get('interactionType');
		const userId = searchParams.get('userId');
		const batch = searchParams.get('batch') === 'true';
		const batchMode = searchParams.get('batchMode');
		// userStatesOnly reserved for future use
		// const userStatesOnly = searchParams.get('userStatesOnly') === 'true'; // Not used in current implementation
		const limit = parseInt(searchParams.get('limit') || '20');
		const offset = parseInt(searchParams.get('offset') || '0');
		const sortBy = searchParams.get('sortBy') || 'created_at';
		const sortOrder = searchParams.get('sortOrder') || 'desc';

		// Get current user session
		const session = await getServerSession(authOptions);
		const currentUserId = userId || session?.user?.id;

		// Handle batch mode (single POI, all interaction types)
		if (batch || batchMode) {
			return handleBatchGet(currentUserId || null, {
				poiId,
				userPoiTempId,
				userPoiApprovedId,
				poiType,
				userStatesOnly: false, // Default value since not used
			});
		}

		// Handle individual interaction queries
		const whereConditions: string[] = [];
		const queryParams: (string | number)[] = [];
		let paramIndex = 1;

		// Add user filter if provided
		if (currentUserId) {
			whereConditions.push(`ui.user_id = $${paramIndex}`);
			queryParams.push(currentUserId);
			paramIndex++;
		}

		// Add POI filters
		if (poiType) {
			const validation = validatePOIParameters(
				poiType,
				poiId || undefined,
				userPoiTempId || undefined,
				userPoiApprovedId || undefined
			);
			if (!validation.valid) {
				return NextResponse.json(
					{ success: false, error: validation.error },
					{ status: 400 }
				);
			}

			whereConditions.push(`ui.poi_type = $${paramIndex}`);
			queryParams.push(poiType);
			paramIndex++;

			if (poiType === 'official' && poiId) {
				whereConditions.push(`ui.poi_id = $${paramIndex}`);
				queryParams.push(parseInt(poiId));
				paramIndex++;
			} else if (poiType === 'user_temp' && userPoiTempId) {
				whereConditions.push(`ui.user_poi_temp_id = $${paramIndex}`);
				queryParams.push(parseInt(userPoiTempId));
				paramIndex++;
			} else if (poiType === 'user_approved' && userPoiApprovedId) {
				whereConditions.push(`ui.user_poi_approved_id = $${paramIndex}`);
				queryParams.push(parseInt(userPoiApprovedId));
				paramIndex++;
			}
		}

		// Add interaction type filter
		if (interactionType) {
			whereConditions.push(`ui.interaction_type = $${paramIndex}`);
			queryParams.push(interactionType);
			paramIndex++;
		}

		// Build query with POI name joins
		const whereClause =
			whereConditions.length > 0
				? `WHERE ${whereConditions.join(' AND ')}`
				: '';
		const query = `
			SELECT
				ui.id,
				ui.user_id,
				ui.poi_id,
				ui.user_poi_temp_id,
				ui.user_poi_approved_id,
				ui.poi_type,
				ui.interaction_type,
				ui.metadata,
				ui.created_at,
				ui.updated_at,
				CASE
					WHEN ui.poi_type = 'official' THEN
						COALESCE(p.name, p.name_en, p.name_tr, p.name_uk, p.name_de, p.name_ru, p.name_ar, 'Unnamed Location')
					WHEN ui.poi_type = 'user_temp' THEN
						COALESCE(upt.name, upt.name_en, upt.name_tr, upt.name_uk, upt.name_de, upt.name_ru, upt.name_ar, 'Unnamed Location')
					WHEN ui.poi_type = 'user_approved' THEN
						COALESCE(upa.name, upa.name_en, upa.name_tr, upa.name_uk, upa.name_de, upa.name_ru, upa.name_ar, 'Unnamed Location')
					ELSE 'Unknown POI'
				END as poi_name
			FROM backend_schema.user_interactions ui
			LEFT JOIN spatial_schema.pois p ON ui.poi_type = 'official' AND ui.poi_id = p.id
			LEFT JOIN spatial_schema.user_pois_temp upt ON ui.poi_type = 'user_temp' AND ui.user_poi_temp_id = upt.id
			LEFT JOIN spatial_schema.user_pois_approved upa ON ui.poi_type = 'user_approved' AND ui.user_poi_approved_id = upa.id
			${whereClause}
			ORDER BY ui.${sortBy} ${sortOrder}
			LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
		`;

		queryParams.push(limit, offset);

		const result = await db.query(query, queryParams);

		// Get total count for pagination
		const countQuery = `
			SELECT COUNT(*) as total
			FROM backend_schema.user_interactions ui
			LEFT JOIN spatial_schema.pois p ON ui.poi_type = 'official' AND ui.poi_id = p.id
			LEFT JOIN spatial_schema.user_pois_temp upt ON ui.poi_type = 'user_temp' AND ui.user_poi_temp_id = upt.id
			LEFT JOIN spatial_schema.user_pois_approved upa ON ui.poi_type = 'user_approved' AND ui.user_poi_approved_id = upa.id
			${whereClause}
		`;
		const countResult = await db.query(countQuery, queryParams.slice(0, -2));
		const totalCount = parseInt(countResult.rows[0].total);

		return NextResponse.json({
			success: true,
			interactions: result.rows,
			total_count: totalCount,
			has_more: offset + limit < totalCount,
			pagination: {
				limit,
				offset,
				total: totalCount,
				page: Math.floor(offset / limit) + 1,
				total_pages: Math.ceil(totalCount / limit),
			},
		});
	} catch (error) {
		logger.error('Error fetching interactions:', {
			error: error instanceof Error ? error.message : String(error),
			stack: error instanceof Error ? error.stack : undefined,
		});
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch interactions',
			},
			{ status: 500 }
		);
	}
}

// Batch interaction handler for POST
async function handleBatchInteractions(
	userId: string,
	pois: Array<{ poi_id: number; poi_type?: string }>
): Promise<NextResponse> {
	try {
		// Limit batch size to prevent abuse
		if (pois.length > 50) {
			return NextResponse.json(
				{ success: false, error: 'Maximum 50 POIs per batch' },
				{ status: 400 }
			);
		}

		logger.info('Batch loading interactions', {
			userId,
			poiCount: pois.length,
		});

		// Extract POI IDs and types
		const poiIds = pois.map((poi) => poi.poi_id).filter(Boolean);
		const poiTypes = pois.map((poi) => poi.poi_type || 'official');

		if (poiIds.length === 0) {
			return NextResponse.json({
				success: true,
				interactions: [],
			});
		}

		// Get interaction counts and user states for all POIs
		const batchQuery = `
			SELECT
				poi_id,
				poi_type,
				interaction_type,
				COUNT(*) as count,
				BOOL_OR(CASE WHEN user_id = $1 THEN true ELSE false END) as user_has_interaction
			FROM backend_schema.user_interactions
			WHERE poi_id = ANY($2)
				AND poi_type = ANY($3)
				AND interaction_type IN ('like', 'favorite', 'visit', 'share')
			GROUP BY poi_id, poi_type, interaction_type
		`;

		const result = await db.query(batchQuery, [userId, poiIds, poiTypes]);

		// Get review counts separately
		const reviewQuery = `
			SELECT
				poi_id,
				poi_type,
				COUNT(*) as review_count
			FROM backend_schema.user_location_reviews
			WHERE poi_id = ANY($1)
				AND poi_type = ANY($2)
			GROUP BY poi_id, poi_type
		`;

		const reviewResult = await db.query(reviewQuery, [poiIds, poiTypes]);

		// Process results into structured format
		const interactions: {
			[key: string]: {
				poi_id: number;
				poi_type: string;
				like: { count: number; hasInteraction: boolean };
				visit: { count: number; hasInteraction: boolean };
				favorite: { count: number; hasInteraction: boolean };
				share: { count: number; hasInteraction: boolean };
				review: { count: number };
			};
		} = {};

		// Initialize all requested POIs with zero interactions
		pois.forEach((poi) => {
			const key = `${poi.poi_id}_${poi.poi_type || 'official'}`;
			interactions[key] = {
				poi_id: poi.poi_id,
				poi_type: poi.poi_type || 'official',
				like: { count: 0, hasInteraction: false },
				visit: { count: 0, hasInteraction: false },
				favorite: { count: 0, hasInteraction: false },
				share: { count: 0, hasInteraction: false },
				review: { count: 0 },
			};
		});

		// Update with actual interaction data from database
		result.rows.forEach((row) => {
			const key = `${row.poi_id}_${row.poi_type}`;
			if (interactions[key]) {
				const type = row.interaction_type as
					| 'like'
					| 'visit'
					| 'favorite'
					| 'share';
				if (interactions[key][type]) {
					interactions[key][type].count = parseInt(row.count) || 0;
					interactions[key][type].hasInteraction =
						row.user_has_interaction || false;
				}
			}
		});

		// Update with review counts
		reviewResult.rows.forEach((row) => {
			const key = `${row.poi_id}_${row.poi_type}`;
			if (interactions[key]) {
				interactions[key].review.count = parseInt(row.review_count) || 0;
			}
		});

		const interactionList = Object.values(interactions);

		logger.info('Batch interactions response', {
			userId,
			requestedPOIs: pois.length,
			returnedInteractions: interactionList.length,
		});

		return NextResponse.json({
			success: true,
			interactions: interactionList,
		});
	} catch (error) {
		logger.error('Error in batch interactions:', {
			error: error instanceof Error ? error.message : String(error),
			stack: error instanceof Error ? error.stack : undefined,
		});
		return NextResponse.json(
			{ success: false, error: 'Failed to load batch interactions' },
			{ status: 500 }
		);
	}
}
