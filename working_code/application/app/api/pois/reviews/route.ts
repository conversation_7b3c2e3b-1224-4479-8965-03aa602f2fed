/** @format */

import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

// GET /api/pois/reviews - Get reviews for a POI
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const poiId = searchParams.get('poiId');
		const poiType = searchParams.get('poiType') || 'official';
		const userPoiTempId = searchParams.get('userPoiTempId');
		const userPoiApprovedId = searchParams.get('userPoiApprovedId');
		const userId = searchParams.get('userId'); // For fetching user's reviews
		const limit = parseInt(searchParams.get('limit') || '20');
		const offset = parseInt(searchParams.get('offset') || '0');
		const sortBy = searchParams.get('sortBy') || 'created_at'; // created_at, rating, helpful_votes
		const sortOrder = searchParams.get('sortOrder') || 'desc';

		// Get current user for personalized data
		const session = await getServerSession(authOptions);
		const currentUserId = session?.user?.id;

		// Handle special case for fetching all reviews by a specific user
		if (userId) {
			// Validate that the user is requesting their own reviews or is authorized
			if (currentUserId !== userId) {
				return NextResponse.json(
					{ success: false, error: 'Unauthorized to view other users reviews' },
					{ status: 403 }
				);
			}

			// Build query for user's reviews across all POI types with POI names
			const query = `
        SELECT
          r.id,
          r.user_id,
          r.poi_id,
          r.user_poi_temp_id,
          r.user_poi_approved_id,
          r.poi_type,
          u.name as user_name,
          u.image as user_avatar,
          r.rating,
          r.review_title,
          r.review_text,
          r.visit_date,
          r.photos,
          r.tags,
          r.helpful_votes,
          r.is_verified,
          r.created_at,
          r.updated_at,
          true as is_own_review,
          CASE
            WHEN r.poi_type = 'official' THEN
              COALESCE(p.name, p.name_en, p.name_tr, p.name_uk, p.name_de, p.name_ru, p.name_ar, 'Unnamed Location')
            WHEN r.poi_type = 'user_temp' THEN
              COALESCE(upt.name, upt.name_en, upt.name_tr, upt.name_uk, upt.name_de, upt.name_ru, upt.name_ar, 'Unnamed Location')
            WHEN r.poi_type = 'user_approved' THEN
              COALESCE(upa.name, upa.name_en, upa.name_tr, upa.name_uk, upa.name_de, upa.name_ru, upa.name_ar, 'Unnamed Location')
            ELSE 'Unknown POI'
          END as poi_name
        FROM backend_schema.user_location_reviews r
        LEFT JOIN backend_schema.nextauth_users u ON r.user_id = u.id
        LEFT JOIN spatial_schema.pois p ON r.poi_type = 'official' AND r.poi_id = p.id
        LEFT JOIN spatial_schema.user_pois_temp upt ON r.poi_type = 'user_temp' AND r.user_poi_temp_id = upt.id
        LEFT JOIN spatial_schema.user_pois_approved upa ON r.poi_type = 'user_approved' AND r.user_poi_approved_id = upa.id
        WHERE r.user_id = $1
        ORDER BY ${
					sortBy === 'created_at'
						? 'r.created_at'
						: sortBy === 'rating'
						? 'r.rating'
						: 'r.helpful_votes'
				} ${sortOrder}
        LIMIT $2 OFFSET $3
      `;

			const countQuery = `
        SELECT COUNT(*) as total
        FROM backend_schema.user_location_reviews r
        LEFT JOIN spatial_schema.pois p ON r.poi_type = 'official' AND r.poi_id = p.id
        LEFT JOIN spatial_schema.user_pois_temp upt ON r.poi_type = 'user_temp' AND r.user_poi_temp_id = upt.id
        LEFT JOIN spatial_schema.user_pois_approved upa ON r.poi_type = 'user_approved' AND r.user_poi_approved_id = upa.id
        WHERE r.user_id = $1
      `;

			const result = await db.query(query, [userId, limit, offset]);
			const countResult = await db.query(countQuery, [userId]);
			const total = parseInt(countResult.rows[0]?.total || '0');

			return NextResponse.json({
				success: true,
				reviews: result.rows,
				pagination: {
					total,
					limit,
					offset,
					hasMore: offset + limit < total,
				},
			});
		}

		// Handle special case for fetching all reviews (when poiType is 'all')
		if (poiType === 'all') {
			// Build query for all reviews across all POI types
			const query = `
        SELECT
          r.id,
          r.user_id,
          r.poi_id,
          r.user_poi_temp_id,
          r.user_poi_approved_id,
          r.poi_type,
          u.name as user_name,
          u.image as user_avatar,
          r.rating,
          r.review_title,
          r.review_text,
          r.visit_date,
          r.photos,
          r.tags,
          r.helpful_votes,
          r.is_verified,
          r.created_at,
          r.updated_at,
          CASE WHEN r.user_id = $1 THEN true ELSE false END as is_own_review
        FROM backend_schema.user_location_reviews r
        LEFT JOIN backend_schema.nextauth_users u ON r.user_id = u.id
        ORDER BY ${
					sortBy === 'created_at'
						? 'r.created_at'
						: sortBy === 'rating'
						? 'r.rating'
						: 'r.helpful_votes'
				} ${sortOrder}
        LIMIT $2 OFFSET $3
      `;

			const countQuery = `
        SELECT COUNT(*) as total
        FROM backend_schema.user_location_reviews r
      `;

			const result = await db.query(query, [
				currentUserId || null,
				limit,
				offset,
			]);
			const countResult = await db.query(countQuery, []);
			const total = parseInt(countResult.rows[0]?.total || '0');

			return NextResponse.json({
				success: true,
				reviews: result.rows,
				pagination: {
					total,
					limit,
					offset,
					hasMore: offset + limit < total,
				},
			});
		}

		// Validate POI type for specific POI queries
		if (
			!poiType ||
			!['official', 'user_temp', 'user_approved'].includes(poiType)
		) {
			return NextResponse.json(
				{ success: false, error: 'Valid POI type is required' },
				{ status: 400 }
			);
		}

		// Validate POI ID based on type
		if (poiType === 'official' && !poiId) {
			return NextResponse.json(
				{ success: false, error: 'POI ID is required for official POIs' },
				{ status: 400 }
			);
		}

		if (poiType === 'user_temp' && !userPoiTempId) {
			return NextResponse.json(
				{ success: false, error: 'User POI temp ID is required for temp POIs' },
				{ status: 400 }
			);
		}

		if (poiType === 'user_approved' && !userPoiApprovedId) {
			return NextResponse.json(
				{
					success: false,
					error: 'User POI approved ID is required for approved POIs',
				},
				{ status: 400 }
			);
		}

		// Build the query
		let query = `
      SELECT
        r.id,
        r.user_id,
        r.poi_id,
        r.user_poi_temp_id,
        r.user_poi_approved_id,
        r.poi_type,
        u.name as user_name,
        u.image as user_avatar,
        r.rating,
        r.review_title,
        r.review_text,
        r.visit_date,
        r.photos,
        r.tags,
        r.helpful_votes,
        r.is_verified,
        r.created_at,
        r.updated_at,
        CASE WHEN r.user_id = $1 THEN true ELSE false END as is_own_review
      FROM backend_schema.user_location_reviews r
      LEFT JOIN backend_schema.nextauth_users u ON r.user_id = u.id
      WHERE r.poi_type = $2
    `;

		// Always pass currentUserId as first param (can be null)
		// Always pass poiType as second param
		const params: (string | number | null)[] = [currentUserId || null, poiType];

		// Add POI-specific conditions
		if (poiType === 'official') {
			query += ` AND r.poi_id = $${params.length + 1}`;
			params.push(parseInt(poiId!));
		} else if (poiType === 'user_temp') {
			query += ` AND r.user_poi_temp_id = $${params.length + 1}`;
			params.push(parseInt(userPoiTempId!));
		} else if (poiType === 'user_approved') {
			query += ` AND r.user_poi_approved_id = $${params.length + 1}`;
			params.push(parseInt(userPoiApprovedId!));
		}

		// Add sorting
		const validSortColumns = ['created_at', 'rating', 'helpful_votes'];
		const validSortOrders = ['asc', 'desc'];

		if (
			validSortColumns.includes(sortBy) &&
			validSortOrders.includes(sortOrder)
		) {
			query += ` ORDER BY r.${sortBy} ${sortOrder.toUpperCase()}`;
		} else {
			query += ` ORDER BY r.created_at DESC`;
		}

		// Add pagination
		query += ` LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
		params.push(limit, offset);

		const result = await db.query(query, params);

		// Get total count for pagination
		let countQuery = `
      SELECT COUNT(*) as total
      FROM backend_schema.user_location_reviews r
      WHERE r.poi_type = $1
    `;
		const countParams: (string | number)[] = [poiType];

		if (poiType === 'official') {
			countQuery += ` AND r.poi_id = $2`;
			countParams.push(parseInt(poiId!));
		} else if (poiType === 'user_temp') {
			countQuery += ` AND r.user_poi_temp_id = $2`;
			countParams.push(parseInt(userPoiTempId!));
		} else if (poiType === 'user_approved') {
			countQuery += ` AND r.user_poi_approved_id = $2`;
			countParams.push(parseInt(userPoiApprovedId!));
		}

		const countResult = await db.query(countQuery, countParams);
		const total = parseInt(countResult.rows[0]?.total || '0');

		return NextResponse.json({
			success: true,
			reviews: result.rows,
			pagination: {
				total,
				limit,
				offset,
				hasMore: offset + limit < total,
			},
		});
	} catch (error) {
		logger.error('Error fetching reviews:', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to fetch reviews' },
			{ status: 500 }
		);
	}
}

// POST /api/pois/reviews - Create a new review
export async function POST(request: NextRequest) {
	let userId: string | undefined;
	let poiId: string | undefined;
	let userPoiTempId: string | undefined;
	let userPoiApprovedId: string | undefined;
	let poiType: string | undefined;
	let rating: number | undefined;

	try {
		// Get user session
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		userId = session.user.id;
		const body = await request.json();
		const bodyData = body as {
			poiId: string;
			userPoiTempId?: string;
			userPoiApprovedId?: string;
			poiType: string;
			rating: number;
			review_title?: string;
			review_text?: string;
			visit_date?: string;
			photos?: string[];
			tags?: string[];
		};

		poiId = bodyData.poiId;
		userPoiTempId = bodyData.userPoiTempId;
		userPoiApprovedId = bodyData.userPoiApprovedId;
		poiType = bodyData.poiType;
		rating = bodyData.rating;
		const review_title = bodyData.review_title;
		const review_text = bodyData.review_text;
		const visit_date = bodyData.visit_date;
		const photos = bodyData.photos || [];
		const tags = bodyData.tags || [];

		// Validation
		if (
			!poiType ||
			!['official', 'user_temp', 'user_approved'].includes(poiType)
		) {
			return NextResponse.json(
				{ success: false, error: 'Valid POI type is required' },
				{ status: 400 }
			);
		}

		if (!rating || rating < 1 || rating > 5) {
			return NextResponse.json(
				{ success: false, error: 'Rating must be between 1 and 5' },
				{ status: 400 }
			);
		}

		// Validate POI ID based on type
		if (poiType === 'official' && !poiId) {
			return NextResponse.json(
				{ success: false, error: 'POI ID is required for official POIs' },
				{ status: 400 }
			);
		}

		if (poiType === 'user_temp' && !userPoiTempId) {
			return NextResponse.json(
				{ success: false, error: 'User POI temp ID is required for temp POIs' },
				{ status: 400 }
			);
		}

		if (poiType === 'user_approved' && !userPoiApprovedId) {
			return NextResponse.json(
				{
					success: false,
					error: 'User POI approved ID is required for approved POIs',
				},
				{ status: 400 }
			);
		}

		// Check if user already has a review for this POI
		const existingQuery = `
      SELECT id FROM backend_schema.user_location_reviews
      WHERE user_id = $1 
      AND poi_type = $2
      AND (
        (poi_type = 'official' AND poi_id = $3) OR
        (poi_type = 'user_temp' AND user_poi_temp_id = $4) OR
        (poi_type = 'user_approved' AND user_poi_approved_id = $5)
      )
    `;

		const existing = await db.query(existingQuery, [
			userId,
			poiType,
			poiId || null,
			userPoiTempId || null,
			userPoiApprovedId || null,
		]);

		if (existing.rows.length > 0) {
			return NextResponse.json(
				{ success: false, error: 'You have already reviewed this place' },
				{ status: 400 }
			);
		}

		// NOTE: Daily limits removed for comments - unlimited commenting allowed

		// Insert new review
		const insertQuery = `
      INSERT INTO backend_schema.user_location_reviews (
        user_id, poi_id, user_poi_temp_id, user_poi_approved_id, poi_type,
        rating, review_title, review_text, visit_date, photos, tags
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING id, created_at
    `;

		const result = await db.query(insertQuery, [
			userId,
			poiId || null,
			userPoiTempId || null,
			userPoiApprovedId || null,
			poiType,
			rating,
			review_title || null,
			review_text || null,
			visit_date || null,
			JSON.stringify(photos || []),
			JSON.stringify(tags || []),
		]);

		// NOTE: Daily limit increment removed for comments

		logger.info('Review created', {
			reviewId: result.rows[0].id,
			userId,
			poiType,
			poiId,
			userPoiTempId,
			userPoiApprovedId,
			rating,
		});

		return NextResponse.json({
			success: true,
			review: {
				id: result.rows[0].id,
				created_at: result.rows[0].created_at,
			},
		});
	} catch (error) {
		logger.error('Error creating review:', {
			error:
				error instanceof Error
					? {
							message: error.message,
							stack: error.stack,
							name: error.name,
					  }
					: error,
			userId,
			requestBody: {
				poiId,
				userPoiTempId,
				userPoiApprovedId,
				poiType,
				rating,
			},
		});
		return NextResponse.json(
			{ success: false, error: 'Failed to create review' },
			{ status: 500 }
		);
	}
}

// PUT /api/pois/reviews - Update an existing review
export async function PUT(request: NextRequest) {
	try {
		// Get user session
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const body = await request.json();
		const {
			reviewId,
			rating,
			review_title,
			review_text,
			visit_date,
			photos = [],
			tags = [],
		} = body;

		const userId = session.user.id;

		// Validation
		if (!reviewId) {
			return NextResponse.json(
				{ success: false, error: 'Review ID is required' },
				{ status: 400 }
			);
		}

		if (!rating || rating < 1 || rating > 5) {
			return NextResponse.json(
				{ success: false, error: 'Rating must be between 1 and 5' },
				{ status: 400 }
			);
		}

		// Check if review exists and belongs to user
		const checkQuery = `
      SELECT id FROM backend_schema.user_location_reviews
      WHERE id = $1 AND user_id = $2
    `;

		const existing = await db.query(checkQuery, [reviewId, userId]);

		if (existing.rows.length === 0) {
			return NextResponse.json(
				{
					success: false,
					error: 'Review not found or you do not have permission to edit it',
				},
				{ status: 404 }
			);
		}

		// Update review
		const updateQuery = `
      UPDATE backend_schema.user_location_reviews
      SET
        rating = $1,
        review_title = $2,
        review_text = $3,
        visit_date = $4,
        photos = $5,
        tags = $6,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $7 AND user_id = $8
      RETURNING id, updated_at
    `;

		const result = await db.query(updateQuery, [
			rating,
			review_title || null,
			review_text || null,
			visit_date || null,
			JSON.stringify(photos || []),
			JSON.stringify(tags || []),
			reviewId,
			userId,
		]);

		logger.info('Review updated', { reviewId, userId });

		return NextResponse.json({
			success: true,
			review: {
				id: result.rows[0].id,
				updated_at: result.rows[0].updated_at,
			},
		});
	} catch (error) {
		logger.error('Error updating review:', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to update review' },
			{ status: 500 }
		);
	}
}

// DELETE /api/pois/reviews - Delete a review
export async function DELETE(request: NextRequest) {
	try {
		// Get user session
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const { searchParams } = new URL(request.url);
		const reviewId = searchParams.get('reviewId');
		const userId = session.user.id;

		if (!reviewId) {
			return NextResponse.json(
				{ success: false, error: 'Review ID is required' },
				{ status: 400 }
			);
		}

		// Check if review exists and belongs to user
		const checkQuery = `
      SELECT id FROM backend_schema.user_location_reviews
      WHERE id = $1 AND user_id = $2
    `;

		const existing = await db.query(checkQuery, [reviewId, userId]);

		if (existing.rows.length === 0) {
			return NextResponse.json(
				{
					success: false,
					error: 'Review not found or you do not have permission to delete it',
				},
				{ status: 404 }
			);
		}

		// Delete review
		const deleteQuery = `
      DELETE FROM backend_schema.user_location_reviews
      WHERE id = $1 AND user_id = $2
    `;

		await db.query(deleteQuery, [reviewId, userId]);

		logger.info('Review deleted', { reviewId, userId });

		return NextResponse.json({
			success: true,
			message: 'Review deleted successfully',
		});
	} catch (error) {
		logger.error('Error deleting review:', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to delete review' },
			{ status: 500 }
		);
	}
}
