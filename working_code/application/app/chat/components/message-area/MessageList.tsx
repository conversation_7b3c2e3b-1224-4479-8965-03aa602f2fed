import React from 'react';
import { Message, TopCandidates } from 'app/chat/types';
import MessageItem from './MessageItem';

interface MessageListProps {
  messages: Message[];
  messagesContainerRef: React.RefObject<HTMLDivElement>;
  messagesEndRef: React.RefObject<HTMLDivElement>;
  onShowMap: () => void;
  extractedLocations?: TopCandidates | null;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  messagesContainerRef,
  messagesEndRef,
  onShowMap,
  extractedLocations,
}) => {
  return (
    <div
      className="flex-1 overflow-y-auto flex flex-col gap-4 p-6"
      ref={messagesContainerRef}
    >
      {messages.map((message, index) => (
        <MessageItem
          key={index}
          message={message}
          onShowMap={onShowMap}
          extractedLocations={extractedLocations}
        />
      ))}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
