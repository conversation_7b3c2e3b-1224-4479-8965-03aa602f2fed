import React from 'react';
import { MessageAreaProps } from 'app/chat/types';
import { chatLayoutStyles } from 'app/chat/styles';
import MessageList from './MessageList';
import ScrollToBottomButton from './ScrollToBottomButton';

const MessageArea: React.FC<MessageAreaProps> = ({
  messages,
  showScrollButton,
  scrollToBottom,
  messagesContainerRef,
  messagesEndRef,
  onShowMap,
  extractedLocations,
}) => {
  return (
    <div
      className="flex-1 overflow-y-auto flex justify-center"
      style={{
        background: chatLayoutStyles.messageAreaBackground,
      }}
    >
      <div className="w-[800px]">
        <MessageList
          messages={messages}
          messagesContainerRef={messagesContainerRef}
          messagesEndRef={messagesEndRef}
          onShowMap={onShowMap}
          extractedLocations={extractedLocations}
        />
        <ScrollToBottomButton
          isVisible={showScrollButton}
          onClick={scrollToBottom}
        />
      </div>
    </div>
  );
};

export default MessageArea;
