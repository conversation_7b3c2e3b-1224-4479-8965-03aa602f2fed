/** @format */

import { getMessageStyles } from '@/app/chat/styles';
import { Message, TopCandidates } from '@/app/chat/types';
import { colors } from '@/app/colors';
import { LocationHoverCard } from '@/app/shared/cards';
import React, { useEffect, useRef, useState } from 'react';
import MessageActions from './MessageActions';
import MessageLoading from './MessageLoading';

interface MessageItemProps {
	message: Message;
	onShowMap: () => void;
	extractedLocations?: TopCandidates | null;
}

const MessageItem: React.FC<MessageItemProps> = ({
	message,
	onShowMap,
	extractedLocations,
}) => {
	const messageRef = useRef<HTMLDivElement>(null);
	const [hoveredLocation, setHoveredLocation] = useState<{
		id: string;
		name: string;
		lat: number;
		lng: number;
		confidence?: number;
		address?: string;
		walk_route_distance_m?: number;
	} | null>(null);
	const [hoverPosition, setHoverPosition] = useState({ x: 0, y: 0 });
	const [isHoveringLocation, setIsHoveringLocation] = useState(false);
	const [isHoveringCard, setIsHoveringCard] = useState(false);
	const hoverTimeout = useRef<NodeJS.Timeout | null>(null);
	const hideTimeout = useRef<NodeJS.Timeout | null>(null);

	// Handle hover events on location elements
	useEffect(() => {
		const handleLocationHover = (event: Event) => {
			const mouseEvent = event as MouseEvent;
			const target = mouseEvent.target as HTMLElement;
			const locationElement = target.closest(
				'[data-location-id][data-lat][data-lng]'
			) as HTMLElement;

			if (locationElement && event.type === 'mouseenter') {
				// Clear any existing timeouts
				if (hoverTimeout.current) {
					clearTimeout(hoverTimeout.current);
					hoverTimeout.current = null;
				}
				if (hideTimeout.current) {
					clearTimeout(hideTimeout.current);
					hideTimeout.current = null;
				}

				const id = locationElement.getAttribute('data-location-id') || '';
				const lat = parseFloat(locationElement.getAttribute('data-lat') || '0');
				const lng = parseFloat(locationElement.getAttribute('data-lng') || '0');
				const name = locationElement.textContent || 'Location';

				// Try to find matching location data from extractedLocations
				let locationData = null;
				if (extractedLocations && Array.isArray(extractedLocations)) {
					locationData = extractedLocations.find(
						(loc: Record<string, unknown>) =>
							loc.name === name ||
							(Math.abs(((loc.lat || loc.latitude || 0) as number) - lat) <
								0.0001 &&
								Math.abs(((loc.lng || loc.longitude || 0) as number) - lng) <
									0.0001)
					);
				}

				// Calculate optimal card position to avoid viewport edges
				const calculateCardPosition = (mouseX: number, mouseY: number) => {
					const cardWidth = 280; // Approximate card width
					const cardHeight = 300; // Approximate card height
					const horizontalOffset = 15; // Horizontal offset from mouse
					const padding = 20; // Padding from viewport edges

					// Start with mouse position, offset horizontally and center vertically
					let x = mouseX + horizontalOffset;
					let y = mouseY - cardHeight / 2; // Center vertically on mouse

					// Adjust horizontal position if card would go off-screen
					if (x + cardWidth > window.innerWidth - padding) {
						x = mouseX - cardWidth - horizontalOffset; // Show on left side of mouse
					}

					// Adjust vertical position if card would go off-screen
					if (y + cardHeight > window.innerHeight - padding) {
						y = window.innerHeight - cardHeight - padding;
					}
					if (y < padding) {
						y = padding;
					}

					// Ensure card doesn't go off-screen on the left
					x = Math.max(padding, x);

					return { x, y };
				};

				const optimalPosition = calculateCardPosition(
					mouseEvent.clientX,
					mouseEvent.clientY
				);

				// Show card immediately on hover
				setHoveredLocation({
					id,
					name,
					lat,
					lng,
					confidence: locationData?.confidence,
					address: locationData?.address,
					walk_route_distance_m: locationData?.walk_route_distance_m,
				});
				setHoverPosition(optimalPosition);
				setIsHoveringLocation(true);
			} else if (event.type === 'mouseleave') {
				setIsHoveringLocation(false);
			}
		};

		// Helper to attach listeners to all elements with data-location-id, data-lat, data-lng
		const attachListeners = () => {
			const messageElement = messageRef.current;
			if (!messageElement) return;
			// Select all elements (including <strong>) with the attributes
			const locationElements = messageElement.querySelectorAll(
				'[data-location-id][data-lat][data-lng]'
			);
			locationElements.forEach((element) => {
				element.addEventListener('mouseenter', handleLocationHover);
				element.addEventListener('mouseleave', handleLocationHover);
			});
			return () => {
				locationElements.forEach((element) => {
					element.removeEventListener('mouseenter', handleLocationHover);
					element.removeEventListener('mouseleave', handleLocationHover);
				});
			};
		};

		// Attach listeners after DOM update
		let cleanup: (() => void) | undefined;
		const observer = new MutationObserver(() => {
			if (cleanup) cleanup();
			cleanup = attachListeners();
		});
		if (messageRef.current) {
			observer.observe(messageRef.current, { childList: true, subtree: true });
			// Initial attach
			cleanup = attachListeners();
		}
		return () => {
			observer.disconnect();
			if (cleanup) cleanup();
		};
	}, [message.text, extractedLocations]);

	// Hide card when neither location nor card is being hovered
	useEffect(() => {
		// Clear any existing timeout first
		if (hideTimeout.current) {
			clearTimeout(hideTimeout.current);
			hideTimeout.current = null;
		}

		// If we have a card visible and neither location nor card is being hovered
		if (hoveredLocation && !isHoveringLocation && !isHoveringCard) {
			hideTimeout.current = setTimeout(() => {
				setHoveredLocation(null);
			}, 1000); // 1 second delay
		}

		// Cleanup function
		return () => {
			if (hideTimeout.current) {
				clearTimeout(hideTimeout.current);
				hideTimeout.current = null;
			}
		};
	}, [isHoveringLocation, isHoveringCard, hoveredLocation]);

	// Cleanup timeouts on unmount
	useEffect(() => {
		return () => {
			if (hoverTimeout.current) clearTimeout(hoverTimeout.current);
			if (hideTimeout.current) clearTimeout(hideTimeout.current);
		};
	}, []);

	// Handle clicks on location elements
	useEffect(() => {
		const handleLocationClick = (event: MouseEvent) => {
			const target = event.target as HTMLElement;

			// First check for elements with data-lat and data-lng attributes (new format)
			const locationElementWithCoords = target.closest(
				'[data-lat][data-lng]'
			) as HTMLElement;

			if (locationElementWithCoords) {
				event.preventDefault();
				const lat = locationElementWithCoords.getAttribute('data-lat');
				const lng = locationElementWithCoords.getAttribute('data-lng');
				const locationName =
					locationElementWithCoords.textContent || 'Location';

				console.log(
					'🗺️ Location clicked in chat (with coordinates):',
					locationName,
					{ lat, lng }
				);

				if (lat && lng) {
					// Use coordinates directly from data attributes for accuracy
					const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
					console.log(
						'🎯 Opening Google Maps with coordinates for:',
						locationName
					);
					window.open(mapsUrl, '_blank');
					return;
				}
			}

			// Fallback to old format for backward compatibility
			const locationElement = target.closest(
				'[data-location-id]'
			) as HTMLElement;

			if (locationElement) {
				event.preventDefault();
				const locationName = locationElement.textContent || 'Location';

				console.log(
					'🗺️ Location clicked in chat (legacy format):',
					locationName
				);

				// Only open Google Maps if we have coordinates from extracted locations
				const matchingLocation = extractedLocations?.locations?.find(
					(loc: Record<string, unknown>) =>
						loc.name &&
						locationName
							.toLowerCase()
							.includes((loc.name as string).toLowerCase())
				);

				if (
					matchingLocation &&
					matchingLocation.latitude &&
					matchingLocation.longitude
				) {
					// Use coordinates for accuracy
					const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${matchingLocation.latitude},${matchingLocation.longitude}`;
					console.log(
						'🎯 Opening Google Maps with coordinates for:',
						matchingLocation.name
					);
					window.open(mapsUrl, '_blank');
				} else {
					console.log(
						'📍 No coordinates available for location:',
						locationName
					);
				}
			}
		};

		const messageElement = messageRef.current;
		if (messageElement) {
			messageElement.addEventListener('click', handleLocationClick);
			return () => {
				messageElement.removeEventListener('click', handleLocationClick);
			};
		}
	}, [message.text]);

	if (message.isLoading) {
		return <MessageLoading />;
	}

	return (
		<>
			<div
				className={`flex ${
					message.isUser ? 'justify-end' : 'justify-start'
				} mb-4`}>
				<div
					ref={messageRef}
					className='p-4 rounded-2xl max-w-xl'
					style={getMessageStyles(message.isUser)}>
					<div
						className='whitespace-pre-wrap break-words location-message-content'
						dangerouslySetInnerHTML={{ __html: message.text }}
						style={
							{
								// Add styles for clickable location elements
								'--location-hover-color': colors.brand.blue,
								'--location-hover-bg': colors.ui.blue50,
							} as React.CSSProperties
						}
					/>

					{/* Add CSS for location styling */}
					<style jsx>{`
						.location-message-content
							:global([data-location-id][data-lat][data-lng]) {
							color: ${colors.brand.blue};
							font-weight: 600;
							text-decoration: underline;
							text-decoration-color: ${colors.ui.blue200};
							cursor: pointer;
							transition: background-color 0.2s ease, color 0.2s ease;
							padding: 2px 4px;
							border-radius: 4px;
							position: relative;
							background-color: ${colors.ui.blue50};
							border: 1px solid transparent;
						}
						/* Subtle hover effect for location elements */
						.location-message-content
							:global([data-location-id][data-lat][data-lng]:hover) {
							background-color: ${colors.ui.blue100};
							border-color: ${colors.ui.blue200};
							color: ${colors.brand.navy};
						}
						.location-message-content
							:global([data-location-id][data-lat][data-lng]::before) {
							content: '📍';
							margin-right: 2px;
							font-size: 0.8em;
						}
					`}</style>
					{!message.isUser && (
						<MessageActions
							messageText={message.text}
							onShowMap={onShowMap}
						/>
					)}
				</div>
			</div>

			{/* Location Hover Card */}
			{hoveredLocation && (
				<LocationHoverCard
					location={hoveredLocation}
					isVisible={!!hoveredLocation}
					position={hoverPosition}
					onClose={() => setHoveredLocation(null)}
					// Pass hover state handlers to card
					onCardHoverChange={(hovering) => {
						setIsHoveringCard(hovering);
					}}
				/>
			)}
		</>
	);
};

export default MessageItem;
