import React from 'react';
import Image from 'next/image';
import { FaBars } from 'react-icons/fa';
import { colors } from 'app/colors';
import { IconButton } from 'app/chat/components/ui';

interface SidebarHeaderProps {
  onClose: () => void;
}

const SidebarHeader: React.FC<SidebarHeaderProps> = ({ onClose }) => {
  return (
    <div
      className="w-full h-[60px] p-4 flex justify-between items-center border-b"
      style={{ borderColor: colors.ui.green200 }}
    >
      <div className="flex items-center">
        <div className="w-10 h-10 relative">
          <Image
            src="/logo/512x512.png"
            alt="Logo"
            width={40}
            height={40}
            className="rounded-lg"
          />
        </div>
      </div>
      <IconButton
        onClick={onClose}
        icon={<FaBars />}
        variant="default"
      />
    </div>
  );
};

export default SidebarHeader;
