/** @format */

import { CreditsDisplay } from '@/app/shared/credits';
import { createHoverEffect, zIndexLayers } from 'app/chat/styles';
import { TopBarProps } from 'app/chat/types';
import { colors } from 'app/colors';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useRef, useState } from 'react';
import { FaBars, FaGlobe, FaMapMarkerAlt } from 'react-icons/fa';
import UserDropdown from './UserDropdown';

const TopBar: React.FC<TopBarProps> = ({
	isLeftOpen,
	isRightOpen,
	setIsLeftOpen,
	setIsRightOpen,
	startNewChat,
	userLocation,
	locationError,
	locationLoading,
	requestAutoLocation,
}) => {
	const [dropdownOpen, setDropdownOpen] = useState(false);
	const { data: session } = useSession();
	const router = useRouter();
	const buttonRef = useRef<HTMLDivElement>(null);

	const getUserInitials = () => {
		if (session?.user?.username) {
			return session.user.username.substring(0, 2).toUpperCase();
		}
		if (session?.user?.email) {
			return session.user.email.substring(0, 2).toUpperCase();
		}
		return 'U';
	};

	return (
		<div
			className={`h-[60px] px-4 md:px-8 flex items-center justify-between border-b relative ${zIndexLayers.topBar} shadow-sm`}
			style={{
				background: `linear-gradient(135deg, #F0FAFF 0%, #F5FDF8 100%)`,
				borderColor: colors.ui.gray200,
			}}>
			<div className='flex items-center gap-3'>
				{/* Logo - only show when left sidebar is closed */}
				{!isLeftOpen && (
					<div
						className='flex items-center cursor-pointer h-full flex-shrink-0'
						onClick={() => router.push('/')}>
						<div className='flex items-center justify-center w-10 h-10 rounded-xl'>
							<Image
								src='/logo/512x512.png'
								alt='Wizlop Logo'
								width={40}
								height={40}
								className='object-contain'
								priority
							/>
						</div>
					</div>
				)}

				{!isLeftOpen && (
					<button
						onClick={() => setIsLeftOpen(true)}
						className='p-2 rounded-lg transition-colors'
						style={{ color: colors.neutral.slateGray }}
						{...createHoverEffect(
							colors.neutral.slateGray,
							colors.brand.green,
							'transparent',
							colors.ui.green100
						)}>
						<FaBars />
					</button>
				)}

				{/* New Chat button - only show when left sidebar is closed */}
				{!isLeftOpen && (
					<button
						onClick={startNewChat}
						className='px-3 py-1.5 text-sm rounded-lg font-medium transition-colors'
						style={{
							backgroundColor: colors.brand.blue,
							color: 'white',
						}}
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor =
								colors.supporting.lightBlue;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.brand.blue;
						}}>
						New Chat
					</button>
				)}
			</div>

			{/* Center section - can be used for additional navigation if needed */}
			<div className='flex-1 flex justify-center'>
				{/* Space for future navigation elements */}
			</div>

			<div className='flex items-center gap-3'>
				{/* Credits Display */}
				<CreditsDisplay
					size='small'
					showAddButton={false}
				/>

				{/* Globe Navigation - matching global nav style */}
				<button
					onClick={() => router.push('/globe')}
					className='flex items-center justify-center w-10 h-10 rounded-xl transition-colors'
					style={{
						backgroundColor: colors.ui.green100,
						color: colors.brand.green,
					}}
					title='Interactive Globe'
					onMouseEnter={(e) =>
						(e.currentTarget.style.backgroundColor = colors.ui.green200)
					}
					onMouseLeave={(e) =>
						(e.currentTarget.style.backgroundColor = colors.ui.green100)
					}>
					<FaGlobe className='w-4 h-4' />
				</button>

				{/* POI Navigation - moved to left side */}
				<button
					onClick={() => router.push('/pois')}
					className='flex items-center justify-center w-10 h-10 rounded-xl transition-colors'
					style={{
						backgroundColor: colors.ui.gray100,
						color: colors.supporting.teal,
					}}
					title='Points of Interest'
					onMouseEnter={(e) =>
						(e.currentTarget.style.backgroundColor = colors.ui.gray200)
					}
					onMouseLeave={(e) =>
						(e.currentTarget.style.backgroundColor = colors.ui.gray100)
					}>
					<FaMapMarkerAlt className='w-4 h-4' />
				</button>

				{/* Map Panel Toggle - opens right panel with map - moved to right side */}
				{!isRightOpen && (
					<button
						onClick={() => setIsRightOpen(true)}
						className='flex items-center justify-center w-10 h-10 rounded-xl transition-colors'
						style={{
							backgroundColor: colors.ui.blue100,
							color: colors.brand.blue,
						}}
						title='Open Map Panel'
						onMouseEnter={(e) =>
							(e.currentTarget.style.backgroundColor = colors.ui.blue200)
						}
						onMouseLeave={(e) =>
							(e.currentTarget.style.backgroundColor = colors.ui.blue100)
						}>
						<FaBars className='w-4 h-4' />
					</button>
				)}

				{/* User Dropdown */}
				<div className='relative'>
					<div
						ref={buttonRef}
						onClick={() => setDropdownOpen((prev) => !prev)}
						className='w-10 h-10 bg-gradient-to-br from-wizlop-500 to-wizlop-600 text-white rounded-xl flex items-center justify-center cursor-pointer select-none hover:from-wizlop-600 hover:to-wizlop-700 transition-all duration-300 shadow-lg'>
						{getUserInitials()}
					</div>

					<UserDropdown
						isOpen={dropdownOpen}
						onClose={() => setDropdownOpen(false)}
						buttonRef={buttonRef}
						userLocation={userLocation}
						locationError={locationError}
						locationLoading={locationLoading}
						requestAutoLocation={requestAutoLocation}
					/>
				</div>
			</div>
		</div>
	);
};

export default TopBar;
