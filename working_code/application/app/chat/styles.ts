/** @format */

import { colors } from 'app/colors';

// ===== BUTTON STYLES =====

export const buttonVariants = {
	primary: {
		backgroundColor: colors.brand.blue,
		color: 'white',
		hoverColor: colors.supporting.lightBlue,
	},
	secondary: {
		backgroundColor: colors.neutral.slateGray,
		color: 'white',
		hoverColor: colors.neutral.textBlack,
	},
	danger: {
		backgroundColor: colors.utility.error,
		color: 'white',
		hoverColor: '#dc2626',
	},
	ghost: {
		backgroundColor: 'transparent',
		color: colors.neutral.slateGray,
		hoverColor: colors.brand.green,
		hoverBg: colors.ui.green100,
	},
};

export const buttonSizes = {
	sm: 'px-3 py-1.5 text-sm',
	md: 'px-4 py-2 text-base',
	lg: 'px-6 py-3 text-lg',
};

export const iconButtonVariants = {
	default: {
		color: colors.neutral.slateGray,
		hoverColor: colors.brand.green,
		backgroundColor: 'transparent',
		hoverBg: colors.ui.green100,
	},
	ghost: {
		color: colors.ui.gray400,
		hoverColor: colors.supporting.lightBlue,
		backgroundColor: 'transparent',
	},
	danger: {
		color: colors.utility.error,
		hoverColor: '#dc2626',
		backgroundColor: 'transparent',
	},
};

export const iconButtonSizes = {
	sm: 'p-1',
	md: 'p-2',
	lg: 'p-3',
};

// ===== MODAL STYLES =====

export const modalOverlayStyles = {
	base: 'fixed inset-0 bg-black/50 z-40',
	mobile: 'sm:hidden',
};

export const sidebarStyles = {
	left: {
		borderColor: colors.ui.green200,
		backgroundColor: colors.ui.green50,
		boxShadow: `0 4px 6px -1px ${colors.ui.green200}60`,
	},
	right: {
		background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.ui.blue50} 100%)`,
		borderLeft: `2px solid ${colors.ui.blue200}`,
		boxShadow: `-4px 0 6px -1px ${colors.ui.blue200}60`,
	},
};

export const locationModalStyles = {
	backdrop:
		'absolute inset-0 z-50 flex items-center justify-center backdrop-blur-sm bg-black/30',
	container: {
		backgroundColor: `${colors.neutral.cloudWhite}F8`,
		border: `1px solid ${colors.ui.gray200}`,
	},
	errorPanel: {
		backgroundColor: `${colors.utility.error}20`,
		border: `1px solid ${colors.utility.error}40`,
	},
};

// ===== COLOR UTILITIES =====

export const getMessageStyles = (isUser: boolean) => {
	return isUser
		? {
				backgroundColor: colors.brand.blue,
				color: 'white',
		  }
		: {
				backgroundColor: colors.neutral.cloudWhite,
				border: `1px solid ${colors.ui.blue200}`,
				boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
				color: colors.neutral.textBlack,
		  };
};

export const getSessionItemStyles = (isActive: boolean) => {
	return isActive
		? {
				backgroundColor: colors.brand.green,
				color: 'white',
		  }
		: {
				backgroundColor: 'transparent',
				color: colors.brand.blue,
		  };
};

export const getLoadingDotColor = () => colors.supporting.lightBlue;

export const getGradientBackground = () =>
	`linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`;

export const getScrollButtonStyles = () => ({
	backgroundColor: colors.brand.blue,
	hoverColor: colors.supporting.lightBlue,
});

// ===== LAYOUT STYLES =====

export const chatLayoutStyles = {
	background: `linear-gradient(135deg,
    rgba(51, 194, 255, 0.08) 0%,
    rgba(128, 237, 153, 0.06) 25%,
    rgba(102, 208, 255, 0.05) 50%,
    rgba(163, 247, 181, 0.07) 75%,
    rgba(26, 169, 154, 0.04) 100%
  ), #FEFEFE`,
	messageAreaBackground: `linear-gradient(135deg,
    rgba(51, 194, 255, 0.04) 0%,
    rgba(128, 237, 153, 0.03) 50%,
    rgba(26, 169, 154, 0.02) 100%
  ), rgba(255, 255, 255, 0.8)`,
};

// ===== TOPBAR STYLES =====

export const topBarStyles = {
	background: colors.neutral.cloudWhite,
	borderBottom: `1px solid ${colors.ui.gray200}`,
	logoGradient: `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.brand.blue} 100%)`,
};

// ===== ANIMATION STYLES =====

export const transitionStyles = {
	default: 'transition-all duration-300',
	slow: 'transition-all duration-500',
	fast: 'transition-all duration-150',
	globe: 'transition-all duration-700 ease-in-out',
	bottomBar: 'transition-all duration-1500 ease-in-out',
};

// ===== HOVER EFFECTS =====

export const createHoverEffect = (
	defaultColor: string,
	hoverColor: string,
	defaultBg?: string,
	hoverBg?: string
) => ({
	onMouseEnter: (e: React.MouseEvent<HTMLElement>) => {
		e.currentTarget.style.color = hoverColor;
		if (hoverBg) {
			e.currentTarget.style.backgroundColor = hoverBg;
		}
	},
	onMouseLeave: (e: React.MouseEvent<HTMLElement>) => {
		e.currentTarget.style.color = defaultColor;
		if (defaultBg !== undefined) {
			e.currentTarget.style.backgroundColor = defaultBg;
		}
	},
});

// ===== RESPONSIVE STYLES =====

export const responsiveStyles = {
	sidebar: {
		mobile: 'fixed sm:relative inset-0 sm:inset-auto w-full h-full sm:h-auto',
		desktop: 'sm:w-[280px]',
	},
	messageArea: {
		container: 'w-[800px]',
		maxWidth: 'max-w-[800px]',
	},
};

// ===== Z-INDEX LAYERS =====
// Proper layering hierarchy to prevent conflicts

export const zIndexLayers = {
	// Background layers (lowest)
	background: 'z-0', // Page background
	globeBackground: 'z-[1]', // Background globe when not transitioning

	// Content layers
	content: 'z-10', // Main content area
	messageArea: 'z-10', // Message area

	// UI element layers
	topBar: 'z-20', // Top navigation bar
	bottomBar: 'z-20', // Bottom input bar

	// Interactive element layers
	dropdown: 'z-30', // Dropdown menus within components
	tooltip: 'z-35', // Tooltips and small overlays

	// Modal and overlay layers
	modal: 'z-40', // Modal backgrounds and overlays
	modalContent: 'z-41', // Modal content

	// Sidebar layers (higher than modals for mobile overlay)
	sidebar: 'z-50', // Left and right sidebars
	sidebarDropdown: 'z-51', // Dropdowns within sidebars

	// Transition layers (need to be below top-level UI)
	globeTransition: 'z-[80]', // Globe during transition animation (lowered to not cover dropdowns)

	// Top-level UI layers (highest interactive elements)
	userDropdown: 'z-[9999]', // User dropdown in top bar (maximum priority)
	notification: 'z-[95]', // Notifications and alerts

	// System layers (absolute highest)
	loading: 'z-[999]', // Loading overlays (system level)
};

// ===== DROPDOWN STYLES =====

export const dropdownStyles = {
	userDropdown: {
		position: 'fixed' as const,
		isolation: 'isolate' as const,
		willChange: 'transform, opacity' as const,
		backfaceVisibility: 'hidden' as const,
		perspective: '1000px' as const,
	},
};
