/** @format */

'use client';

import { colors } from '@/app/colors';
import { useCreditsData } from '@/app/shared/credits';
import { useProfileData } from '@/app/shared/profile';
import { useUserInteractionsContext } from '@/app/shared/userInteractions/shared/context/UserInteractionsProvider';
import { useSession } from 'next-auth/react';
import React, { useEffect, useState } from 'react';
import { FiCamera, FiLoader } from 'react-icons/fi';
import { ProfilePictureUpload } from './ProfilePictureUpload';

interface User {
	id: string;
	email: string;
	username: string;
	name?: string | null;
	image?: string | null;
	profile_picture_url?: string | null;
	role: 'user' | 'agent' | 'superuser';
	permissions: string[];
}

interface UserProfile {
	id: string;
	user_id: string;
	name: string;
	age: number;
	avatar_url?: string;
	profile_completed: boolean;
	allow_data_usage: boolean;
	preferences: Record<string, unknown>;
	timezone: string;
	total_locations_visited: number;
	total_reviews_written: number;
	total_photos_uploaded: number;
	adventurer_level: number;
	days_active: number;
	last_activity_date?: string;
	created_at: string;
	updated_at: string;
}

interface EnhancedProfileHeaderProps {
	user?: User;
	onProfileUpdate?: (updatedProfile: Partial<UserProfile>) => void;
	integrated?: boolean;
}

export const EnhancedProfileHeader: React.FC<EnhancedProfileHeaderProps> = ({
	user,
	onProfileUpdate,
	integrated = false,
}) => {
	const { data: session, update: updateSession } = useSession();
	const [showProfilePictureUpload, setShowProfilePictureUpload] =
		useState(false);

	// Use session user if no user prop provided
	const currentUser = user || session?.user;

	const { loading: profileLoading, loadProfile } = useProfileData({
		userId: currentUser?.id,
		autoLoad: true,
	});

	const { totalCredits, loading: creditsLoading } = useCreditsData({
		userId: currentUser?.id,
		autoLoad: true,
	});

	// Use the shared interaction context to get stats
	const { likes, favorites, visits, reviews, lazyLoadInteraction } =
		useUserInteractionsContext();

	// Load interaction data when component mounts
	useEffect(() => {
		if (currentUser?.id) {
			lazyLoadInteraction('likes');
			lazyLoadInteraction('favorites');
			lazyLoadInteraction('visits');
			lazyLoadInteraction('reviews');
		}
	}, [currentUser?.id, lazyLoadInteraction]);

	const interactionStatsLoading =
		likes.loading || favorites.loading || visits.loading || reviews.loading;

	if (profileLoading || creditsLoading || interactionStatsLoading) {
		return (
			<div
				className={integrated ? 'py-8' : 'bg-white rounded-2xl shadow-lg p-8'}>
				<div className='flex items-center justify-center py-12'>
					<FiLoader
						className='w-12 h-12 animate-spin'
						style={{ color: colors.brand.blue }}
					/>
				</div>
			</div>
		);
	}

	if (integrated) {
		// Integrated mode - beautiful modern layout
		return (
			<>
				{/* Modern Avatar Section */}
				<div className='relative'>
					<div className='relative group'>
						<img
							src={
								currentUser?.image ||
								`https://ui-avatars.com/api/?name=${encodeURIComponent(
									currentUser?.name || 'User'
								)}&background=${colors.brand.blue.replace('#', '')}&color=fff`
							}
							alt={currentUser?.name || 'User'}
							className='w-32 h-32 rounded-2xl border-4 border-white shadow-xl object-cover transition-all duration-300 group-hover:scale-105 group-hover:shadow-2xl'
						/>

						{/* Elegant Avatar Upload Button */}
						<button
							onClick={() => setShowProfilePictureUpload(true)}
							className='absolute -bottom-2 -right-2 rounded-xl p-3 shadow-lg cursor-pointer transition-all duration-300 hover:scale-110 hover:shadow-xl'
							style={{ backgroundColor: colors.brand.blue }}>
							<FiCamera className='w-4 h-4 text-white' />
						</button>
					</div>
				</div>

				{/* User Info and Actions */}
				<div className='flex flex-col gap-6 mt-8'>
					{/* Name and User Info with Clear Glass Effect */}
					<div className='relative'>
						{/* Subtle glass background */}
						<div className='absolute inset-0 bg-black/10 rounded-2xl border border-white/20'></div>

						{/* Content */}
						<div className='relative p-6'>
							<h1 className='text-3xl font-bold mb-6 text-white drop-shadow-lg'>
								{currentUser?.name || 'Anonymous User'}
							</h1>

							{/* User Details in elegant grid */}
							<div className='grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm'>
								{(currentUser as any)?.username && (
									<div className='flex flex-col space-y-1'>
										<span className='text-white/80 text-xs uppercase tracking-wider font-medium'>
											Username
										</span>
										<span className='text-white font-semibold text-base'>
											@{(currentUser as any).username}
										</span>
									</div>
								)}
								{currentUser?.email && (
									<div className='flex flex-col space-y-1'>
										<span className='text-white/80 text-xs uppercase tracking-wider font-medium'>
											Email
										</span>
										<span className='text-white font-semibold text-base'>
											{currentUser.email}
										</span>
									</div>
								)}
								{(currentUser as any)?.age && (
									<div className='flex flex-col space-y-1'>
										<span className='text-white/80 text-xs uppercase tracking-wider font-medium'>
											Age
										</span>
										<span className='text-white font-semibold text-base'>
											{(currentUser as any).age} years old
										</span>
									</div>
								)}
							</div>
						</div>
					</div>
				</div>

				{/* Enhanced Profile Picture Upload Modal */}
				{showProfilePictureUpload && (
					<div className='fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50'>
						<div className='bg-white rounded-3xl shadow-2xl p-8 max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto'>
							<div className='flex items-center justify-between mb-6'>
								<h3 className='text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-cyan-600 bg-clip-text text-transparent'>
									Update Profile Picture
								</h3>
								<button
									onClick={() => setShowProfilePictureUpload(false)}
									className='w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors'>
									<span className='text-gray-600 text-xl'>✕</span>
								</button>
							</div>

							<ProfilePictureUpload
								currentProfilePicture={currentUser?.image || undefined}
								onUploadComplete={async (result) => {
									console.log('Profile picture uploaded:', result);
									onProfileUpdate?.({ avatar_url: result.url });
									setShowProfilePictureUpload(false);
									await updateSession();
									await loadProfile();
								}}
								onUploadError={(error) => {
									console.error('Profile picture upload error:', error);
									alert('Failed to upload profile picture: ' + error);
								}}
								size='large'
								showHistory={true}
								allowCrop={true}
							/>
						</div>
					</div>
				)}
			</>
		);
	}

	// Standalone mode
	return (
		<div
			className='bg-white rounded-2xl shadow-lg border overflow-hidden'
			style={{ borderColor: colors.ui.gray200 }}>
			{/* Clean Cover Background */}
			<div
				className='relative h-32'
				style={{
					background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
				}}>
				{/* Subtle Pattern */}
				<div className='absolute inset-0 opacity-10'>
					<div
						className='absolute inset-0'
						style={{
							backgroundImage: `radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3) 0%, transparent 50%)`,
							backgroundSize: '60px 60px',
						}}></div>
				</div>
			</div>

			{/* Clean Profile Content */}
			<div className='relative p-6'>
				{/* Profile Header Row */}
				<div className='flex items-start -mt-12 mb-6'>
					{/* Clean Avatar Section */}
					<div className='relative'>
						<div className='relative group'>
							<img
								src={
									currentUser?.image ||
									`https://ui-avatars.com/api/?name=${encodeURIComponent(
										currentUser?.name || 'User'
									)}&background=${colors.brand.blue.replace('#', '')}&color=fff`
								}
								alt={currentUser?.name || 'User'}
								className='w-24 h-24 rounded-2xl border-4 border-white shadow-lg object-cover transition-transform duration-200 group-hover:scale-105'
							/>

							{/* Clean Avatar Upload Button */}
							<button
								onClick={() => setShowProfilePictureUpload(true)}
								className='absolute -bottom-1 -right-1 rounded-xl p-2 shadow-lg cursor-pointer transition-all duration-200 hover:scale-110'
								style={{ backgroundColor: colors.brand.blue }}>
								<FiCamera className='w-4 h-4 text-white' />
							</button>
						</div>
					</div>
				</div>

				{/* User Info */}
				<div>
					<h1
						className='text-2xl font-bold mb-2'
						style={{ color: colors.neutral.textBlack }}>
						{currentUser?.name || 'Anonymous User'}
					</h1>

					{/* Credits Badge */}
					{totalCredits > 0 && (
						<div
							className='inline-flex items-center gap-2 px-3 py-1 rounded-lg mb-4'
							style={{ backgroundColor: colors.ui.blue50 }}>
							<span
								className='text-sm font-medium'
								style={{ color: colors.brand.blue }}>
								{totalCredits} Credits
							</span>
						</div>
					)}
				</div>
			</div>

			{/* Enhanced Profile Picture Upload Modal */}
			{showProfilePictureUpload && (
				<div className='fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50'>
					<div className='bg-white rounded-3xl shadow-2xl p-8 max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto'>
						<div className='flex items-center justify-between mb-6'>
							<h3 className='text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-cyan-600 bg-clip-text text-transparent'>
								Update Profile Picture
							</h3>
							<button
								onClick={() => setShowProfilePictureUpload(false)}
								className='w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors'>
								<span className='text-gray-600 text-xl'>✕</span>
							</button>
						</div>

						<ProfilePictureUpload
							currentProfilePicture={currentUser?.image || undefined}
							onUploadComplete={async (result) => {
								console.log('Profile picture uploaded:', result);
								onProfileUpdate?.({ avatar_url: result.url });
								setShowProfilePictureUpload(false);
								await updateSession();
								await loadProfile();
							}}
							onUploadError={(error) => {
								console.error('Profile picture upload error:', error);
								alert('Failed to upload profile picture: ' + error);
							}}
							size='large'
							showHistory={true}
							allowCrop={true}
						/>
					</div>
				</div>
			)}
		</div>
	);
};
