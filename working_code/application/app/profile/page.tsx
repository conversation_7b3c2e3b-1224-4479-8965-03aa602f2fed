'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { colors } from '@/app/colors'
import { LoadingSpinner } from '@/app/shared/system'
import { ProfilePageComponent } from './components'

export default function ProfilePageModule() {
  const { status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/')
    }
  }, [status, router])

  if (status === 'loading') {
    return <LoadingSpinner text="Loading profile..." />
  }

  if (status === 'unauthenticated') {
    return null // Will redirect to home
  }

  return (
    <div
      className="min-h-screen"
      style={{
        background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`
      }}
    >
      <ProfilePageComponent />
    </div>
  )
}
