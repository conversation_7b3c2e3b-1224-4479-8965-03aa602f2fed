/** @format */

'use client';

import { colors } from '@/app/colors';
import { useCreditsData } from '@/app/shared/credits';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FaComments, FaGlobe } from 'react-icons/fa';
import {
	FiArrowLeft,
	FiCheck,
	FiEdit3,
	FiGift,
	FiMapPin,
	FiMessageCircle,
	FiRepeat,
	FiShoppingCart,
	FiStar,
	FiZap,
} from 'react-icons/fi';

export default function CreditsPage() {
	const router = useRouter();
	const { data: session } = useSession();
	const {
		credits,
		availableCredits,
		subscriptionStatus,
		loading,
		error,
		earnCredits,
		purchaseCredits,
	} = useCreditsData({
		userId: session?.user.id,
		autoLoad: true,
	});

	const [selectedPlan, setSelectedPlan] = useState<
		'subscription' | 'purchase' | null
	>(null);
	const [processing, setProcessing] = useState(false);

	// Handle earning credits (demo functionality)
	const handleEarnCredits = async (method: {
		icon: React.ComponentType;
		title: string;
		description: string;
		credits: number;
		color: string;
	}) => {
		if (!session?.user.id) {
			alert('Please sign in to earn credits');
			return;
		}

		setProcessing(true);
		try {
			const success = await earnCredits(method.credits, method.title);
			if (success) {
				alert(
					`Congratulations! You earned ${method.credits} credits for ${method.title}!`
				);
			}
		} catch (error) {
			console.error('Error earning credits:', error);
		} finally {
			setProcessing(false);
		}
	};

	// Handle purchasing credits (demo functionality)
	const handlePurchaseCredits = async (plan: {
		type: 'subscription' | 'purchase';
		name: string;
		price: string;
		credits: string;
		icon: React.ComponentType;
		features: string[];
		popular: boolean;
	}) => {
		if (!session?.user.id) {
			alert('Please sign in to purchase credits');
			return;
		}

		setProcessing(true);
		try {
			if (plan.type === 'subscription') {
				// Demo subscription purchase
				const expiryDate = new Date();
				expiryDate.setMonth(expiryDate.getMonth() + 1);

				const success = await purchaseCredits(
					1000, // Give 1000 credits for subscription
					'premium',
					expiryDate.toISOString()
				);

				if (success) {
					alert(
						'Subscription activated! You now have unlimited credits for 1 month.'
					);
				}
			} else {
				// Demo credit pack purchase
				const creditAmounts = [25, 75, 150, 250];
				const randomAmount =
					creditAmounts[Math.floor(Math.random() * creditAmounts.length)];

				const success = await purchaseCredits(randomAmount);
				if (success) {
					alert(`Successfully purchased ${randomAmount} credits!`);
				}
			}
		} catch (error) {
			console.error('Error purchasing credits:', error);
		} finally {
			setProcessing(false);
		}
	};

	const earnMethods = [
		{
			icon: FiMapPin,
			title: 'Add New Locations',
			description: 'Submit new POIs (5 credits after approval)',
			credits: 5,
			color: colors.brand.blue,
		},
		{
			icon: FiEdit3,
			title: 'Update POI Information',
			description: 'Improve existing location data (5 credits after approval)',
			credits: 5,
			color: colors.brand.green,
		},
		{
			icon: FiStar,
			title: 'Like Locations',
			description: 'Like locations (no credits - for engagement only)',
			credits: 0,
			color: colors.supporting.mintGreen,
		},
		{
			icon: FiMessageCircle,
			title: 'Write Comments',
			description: 'Add helpful comments (no credits - for engagement only)',
			credits: 0,
			color: colors.supporting.lightBlue,
		},
	];

	const plans = [
		{
			type: 'subscription' as const,
			name: 'Unlimited Explorer',
			price: '$9.99/month',
			credits: 'Unlimited',
			icon: FiRepeat,
			features: [
				'Unlimited requests (no credit limits)',
				'Priority response times',
				'Advanced location insights',
				'Exclusive travel recommendations',
				'Early access to new features',
			],
			popular: true,
		},
		{
			type: 'purchase' as const,
			name: 'Credit Packs',
			price: 'From $2.99',
			credits: '25-250 Credits',
			icon: FiShoppingCart,
			features: [
				'25 Credits - $2.99 (25 requests)',
				'75 Credits - $7.99 (75 requests)',
				'150 Credits - $14.99 (150 requests)',
				'250 Credits - $22.99 (250 requests)',
				'Credits never expire',
			],
			popular: false,
		},
	];

	return (
		<div
			className='min-h-screen'
			style={{
				background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`,
			}}>
			{/* Header */}
			<div
				className='shadow-sm border-b'
				style={{
					backgroundColor: colors.neutral.cloudWhite,
					borderColor: colors.ui.gray200,
				}}>
				<div className='max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4'>
					<div className='flex items-center justify-between'>
						<div className='flex items-center gap-4'>
							<button
								onClick={() => router.back()}
								className='flex items-center gap-2 px-3 py-2 rounded-lg transition-colors'
								style={{ color: colors.neutral.slateGray }}
								onMouseEnter={(e) => {
									e.currentTarget.style.color = colors.supporting.lightBlue;
									e.currentTarget.style.backgroundColor = colors.ui.blue50;
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.color = colors.neutral.slateGray;
									e.currentTarget.style.backgroundColor = 'transparent';
								}}>
								<FiArrowLeft className='w-4 h-4' />
								<span className='font-medium'>Back</span>
							</button>

							<div className='flex items-center gap-3'>
								<div
									className='w-10 h-10 rounded-lg flex items-center justify-center'
									style={{
										background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`,
									}}>
									<FiZap className='w-6 h-6 text-white' />
								</div>
								<div>
									<h1
										className='text-2xl font-bold'
										style={{ color: colors.neutral.textBlack }}>
										Credits System
									</h1>
									<p
										className='text-sm'
										style={{ color: colors.neutral.slateGray }}>
										Pay-per-request AI travel assistant with earning
										opportunities
									</p>
								</div>
							</div>
						</div>

						{/* Navigation Buttons */}
						<div className='flex items-center gap-3'>
							<button
								onClick={() => router.push('/globe')}
								className='p-2 rounded-lg transition-colors'
								style={{
									color: colors.neutral.slateGray,
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.color = colors.supporting.lightBlue;
									e.currentTarget.style.backgroundColor = colors.ui.blue50;
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.color = colors.neutral.slateGray;
									e.currentTarget.style.backgroundColor = 'transparent';
								}}
								title='Interactive Globe'>
								<FaGlobe className='w-5 h-5' />
							</button>
							<button
								onClick={() => router.push('/chat')}
								className='p-2 rounded-lg transition-colors'
								style={{
									color: colors.neutral.slateGray,
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.color = colors.supporting.lightBlue;
									e.currentTarget.style.backgroundColor = colors.ui.blue50;
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.color = colors.neutral.slateGray;
									e.currentTarget.style.backgroundColor = 'transparent';
								}}
								title='Chat Assistant'>
								<FaComments className='w-5 h-5' />
							</button>
						</div>
					</div>
				</div>
			</div>

			<div className='max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
				{/* Current Credits Status */}
				<div
					className='rounded-2xl shadow-lg p-6 mb-8'
					style={{ backgroundColor: colors.neutral.cloudWhite }}>
					<div className='text-center'>
						<h2
							className='text-xl font-bold mb-4'
							style={{ color: colors.neutral.textBlack }}>
							Your Credits
						</h2>

						{loading ? (
							<div className='flex items-center justify-center py-4'>
								<FiRepeat
									className='w-6 h-6 animate-spin mr-2'
									style={{ color: colors.brand.blue }}
								/>
								<span style={{ color: colors.neutral.slateGray }}>
									Loading credits...
								</span>
							</div>
						) : error ? (
							<div
								className='p-4 rounded-lg mb-4'
								style={{ backgroundColor: colors.utility.errorLight }}>
								<p
									className='text-sm'
									style={{ color: colors.utility.error }}>
									{error}
								</p>
							</div>
						) : (
							<div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
								<div className='text-center'>
									<div
										className='text-3xl font-bold mb-2'
										style={{ color: colors.brand.blue }}>
										{availableCredits}
									</div>
									<div
										className='text-sm'
										style={{ color: colors.neutral.slateGray }}>
										Available Credits
									</div>
									<div
										className='text-xs mt-1'
										style={{ color: colors.ui.gray500 }}>
										≈ {availableCredits} requests
									</div>
								</div>

								<div className='text-center'>
									<div
										className='text-2xl font-bold mb-2'
										style={{ color: colors.brand.green }}>
										{credits?.credits_earned || 0}
									</div>
									<div
										className='text-sm'
										style={{ color: colors.neutral.slateGray }}>
										Earned Credits
									</div>
									<div
										className='text-xs mt-1'
										style={{ color: colors.ui.gray500 }}>
										From contributions
									</div>
								</div>

								<div className='text-center'>
									<div
										className='text-2xl font-bold mb-2'
										style={{ color: colors.supporting.lightBlue }}>
										{subscriptionStatus.type !== 'none'
											? subscriptionStatus.type.toUpperCase()
											: 'NONE'}
									</div>
									<div
										className='text-sm'
										style={{ color: colors.neutral.slateGray }}>
										Subscription
									</div>
									<div
										className='text-xs mt-1'
										style={{ color: colors.ui.gray500 }}>
										{subscriptionStatus.active
											? `${subscriptionStatus.daysLeft} days left`
											: 'No active subscription'}
									</div>
								</div>
							</div>
						)}
					</div>
				</div>

				{/* Value Proposition */}
				<div
					className='rounded-2xl shadow-lg p-6 mb-8'
					style={{
						background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`,
						color: 'white',
					}}>
					<div className='text-center mb-6'>
						<h2 className='text-2xl font-bold mb-2'>
							Affordable AI Travel Assistant
						</h2>
						<p className='text-lg opacity-90 mb-4'>
							1 credit = 1 request • Starting from just $2.99
						</p>
						<div className='flex justify-center items-center gap-8 text-sm'>
							<div className='text-center'>
								<div className='text-2xl font-bold'>$0.12</div>
								<div className='opacity-80'>per request</div>
							</div>
							<div className='text-center'>
								<div className='text-2xl font-bold'>Free</div>
								<div className='opacity-80'>for agents</div>
							</div>
							<div className='text-center'>
								<div className='text-2xl font-bold'>∞</div>
								<div className='opacity-80'>never expire</div>
							</div>
						</div>
					</div>
				</div>

				{/* How It Works */}
				<div
					className='rounded-2xl shadow-lg p-6 mb-8'
					style={{ backgroundColor: colors.neutral.cloudWhite }}>
					<div className='text-center mb-6'>
						<h2
							className='text-2xl font-bold mb-2'
							style={{ color: colors.neutral.textBlack }}>
							How Credits Work
						</h2>
						<p
							className='text-lg'
							style={{ color: colors.neutral.slateGray }}>
							Simple, transparent pricing for AI requests and interactions
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
						<div className='text-center'>
							<div
								className='w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4'
								style={{ backgroundColor: colors.ui.blue100 }}>
								<FiMessageCircle
									className='w-8 h-8'
									style={{ color: colors.brand.blue }}
								/>
							</div>
							<h3
								className='text-lg font-semibold mb-2'
								style={{ color: colors.neutral.textBlack }}>
								1 Credit = 1 Request
							</h3>
							<p
								className='text-sm'
								style={{ color: colors.ui.gray500 }}>
								Each AI request costs 1 credit
							</p>
						</div>

						<div className='text-center'>
							<div
								className='w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4'
								style={{ backgroundColor: colors.ui.blue100 }}>
								<FiGift
									className='w-8 h-8'
									style={{ color: colors.supporting.lightBlue }}
								/>
							</div>
							<h3
								className='text-lg font-semibold mb-2'
								style={{ color: colors.neutral.textBlack }}>
								Earn by Contributing
							</h3>
							<p
								className='text-sm'
								style={{ color: colors.ui.gray500 }}>
								Get credits for helping improve our database
							</p>
						</div>

						<div className='text-center'>
							<div
								className='w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4'
								style={{ backgroundColor: colors.ui.green100 }}>
								<FiZap
									className='w-8 h-8'
									style={{ color: colors.brand.green }}
								/>
							</div>
							<h3
								className='text-lg font-semibold mb-2'
								style={{ color: colors.neutral.textBlack }}>
								Never Expire
							</h3>
							<p
								className='text-sm'
								style={{ color: colors.ui.gray500 }}>
								Your credits stay with you forever
							</p>
						</div>
					</div>
				</div>

				{/* Earn Credits */}
				<div
					className='rounded-2xl shadow-lg p-6 mb-8'
					style={{ backgroundColor: colors.neutral.cloudWhite }}>
					<h2
						className='text-2xl font-bold mb-6 text-center'
						style={{ color: colors.neutral.textBlack }}>
						Earn Credits by Contributing
					</h2>

					<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
						{earnMethods.map((method, index) => (
							<button
								key={index}
								onClick={() => handleEarnCredits(method)}
								disabled={processing}
								className='p-4 rounded-xl border-2 transition-all duration-200 hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed'
								style={{
									backgroundColor: colors.ui.gray50,
									borderColor: colors.ui.gray200,
								}}>
								<div className='flex items-center gap-4'>
									<div
										className='w-12 h-12 rounded-xl flex items-center justify-center'
										style={{ backgroundColor: method.color }}>
										<method.icon className='w-6 h-6 text-white' />
									</div>
									<div className='flex-1'>
										<div className='flex items-center justify-between mb-1'>
											<h3
												className='font-semibold'
												style={{ color: colors.neutral.textBlack }}>
												{method.title}
											</h3>
											<span
												className='px-2 py-1 rounded-full text-sm font-bold'
												style={{
													backgroundColor: colors.ui.green100,
													color: colors.brand.green,
												}}>
												+{method.credits}
											</span>
										</div>
										<p
											className='text-sm'
											style={{ color: colors.ui.gray500 }}>
											{method.description}
										</p>
									</div>
								</div>
							</button>
						))}
					</div>
				</div>

				{/* Purchase Plans */}
				<div
					className='rounded-2xl shadow-lg p-6'
					style={{ backgroundColor: colors.neutral.cloudWhite }}>
					<h2
						className='text-2xl font-bold mb-6 text-center'
						style={{ color: colors.neutral.textBlack }}>
						Get More Credits
					</h2>

					<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
						{plans.map((plan) => (
							<div
								key={plan.type}
								className={`p-6 rounded-xl border-2 transition-all duration-200 cursor-pointer flex flex-col ${
									plan.popular ? 'ring-2' : ''
								}`}
								style={{
									backgroundColor:
										selectedPlan === plan.type
											? colors.ui.blue50
											: colors.ui.gray50,
									borderColor:
										selectedPlan === plan.type
											? colors.brand.blue
											: colors.ui.gray200,
									minHeight: '400px',
								}}
								onClick={() => setSelectedPlan(plan.type)}>
								{/* Popular Badge - Fixed Height */}
								<div className='text-center mb-4 h-8 flex items-center justify-center'>
									{plan.popular && (
										<span
											className='px-3 py-1 rounded-full text-xs font-bold'
											style={{
												backgroundColor: colors.supporting.lightBlue,
												color: 'white',
											}}>
											MOST POPULAR
										</span>
									)}
								</div>

								<div className='text-center mb-4'>
									<plan.icon
										className='w-12 h-12 mx-auto mb-3'
										style={{ color: colors.brand.blue }}
									/>
									<h3
										className='text-xl font-bold mb-2'
										style={{ color: colors.neutral.textBlack }}>
										{plan.name}
									</h3>
									<div
										className='text-2xl font-bold mb-1'
										style={{ color: colors.brand.blue }}>
										{plan.price}
									</div>
									<div
										className='text-sm'
										style={{ color: colors.ui.gray500 }}>
										{plan.credits}
									</div>
								</div>

								{/* Features List - Flexible Height */}
								<div className='flex-1'>
									<ul className='space-y-2 mb-6'>
										{plan.features.map((feature, index) => (
											<li
												key={index}
												className='flex items-center gap-2 text-sm'
												style={{ color: colors.neutral.slateGray }}>
												<FiCheck
													className='w-4 h-4 flex-shrink-0'
													style={{ color: colors.brand.green }}
												/>
												{feature}
											</li>
										))}
									</ul>
								</div>

								{/* Button - Always at Bottom */}
								<button
									onClick={() => handlePurchaseCredits(plan)}
									disabled={processing}
									className='w-full py-3 rounded-lg font-semibold transition-all duration-200 mt-auto disabled:opacity-50 disabled:cursor-not-allowed'
									style={{
										backgroundColor:
											selectedPlan === plan.type
												? colors.brand.blue
												: colors.brand.blue,
										color: 'white',
										opacity: selectedPlan === plan.type ? 1 : 0.8,
									}}
									onMouseEnter={(e) => {
										if (!processing) {
											e.currentTarget.style.opacity = '1';
											e.currentTarget.style.backgroundColor =
												colors.supporting.lightBlue;
										}
									}}
									onMouseLeave={(e) => {
										if (!processing) {
											e.currentTarget.style.opacity =
												selectedPlan === plan.type ? '1' : '0.8';
											e.currentTarget.style.backgroundColor = colors.brand.blue;
										}
									}}>
									{processing
										? 'Processing...'
										: plan.type === 'subscription'
										? 'Start Subscription'
										: 'Buy Credits'}
								</button>
							</div>
						))}
					</div>
				</div>
			</div>
		</div>
	);
}
