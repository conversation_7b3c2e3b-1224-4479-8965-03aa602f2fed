/** @format */

'use client';

import { colors } from '@/app/colors';
import React, { useEffect, useState } from 'react';

interface ParallaxBackgroundProps {
	children: React.ReactNode;
}

const ParallaxBackground: React.FC<ParallaxBackgroundProps> = ({ children }) => {
	const [scrollY, setScrollY] = useState(0);

	useEffect(() => {
		const handleScroll = () => {
			setScrollY(window.scrollY);
		};

		window.addEventListener('scroll', handleScroll, { passive: true });
		return () => window.removeEventListener('scroll', handleScroll);
	}, []);

	return (
		<div className="relative">
			{/* Parallax Background Layers */}
			<div className="fixed inset-0 pointer-events-none">
				{/* Layer 1 - Slowest moving background */}
				<div
					className="absolute inset-0 opacity-30"
					style={{
						transform: `translateY(${scrollY * 0.1}px)`,
						background: `
							radial-gradient(circle at 20% 20%, ${colors.brand.blue}20 0%, transparent 50%),
							radial-gradient(circle at 80% 80%, ${colors.brand.green}20 0%, transparent 50%),
							radial-gradient(circle at 40% 60%, ${colors.brand.navy}15 0%, transparent 50%)
						`,
					}}
				/>

				{/* Layer 2 - Medium speed geometric shapes */}
				<div
					className="absolute inset-0 opacity-20"
					style={{
						transform: `translateY(${scrollY * 0.2}px)`,
					}}
				>
					{/* Floating geometric shapes */}
					{Array.from({ length: 8 }).map((_, i) => (
						<div
							key={i}
							className="absolute rounded-full"
							style={{
								left: `${10 + (i * 12)}%`,
								top: `${20 + (i * 15)}%`,
								width: `${20 + (i * 5)}px`,
								height: `${20 + (i * 5)}px`,
								background: i % 3 === 0 
									? colors.brand.blue + '30' 
									: i % 3 === 1 
									? colors.brand.green + '30' 
									: colors.supporting.lightBlue + '30',
								transform: `translateY(${scrollY * (0.15 + i * 0.02)}px) rotate(${scrollY * 0.1}deg)`,
								transition: 'transform 0.1s ease-out',
							}}
						/>
					))}
				</div>

				{/* Layer 3 - Fastest moving accent elements */}
				<div
					className="absolute inset-0 opacity-10"
					style={{
						transform: `translateY(${scrollY * 0.3}px)`,
					}}
				>
					{/* Triangle shapes */}
					{Array.from({ length: 5 }).map((_, i) => (
						<div
							key={i}
							className="absolute"
							style={{
								left: `${15 + (i * 18)}%`,
								top: `${30 + (i * 20)}%`,
								width: `${15 + (i * 3)}px`,
								height: `${15 + (i * 3)}px`,
								background: colors.brand.navy + '40',
								clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)',
								transform: `translateY(${scrollY * (0.25 + i * 0.03)}px) rotate(${scrollY * 0.2}deg)`,
								transition: 'transform 0.1s ease-out',
							}}
						/>
					))}
				</div>

				{/* Layer 4 - Gradient overlay that moves with scroll */}
				<div
					className="absolute inset-0 opacity-5"
					style={{
						transform: `translateY(${scrollY * 0.05}px)`,
						background: `
							linear-gradient(45deg,
								${colors.brand.blue}10 0%,
								transparent 25%,
								${colors.brand.green}10 50%,
								transparent 75%,
								${colors.brand.navy}10 100%
							)
						`,
					}}
				/>
			</div>

			{/* Content */}
			<div className="relative z-10">
				{children}
			</div>
		</div>
	);
};

export default ParallaxBackground;
