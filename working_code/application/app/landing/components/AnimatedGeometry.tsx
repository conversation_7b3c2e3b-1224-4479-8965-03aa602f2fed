/** @format */

'use client';

import React from 'react';

interface GeometricShape {
	type: 'circle' | 'triangle' | 'square';
	size: number;
	x: number;
	y: number;
	color: string;
	delay: number;
}

interface AnimatedGeometryProps {
	isVisible: boolean;
	shapes: GeometricShape[];
}

const AnimatedGeometry: React.FC<AnimatedGeometryProps> = ({ isVisible, shapes }) => {
	return (
		<div className="absolute inset-0 pointer-events-none overflow-hidden">
			{shapes.map((shape, index) => (
				<div
					key={index}
					className="absolute transition-all duration-1000 ease-out"
					style={{
						left: `${shape.x}%`,
						top: `${shape.y}%`,
						width: `${shape.size}px`,
						height: `${shape.size}px`,
						backgroundColor: shape.color,
						opacity: isVisible ? 0.6 : 0,
						transform: isVisible 
							? 'scale(1) rotate(0deg)' 
							: 'scale(0) rotate(45deg)',
						transitionDelay: `${shape.delay}ms`,
						borderRadius: shape.type === 'circle' ? '50%' : '0%',
						clipPath: shape.type === 'triangle' 
							? 'polygon(50% 0%, 0% 100%, 100% 100%)' 
							: 'none',
					}}
				/>
			))}
		</div>
	);
};

export default AnimatedGeometry;
