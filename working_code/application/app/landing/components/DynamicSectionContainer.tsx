/** @format */

'use client';

import React from 'react';

interface DynamicSectionContainerProps {
	sectionType: 'hero' | 'section1' | 'section2' | 'footer';
	children: React.ReactNode;
	className?: string;
	style?: React.CSSProperties;
}

const DynamicSectionContainer: React.FC<DynamicSectionContainerProps> = ({
	sectionType,
	children,
	className = '',
	style = {},
}) => {
	// Use simple centered container approach instead of complex dynamic layout
	const centeredContainerClass =
		'mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8';

	return (
		<div
			className={`${className}`}
			style={style}>
			<div className={centeredContainerClass}>{children}</div>
		</div>
	);
};

export default DynamicSectionContainer;
