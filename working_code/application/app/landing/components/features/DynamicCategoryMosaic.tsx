/** @format */

'use client';

import React, { useState } from 'react';
import {
	FiActivity,
	FiBook,
	FiCoffee,
	FiHeart,
	FiMapPin,
	FiMusic,
	FiShoppingBag,
	FiSun,
	FiTrendingUp,
} from 'react-icons/fi';
import { colors } from '../../../colors';
import { POI_CATEGORIES_DATA } from '../../../shared/poi/constants';

// All available colors from design system (brand + supporting)
const ALL_COLORS = [
	colors.brand.blue,
	colors.brand.green,
	colors.brand.navy,
	colors.supporting.lightBlue,
	colors.supporting.mintGreen,
	colors.supporting.teal,
	colors.supporting.darkBlue,
	colors.supporting.purple,
	colors.supporting.softNavy,
];

// Helper functions from constants
const getPOICategories = (): string[] => {
	return Object.keys(POI_CATEGORIES_DATA);
};

const getPOISubcategoriesWithCategory = () => {
	const allSubcategories: Array<{
		subcategory: string;
		category: string;
		importance: number;
	}> = [];
	for (const category in POI_CATEGORIES_DATA) {
		const categoryData =
			POI_CATEGORIES_DATA[category as keyof typeof POI_CATEGORIES_DATA];
		for (const subcategoryData of categoryData.subcategories) {
			allSubcategories.push({
				subcategory: subcategoryData.name,
				category,
				importance: subcategoryData.importance,
			});
		}
	}
	return allSubcategories;
};

// Generates a gradient from a base color with 50% transparency
const getGradient = (color: string) => {
	const hexToRgb = (hex: string) => {
		const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
		return result
			? {
					r: parseInt(result[1], 16),
					g: parseInt(result[2], 16),
					b: parseInt(result[3], 16),
			  }
			: null;
	};

	const baseRgb = hexToRgb(color);
	if (!baseRgb) return color;

	// Create a lighter version for gradient end
	const lighterRgb = {
		r: Math.min(255, baseRgb.r + 40),
		g: Math.min(255, baseRgb.g + 40),
		b: Math.min(255, baseRgb.b + 40),
	};

	const startColor = `rgba(${baseRgb.r}, ${baseRgb.g}, ${baseRgb.b}, 0.5)`;
	const endColor = `rgba(${lighterRgb.r}, ${lighterRgb.g}, ${lighterRgb.b}, 0.5)`;

	return `linear-gradient(45deg, ${startColor}, ${endColor})`;
};

// Shuffle function
const shuffleArray = <T,>(array: T[]): T[] => {
	const shuffled = [...array];
	for (let i = shuffled.length - 1; i > 0; i--) {
		const j = Math.floor(Math.random() * (i + 1));
		[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
	}
	return shuffled;
};

// Calculate wave height and safe container height
const calculateWaveHeight = () => {
	// Wave height is 100px when active (from WaveTransition component)
	const waveHeight = 100;
	// Add some padding to ensure cards don't touch the wave
	const wavePadding = 20;
	return waveHeight + wavePadding;
};

// Calculate maximum safe height for cards container
const calculateMaxContainerHeight = () => {
	const viewportHeight = window.innerHeight;
	const waveReservedSpace = calculateWaveHeight();
	const filtersHeight = 120; // Approximate height for category/subcategory filters
	const sectionPadding = 80; // Top and bottom padding for the section

	return Math.max(
		400,
		viewportHeight - waveReservedSpace - filtersHeight - sectionPadding
	);
};

interface CardData {
	subcategory: string;
	category: string;
	importance: number;
	weight?: number;
	x?: number;
	y?: number;
	width?: number;
	height?: number;
}

interface DynamicCategoryMosaicProps {
	onCategorySelect?: (category: CardData) => void;
}

// Treemap algorithm implementation
const generateTreemap = (
	data: CardData[],
	x: number,
	y: number,
	width: number,
	height: number
): CardData[] => {
	if (data.length === 0) return [];
	if (data.length === 1) {
		return [{ ...data[0], x, y, width, height }];
	}

	// Calculate total weight
	const totalWeight = data.reduce((sum, item) => sum + (item.weight || 1), 0);

	// Sort by weight (largest first)
	const sortedData = [...data].sort(
		(a, b) => (b.weight || 1) - (a.weight || 1)
	);

	// Split into two groups
	let leftWeight = 0;
	let splitIndex = 0;
	const targetWeight = totalWeight / 2;

	for (let i = 0; i < sortedData.length; i++) {
		leftWeight += sortedData[i].weight || 1;
		if (leftWeight >= targetWeight) {
			splitIndex = i + 1;
			break;
		}
	}

	const leftGroup = sortedData.slice(0, splitIndex);
	const rightGroup = sortedData.slice(splitIndex);

	// Decide split direction based on aspect ratio
	const isWiderThanTall = width > height;
	let leftResult: CardData[] = [];
	let rightResult: CardData[] = [];

	if (isWiderThanTall) {
		// Split vertically
		const leftWidth = (width * leftWeight) / totalWeight;
		const rightWidth = width - leftWidth;

		leftResult = generateTreemap(leftGroup, x, y, leftWidth, height);
		rightResult = generateTreemap(
			rightGroup,
			x + leftWidth,
			y,
			rightWidth,
			height
		);
	} else {
		// Split horizontally
		const leftHeight = (height * leftWeight) / totalWeight;
		const rightHeight = height - leftHeight;

		leftResult = generateTreemap(leftGroup, x, y, width, leftHeight);
		rightResult = generateTreemap(
			rightGroup,
			x,
			y + leftHeight,
			width,
			rightHeight
		);
	}

	return [...leftResult, ...rightResult];
};

// Get icon for subcategory - dynamic based on category type
const getCardIcon = (_subcategory: string, category: string) => {
	// Map categories to icon types
	const categoryIconMap: { [key: string]: JSX.Element } = {
		'Food & Drink': <FiCoffee className='w-full h-full text-white' />,
		'Cultural & Creative Experiences': (
			<FiBook className='w-full h-full text-white' />
		),
		'Sports & Fitness': <FiActivity className='w-full h-full text-white' />,
		Entertainment: <FiMusic className='w-full h-full text-white' />,
		'Shopping & Markets': (
			<FiShoppingBag className='w-full h-full text-white' />
		),
		'Outdoor & Nature': <FiSun className='w-full h-full text-white' />,
		'Wellness & Beauty': <FiHeart className='w-full h-full text-white' />,
		Transportation: <FiTrendingUp className='w-full h-full text-white' />,
	};

	return (
		categoryIconMap[category] || (
			<FiMapPin className='w-full h-full text-white' />
		)
	);
};

const CategoryCard: React.FC<{
	cardData: CardData;
	index: number;
}> = ({ cardData, index }) => {
	const { x = 0, y = 0, width = 0, height = 0 } = cardData;

	// Get color ensuring no adjacent cards have same color
	const getDistributedColor = (cardIndex: number) => {
		// Use a more sophisticated distribution to avoid adjacent duplicates
		const colorIndex =
			(cardIndex * 3 + Math.floor(cardIndex / 3)) % ALL_COLORS.length;
		return ALL_COLORS[colorIndex];
	};

	const color = getDistributedColor(index);
	const gradient = getGradient(color);

	// Add spacing between cards
	const spacing = 4;
	const adjustedX = x + spacing;
	const adjustedY = y + spacing;
	const adjustedWidth = width - spacing * 2;
	const adjustedHeight = height - spacing * 2;

	return (
		<div
			style={{
				position: 'absolute',
				left: `${adjustedX}px`,
				top: `${adjustedY}px`,
				width: `${adjustedWidth}px`,
				height: `${adjustedHeight}px`,
				background: gradient, // Full opacity for filled colors
				border: `2px solid ${color}99`, // 60% border opacity for subtle contrast
				borderRadius: '20px',
				boxSizing: 'border-box',
				overflow: 'hidden',
				color: 'white',
				display: 'flex',
				flexDirection: 'column',
				justifyContent: 'center',
				alignItems: 'center',
				padding: adjustedWidth > 180 ? '24px' : '16px',
				textAlign: 'center',
				textShadow: '2px 2px 6px rgba(0,0,0,0.6)',
				boxShadow: `0 6px 20px ${color}30, 0 2px 8px rgba(0,0,0,0.15)`,
				backdropFilter: 'blur(12px)',
			}}>
			{/* Icon with enhanced styling */}
			<div
				style={{
					marginBottom: adjustedWidth > 180 ? '20px' : '12px',
					fontSize:
						adjustedWidth > 250
							? '3em'
							: adjustedWidth > 180
							? '2.5em'
							: adjustedWidth > 120
							? '2em'
							: '1.5em',
					filter: 'drop-shadow(0 3px 6px rgba(0,0,0,0.4))',
				}}>
				{getCardIcon(cardData.subcategory, cardData.category)}
			</div>

			<h3
				style={{
					fontWeight: '800',
					fontSize:
						adjustedWidth > 250
							? '1.2em'
							: adjustedWidth > 180
							? '1em'
							: adjustedWidth > 120
							? '0.9em'
							: '0.8em',
					margin: 0,
					marginBottom: adjustedWidth > 180 ? '8px' : '6px',
					letterSpacing: '0.5px',
					lineHeight: '1.1',
					textTransform: 'uppercase',
				}}>
				{cardData.subcategory}
			</h3>

			<p
				style={{
					fontSize:
						adjustedWidth > 250
							? '0.9em'
							: adjustedWidth > 180
							? '0.8em'
							: adjustedWidth > 120
							? '0.7em'
							: '0.6em',
					opacity: 0.9,
					margin: 0,
					fontWeight: '500',
					letterSpacing: '0.3px',
					textTransform: 'capitalize',
				}}>
				{cardData.category}
			</p>
		</div>
	);
};

const DynamicCategoryMosaic: React.FC<DynamicCategoryMosaicProps> = () => {
	const [selectedCategory, setSelectedCategory] = useState<string>('all');
	const [selectedSubcategory, setSelectedSubcategory] = useState<string>('all');
	const [shuffledData, setShuffledData] = useState<
		Array<{ subcategory: string; category: string; importance: number }>
	>([]);
	const [randomSubcategories, setRandomSubcategories] = useState<
		Array<{ subcategory: string; category: string; importance: number }>
	>([]);
	const [layoutCards, setLayoutCards] = useState<any[]>([]);
	const [containerSize, setContainerSize] = useState({
		width: 1200,
		height: 800,
	});

	const containerRef = React.useRef<HTMLDivElement>(null);

	// Get all categories and subcategories
	const allCategories = getPOICategories();
	const allSubcategories = getPOISubcategoriesWithCategory();

	// Generate random subcategories for "all" categories view
	const generateRandomSubcategories = () => {
		const maxCards =
			containerSize.width < 600 ? 6 : containerSize.width < 900 ? 8 : 10;
		const shuffled = shuffleArray(allSubcategories);
		return shuffled.slice(0, maxCards);
	};

	// Initialize shuffled data on component mount (shuffle on refresh only)
	React.useEffect(() => {
		const shuffled = shuffleArray(allSubcategories);
		setShuffledData(shuffled);
		// Generate initial random subcategories
		setRandomSubcategories(generateRandomSubcategories());
	}, []); // Empty dependency array means this only runs on mount/refresh

	// Regenerate random subcategories when container size changes or when switching back to "all"
	React.useEffect(() => {
		if (selectedCategory === 'all') {
			setRandomSubcategories(generateRandomSubcategories());
		}
	}, [containerSize, selectedCategory]);

	// Track container size for responsive layout
	React.useEffect(() => {
		const updateContainerSize = () => {
			if (containerRef.current) {
				const rect = containerRef.current.getBoundingClientRect();
				const newWidth = Math.max(300, rect.width);
				// Calculate height using dynamic wave height calculation
				const maxSafeHeight = calculateMaxContainerHeight();
				const availableHeight = Math.min(rect.height || 450, maxSafeHeight);
				const newHeight = Math.max(400, availableHeight);
				setContainerSize({ width: newWidth, height: newHeight });
			}
		};

		// Initial size with delay to ensure DOM is ready
		setTimeout(updateContainerSize, 100);

		// Listen for resize events
		const resizeObserver = new ResizeObserver(updateContainerSize);
		if (containerRef.current) {
			resizeObserver.observe(containerRef.current);
		}

		// Listen for window resize as fallback
		window.addEventListener('resize', updateContainerSize);

		return () => {
			resizeObserver.disconnect();
			window.removeEventListener('resize', updateContainerSize);
		};
	}, []);

	// Filter data based on selected category and subcategory
	const getFilteredData = () => {
		if (selectedCategory !== 'all') {
			// When a specific category is selected, show ALL subcategories for that category
			let filtered = shuffledData.filter(
				(item) => item.category === selectedCategory
			);

			if (selectedSubcategory !== 'all') {
				filtered = filtered.filter(
					(item) => item.subcategory === selectedSubcategory
				);
			}

			return filtered;
		}

		// When "all" categories is selected, use the random subcategories
		if (selectedSubcategory !== 'all') {
			return randomSubcategories.filter(
				(item) => item.subcategory === selectedSubcategory
			);
		}

		// Return the random subcategories for "all" view
		return randomSubcategories;
	};

	// Generate layout only when filtered data changes (not on every render)
	React.useEffect(() => {
		const filteredData = getFilteredData();

		if (filteredData.length > 0) {
			// Calculate weights based on importance for balanced distribution
			const totalImportance = filteredData.reduce(
				(sum, item) => sum + item.importance,
				0
			);

			const subcategoriesWithWeights = filteredData.map((item) => {
				// Base weight from importance (normalized)
				const importanceRatio = item.importance / totalImportance;
				const baseWeight = importanceRatio * 100; // Scale to reasonable range

				// Add small variation (±15%) to prevent identical layouts while maintaining importance hierarchy
				const variation = (Math.random() - 0.5) * 0.3; // ±15% variation
				const finalWeight = baseWeight * (1 + variation);

				// Ensure reasonable weight range with minimum differences
				const clampedWeight = Math.max(5, Math.min(50, finalWeight));

				return {
					...item,
					weight: clampedWeight,
				};
			});

			// Randomly shuffle the weighted data to change groupings
			const shuffledWeightedData = shuffleArray(subcategoriesWithWeights);

			// Generate treemap layout with dynamic container size
			const newLayoutCards = generateTreemap(
				shuffledWeightedData,
				0,
				0,
				containerSize.width,
				containerSize.height
			);

			setLayoutCards(newLayoutCards);
		}
	}, [
		shuffledData,
		randomSubcategories,
		selectedCategory,
		selectedSubcategory,
		containerSize,
	]); // Include all data dependencies

	// Get subcategories for selected category
	const getSubcategoriesForCategory = (category: string) => {
		if (category === 'all') return [];
		return allSubcategories
			.filter((item) => item.category === category)
			.map((item) => item.subcategory)
			.filter((value, index, self) => self.indexOf(value) === index); // Remove duplicates
	};

	const availableSubcategories = getSubcategoriesForCategory(selectedCategory);

	return (
		<div className='flex flex-col h-full min-h-0'>
			{/* Redesigned Filter Section */}
			<div className='flex-shrink-0 mb-4 ml-4'>
				{/* Category Filters */}
				<div className='mb-4'>
					{/* Main 3 Categories + Dropdown */}
					<div className='flex items-center gap-4 mb-3'>
						{/* Categories label moved to the left */}
						<h4
							className='text-sm font-semibold flex-shrink-0'
							style={{ color: colors.brand.navy }}>
							Categories
						</h4>

						{/* Filter buttons with added spacing */}
						<div className='flex flex-wrap gap-2'>
							{/* All Categories Button */}
							<button
								className={`px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 border ${
									selectedCategory === 'all' ? 'scale-105' : 'hover:scale-105'
								}`}
								style={{
									minWidth: '70px',
									height: '32px',
									borderColor:
										selectedCategory === 'all'
											? colors.brand.blue
											: colors.supporting.lightBlue,
									background:
										selectedCategory === 'all'
											? `linear-gradient(135deg, ${colors.brand.blue}20 0%, ${colors.brand.green}20 100%)`
											: `linear-gradient(135deg, ${colors.supporting.lightBlue}15 0%, ${colors.supporting.mintGreen}15 100%)`,
									color:
										selectedCategory === 'all'
											? colors.brand.navy
											: colors.supporting.darkBlue,
									fontWeight: selectedCategory === 'all' ? '600' : '500',
								}}
								onClick={() => {
									setSelectedCategory('all');
									setSelectedSubcategory('all');
								}}>
								<span className='block truncate'>All</span>
							</button>

							{/* First 3 Main Categories */}
							{allCategories.slice(0, 3).map((category) => (
								<button
									key={category}
									className={`px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 border ${
										selectedCategory === category
											? 'scale-105'
											: 'hover:scale-105'
									}`}
									style={{
										minWidth: '70px',
										height: '32px',
										borderColor:
											selectedCategory === category
												? colors.brand.green
												: colors.supporting.teal,
										background:
											selectedCategory === category
												? `linear-gradient(135deg, ${colors.brand.green}20 0%, ${colors.brand.blue}20 100%)`
												: `linear-gradient(135deg, ${colors.supporting.teal}15 0%, ${colors.supporting.lightBlue}15 100%)`,
										color:
											selectedCategory === category
												? colors.brand.navy
												: colors.supporting.darkBlue,
										fontWeight: selectedCategory === category ? '600' : '500',
									}}
									onClick={() => {
										setSelectedCategory(category);
										setSelectedSubcategory('all');
									}}>
									<span className='block truncate'>
										{category.length > 8
											? category.substring(0, 8) + '...'
											: category}
									</span>
								</button>
							))}

							{/* Dropdown for remaining categories */}
							{allCategories.length > 3 && (
								<select
									value={selectedCategory}
									onChange={(e) => {
										setSelectedCategory(e.target.value);
										setSelectedSubcategory('all');
									}}
									className='px-3 py-2 rounded-lg text-xs font-medium border cursor-pointer'
									style={{
										minWidth: '70px',
										height: '32px',
										borderColor: colors.supporting.teal,
										background: `linear-gradient(135deg, ${colors.supporting.teal}15 0%, ${colors.supporting.lightBlue}15 100%)`,
										color: colors.supporting.darkBlue,
										fontWeight: '500',
									}}>
									<option value=''>More...</option>
									{allCategories.slice(3).map((category) => (
										<option
											key={category}
											value={category}>
											{category}
										</option>
									))}
								</select>
							)}
						</div>
					</div>
				</div>

				{/* Subcategory Filters - Only show when specific category is selected */}
				{selectedCategory !== 'all' && (
					<div className='mb-4'>
						<h4
							className='text-sm font-semibold mb-3'
							style={{ color: colors.brand.navy }}>
							Subcategories
						</h4>
						<div className='flex flex-wrap gap-2'>
							{/* All Subcategories Button */}
							<button
								className={`px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 border ${
									selectedSubcategory === 'all'
										? 'scale-105'
										: 'hover:scale-105'
								}`}
								style={{
									minWidth: '50px',
									height: '28px',
									borderColor:
										selectedSubcategory === 'all'
											? colors.brand.green
											: colors.supporting.teal,
									background:
										selectedSubcategory === 'all'
											? `linear-gradient(135deg, ${colors.brand.green}15 0%, ${colors.brand.blue}15 100%)`
											: `linear-gradient(135deg, ${colors.supporting.teal}10 0%, ${colors.supporting.lightBlue}10 100%)`,
									color:
										selectedSubcategory === 'all'
											? colors.brand.navy
											: colors.supporting.darkBlue,
									fontWeight: selectedSubcategory === 'all' ? '600' : '500',
								}}
								onClick={() => setSelectedSubcategory('all')}>
								<span className='block truncate'>All</span>
							</button>

							{/* Available Subcategories */}
							{availableSubcategories.slice(0, 8).map((subcategory) => (
								<button
									key={subcategory}
									className={`px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 border ${
										selectedSubcategory === subcategory
											? 'scale-105'
											: 'hover:scale-105'
									}`}
									style={{
										minWidth: '50px',
										height: '28px',
										borderColor:
											selectedSubcategory === subcategory
												? colors.brand.green
												: colors.supporting.teal,
										background:
											selectedSubcategory === subcategory
												? `linear-gradient(135deg, ${colors.brand.green}15 0%, ${colors.brand.blue}15 100%)`
												: `linear-gradient(135deg, ${colors.supporting.teal}10 0%, ${colors.supporting.lightBlue}10 100%)`,
										color:
											selectedSubcategory === subcategory
												? colors.brand.navy
												: colors.supporting.darkBlue,
										fontWeight:
											selectedSubcategory === subcategory ? '600' : '500',
									}}
									onClick={() => setSelectedSubcategory(subcategory)}>
									<span className='block truncate'>
										{subcategory.length > 6
											? subcategory.substring(0, 6) + '...'
											: subcategory}
									</span>
								</button>
							))}
						</div>
					</div>
				)}
			</div>

			{/* Cards Container - Takes remaining space */}
			<div className='flex-1 min-h-0'>
				<div
					ref={containerRef}
					className='w-full h-full'
					style={{
						position: 'relative',
						boxSizing: 'border-box',
						overflow: 'hidden',
						background: 'transparent',
						minHeight: '400px',
						maxHeight: `${calculateMaxContainerHeight()}px`, // Dynamic calculation based on wave height
					}}>
					{layoutCards.map((card, index) => (
						<CategoryCard
							key={`${card.category}-${card.subcategory}-${index}`}
							cardData={card}
							index={index}
						/>
					))}
				</div>
			</div>
		</div>
	);
};

export default DynamicCategoryMosaic;
