/** @format */

'use client';

import { colors } from '@/app/colors';
import React from 'react';

const HeroBackground: React.FC = () => {
	return (
		<div className='absolute inset-0 overflow-hidden pointer-events-none'>
			{/* Geometric Patterns */}
			<div className='absolute inset-0 opacity-20'>
				<div
					className='absolute top-0 left-0 w-full h-full'
					style={{
						background: `
							radial-gradient(circle at 20% 20%, ${colors.brand.blue}30 0%, transparent 50%),
							radial-gradient(circle at 80% 80%, ${colors.brand.green}30 0%, transparent 50%),
							radial-gradient(circle at 40% 60%, ${colors.brand.navy}20 0%, transparent 50%)
						`,
					}}
				/>
			</div>

			{/* Istanbul Map Outline */}
			<div className='absolute inset-0 flex items-center justify-center opacity-10'>
				<svg
					width='400'
					height='300'
					viewBox='0 0 400 300'
					className='text-current'>
					<path
						d='M50 150 Q100 100 150 120 Q200 140 250 130 Q300 120 350 140 Q380 160 350 180 Q300 200 250 190 Q200 180 150 200 Q100 220 50 180 Z'
						fill='none'
						stroke={colors.brand.blue}
						strokeWidth='2'
						className='animate-pulse'
					/>
					{/* Location pins */}
					{Array.from({ length: 8 }).map((_, i) => (
						<circle
							key={i}
							cx={80 + i * 35}
							cy={140 + Math.sin(i) * 20}
							r='3'
							fill={colors.brand.green}
							className='animate-pulse'
							style={{ animationDelay: `${i * 0.2}s` }}
						/>
					))}
				</svg>
			</div>

			{/* Animated Location Pins */}
			<div className='absolute inset-0'>
				{Array.from({ length: 12 }).map((_, i) => (
					<div
						key={i}
						className='absolute w-4 h-4 rounded-full animate-pulse'
						style={{
							left: `${20 + ((i * 7) % 60)}%`,
							top: `${30 + ((i * 11) % 40)}%`,
							background:
								i % 3 === 0
									? colors.brand.blue
									: i % 3 === 1
									? colors.brand.green
									: colors.supporting.lightBlue,
							animationDelay: `${i * 0.3}s`,
							animationDuration: `${2 + Math.random()}s`,
						}}>
						<div
							className='absolute inset-0 rounded-full animate-ping'
							style={{
								background:
									i % 3 === 0
										? colors.brand.blue
										: i % 3 === 1
										? colors.brand.green
										: colors.supporting.lightBlue,
								animationDelay: `${i * 0.3}s`,
							}}></div>
					</div>
				))}
			</div>
		</div>
	);
};

export default HeroBackground;
