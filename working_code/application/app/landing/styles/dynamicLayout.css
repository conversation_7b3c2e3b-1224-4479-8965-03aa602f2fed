/** @format */

/* Dynamic Layout Styles for Landing Page */

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Base responsive utilities */
.dynamic-section {
  transition: all 0.3s ease-out;
  position: relative;
}

/* Mobile-first responsive widths */
.dynamic-section {
  width: 92%;
  margin: 0 auto;
}

/* Tablet responsive adjustments */
@media (min-width: 768px) {
  .dynamic-section {
    width: 80%;
  }
  
  /* Left aligned sections */
  .dynamic-section.section-left {
    margin-left: 10%;
    margin-right: auto;
  }
  
  /* Right aligned sections */
  .dynamic-section.section-right {
    margin-left: auto;
    margin-right: 10%;
  }
  
  /* Center aligned sections */
  .dynamic-section.section-center {
    margin-left: auto;
    margin-right: auto;
  }
}

/* Desktop responsive adjustments */
@media (min-width: 1024px) {
  .dynamic-section {
    width: 70%;
    max-width: 1200px;
  }
  
  /* Left aligned sections */
  .dynamic-section.section-left {
    margin-left: 15%;
    margin-right: auto;
  }
  
  /* Right aligned sections */
  .dynamic-section.section-right {
    margin-left: auto;
    margin-right: 15%;
  }
  
  /* Center aligned sections */
  .dynamic-section.section-center {
    margin-left: auto;
    margin-right: auto;
  }
}

/* Large desktop adjustments */
@media (min-width: 1440px) {
  .dynamic-section {
    max-width: 1400px;
  }
  
  /* Left aligned sections */
  .dynamic-section.section-left {
    margin-left: 12%;
    margin-right: auto;
  }
  
  /* Right aligned sections */
  .dynamic-section.section-right {
    margin-left: auto;
    margin-right: 12%;
  }
}

/* Animation classes for scroll transitions */
.scroll-animation {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-animation.fade-scale-idle {
  opacity: 0;
  transform: scale(0.9);
}

.scroll-animation.fade-scale-entering {
  opacity: 0.5;
  transform: scale(0.95);
}

.scroll-animation.fade-scale-visible {
  opacity: 1;
  transform: scale(1);
}

.scroll-animation.slide-diagonal-idle {
  opacity: 0;
  transform: translateX(48px) translateY(32px) rotate(1deg);
}

.scroll-animation.slide-diagonal-entering {
  opacity: 0.7;
  transform: translateX(24px) translateY(16px) rotate(0.5deg);
}

.scroll-animation.slide-diagonal-visible {
  opacity: 1;
  transform: translateX(0) translateY(0) rotate(0deg);
}

.scroll-animation.wave-wash-idle {
  opacity: 0;
  transform: translateY(32px) scale(0.95);
}

.scroll-animation.wave-wash-entering {
  opacity: 0.7;
  transform: translateY(16px) scale(0.98);
}

.scroll-animation.wave-wash-visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Wave transition styles */
.wave-transition {
  position: absolute;
  left: 0;
  right: 0;
  pointer-events: none;
  z-index: 10;
  transition: all 1.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.wave-transition.wave-up {
  bottom: 0;
}

.wave-transition.wave-down {
  top: 0;
}

.wave-transition svg {
  width: 100%;
  height: 100%;
  display: block;
}

/* Geometric animation backgrounds */
.geometric-background {
  position: absolute;
  inset: 0;
  pointer-events: none;
  overflow: hidden;
  z-index: 1;
}

.geometric-shape {
  position: absolute;
  transition: all 1s ease-out;
  opacity: 0;
}

.geometric-shape.visible {
  opacity: 0.6;
}

.geometric-shape.circle {
  border-radius: 50%;
}

.geometric-shape.triangle {
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}

.geometric-shape.square {
  border-radius: 0;
}

/* Section spacing adjustments */
.section-spacing {
  padding: 3rem 0;
}

@media (min-width: 768px) {
  .section-spacing {
    padding: 4rem 0;
  }
}

@media (min-width: 1024px) {
  .section-spacing {
    padding: 5rem 0;
  }
}

/* Content alignment utilities */
.content-left {
  text-align: left;
}

.content-right {
  text-align: right;
}

.content-center {
  text-align: center;
}

@media (min-width: 768px) {
  .content-responsive-center {
    text-align: center;
  }
}

/* Hover effects for dynamic sections */
.dynamic-section:hover {
  transform: translateY(-2px);
}

.dynamic-section.section-left:hover {
  transform: translateY(-2px) translateX(4px);
}

.dynamic-section.section-right:hover {
  transform: translateY(-2px) translateX(-4px);
}

/* Performance optimizations */
.dynamic-section,
.scroll-animation,
.wave-transition,
.geometric-shape {
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .dynamic-section,
  .scroll-animation,
  .wave-transition,
  .geometric-shape {
    transition: none;
    animation: none;
  }
  
  .dynamic-section:hover {
    transform: none;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .wave-transition svg path {
    opacity: 0.8;
  }
  
  .geometric-shape {
    opacity: 0.4;
  }
  
  .geometric-shape.visible {
    opacity: 0.3;
  }
}
