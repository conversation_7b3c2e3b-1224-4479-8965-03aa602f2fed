/** @format */

'use client';

import { DYNAMIC_LAYOUT_CONFIG } from './responsiveUtils';

// Section positioning types
export type SectionPosition = 'left' | 'right' | 'center';

// Layout configuration for each section
export interface SectionLayoutConfig {
	position: SectionPosition;
	width: {
		mobile: string;
		tablet: string;
		desktop: string;
	};
	margin: {
		mobile: string;
		tablet: string;
		desktop: string;
	};
	padding: {
		mobile: string;
		tablet: string;
		desktop: string;
	};
}

// Get layout configuration for a specific section
export const getSectionLayout = (
	sectionType: 'hero' | 'section1' | 'section2' | 'footer'
): SectionLayoutConfig => {
	const position = DYNAMIC_LAYOUT_CONFIG.positioning[sectionType];
	const widths = DYNAMIC_LAYOUT_CONFIG.sectionWidths;
	const spacing = DYNAMIC_LAYOUT_CONFIG.sectionSpacing;

	return {
		position,
		width: {
			mobile: widths.mobile.preferred,
			tablet: widths.tablet.preferred,
			desktop: widths.desktop.preferred,
		},
		margin: {
			mobile: getMarginForPosition(position, spacing.mobile.horizontal),
			tablet: getMarginForPosition(position, spacing.tablet.horizontal),
			desktop: getMarginForPosition(position, spacing.desktop.horizontal),
		},
		padding: {
			mobile: `${spacing.mobile.vertical} ${spacing.mobile.horizontal}`,
			tablet: `${spacing.tablet.vertical} ${spacing.tablet.horizontal}`,
			desktop: `${spacing.desktop.vertical} ${spacing.desktop.horizontal}`,
		},
	};
};

// Calculate margin based on position
const getMarginForPosition = (
	position: SectionPosition,
	horizontalSpacing: string
): string => {
	switch (position) {
		case 'left':
			return `0 auto 0 ${horizontalSpacing}`;
		case 'right':
			return `0 ${horizontalSpacing} 0 auto`;
		case 'center':
		default:
			return '0 auto';
	}
};

// Generate responsive CSS styles for a section
export const generateSectionStyles = (
	sectionType: 'hero' | 'section1' | 'section2' | 'footer'
): React.CSSProperties => {
	const layout = getSectionLayout(sectionType);

	return {
		width: layout.width.mobile,
		margin: layout.margin.mobile,
		padding: layout.padding.mobile,
		transition: 'all 0.3s ease-out',

		// Responsive styles using CSS custom properties
		'--tablet-width': layout.width.tablet,
		'--tablet-margin': layout.margin.tablet,
		'--tablet-padding': layout.padding.tablet,

		'--desktop-width': layout.width.desktop,
		'--desktop-margin': layout.margin.desktop,
		'--desktop-padding': layout.padding.desktop,
	} as React.CSSProperties;
};

// CSS class generator for responsive behavior
export const getResponsiveClasses = (
	sectionType: 'hero' | 'section1' | 'section2' | 'footer'
): string => {
	const layout = getSectionLayout(sectionType);
	const baseClasses = 'relative transition-all duration-300 ease-out';

	// Position-specific classes
	const positionClasses = {
		left: 'ml-4 md:ml-8 lg:ml-16',
		right: 'mr-4 md:mr-8 lg:mr-16',
		center: 'mx-auto',
	};

	// Width constraint classes
	const widthClasses = 'w-[92%] md:w-[80%] lg:w-[70%]';

	// Max width constraints to prevent edge-to-edge
	const maxWidthClasses = 'max-w-none md:max-w-5xl lg:max-w-6xl';

	return `${baseClasses} ${
		positionClasses[layout.position]
	} ${widthClasses} ${maxWidthClasses}`;
};

// Hook for dynamic section positioning
export const useDynamicLayout = (
	sectionType: 'hero' | 'section1' | 'section2' | 'footer'
) => {
	const layout = getSectionLayout(sectionType);
	const styles = generateSectionStyles(sectionType);
	const classes = getResponsiveClasses(sectionType);

	return {
		layout,
		styles,
		classes,
		position: layout.position,
	};
};

// Get container props for sections
export const getDynamicSectionProps = (
	sectionType: 'hero' | 'section1' | 'section2' | 'footer',
	className: string = '',
	style: React.CSSProperties = {}
) => {
	const { styles, classes, position } = useDynamicLayout(sectionType);

	// Add CSS classes for positioning and responsive behavior
	const positionClass = `section-${position}`;
	const responsiveClasses = 'dynamic-section section-spacing';

	return {
		className: `${classes} ${responsiveClasses} ${positionClass} ${className}`,
		style: { ...styles, ...style },
	};
};

// Zig-zag layout utilities
export const ZIGZAG_PATTERNS = {
	// Standard zig-zag: left, right, left, right
	standard: ['left', 'right', 'left', 'right'] as SectionPosition[],

	// Reverse zig-zag: right, left, right, left
	reverse: ['right', 'left', 'right', 'left'] as SectionPosition[],

	// Center-focused: center, left, right, center
	centerFocused: ['center', 'left', 'right', 'center'] as SectionPosition[],

	// Asymmetric: left, left, right, center
	asymmetric: ['left', 'left', 'right', 'center'] as SectionPosition[],
};

// Get position for a section based on pattern
export const getZigZagPosition = (
	sectionIndex: number,
	pattern: SectionPosition[] = ZIGZAG_PATTERNS.centerFocused
): SectionPosition => {
	return pattern[sectionIndex % pattern.length];
};

// Advanced layout configurations
export const ADVANCED_LAYOUTS = {
	// Wave-like positioning with varying widths
	wave: {
		positions: ['center', 'left', 'right', 'center'] as SectionPosition[],
		widths: {
			mobile: ['95%', '90%', '90%', '95%'],
			tablet: ['85%', '75%', '75%', '85%'],
			desktop: ['75%', '65%', '65%', '75%'],
		},
	},

	// Spiral-like positioning
	spiral: {
		positions: ['center', 'right', 'left', 'center'] as SectionPosition[],
		widths: {
			mobile: ['92%', '88%', '88%', '92%'],
			tablet: ['80%', '70%', '70%', '80%'],
			desktop: ['70%', '60%', '60%', '70%'],
		},
	},

	// Funnel-like positioning (wide to narrow)
	funnel: {
		positions: ['center', 'left', 'right', 'center'] as SectionPosition[],
		widths: {
			mobile: ['95%', '90%', '85%', '80%'],
			tablet: ['85%', '75%', '70%', '65%'],
			desktop: ['75%', '65%', '60%', '55%'],
		},
	},
};

// Get advanced layout configuration
export const getAdvancedLayout = (
	layoutType: keyof typeof ADVANCED_LAYOUTS,
	sectionIndex: number
) => {
	const layout = ADVANCED_LAYOUTS[layoutType];
	const position = layout.positions[sectionIndex % layout.positions.length];

	return {
		position,
		width: {
			mobile: layout.widths.mobile[sectionIndex % layout.widths.mobile.length],
			tablet: layout.widths.tablet[sectionIndex % layout.widths.tablet.length],
			desktop:
				layout.widths.desktop[sectionIndex % layout.widths.desktop.length],
		},
	};
};

// Responsive breakpoint utilities
export const RESPONSIVE_BREAKPOINTS = {
	mobile: '(max-width: 767px)',
	tablet: '(min-width: 768px) and (max-width: 1023px)',
	desktop: '(min-width: 1024px)',
};

// Media query hook for responsive behavior
export const useResponsiveLayout = () => {
	const isMobile =
		typeof window !== 'undefined'
			? window.matchMedia(RESPONSIVE_BREAKPOINTS.mobile).matches
			: false;
	const isTablet =
		typeof window !== 'undefined'
			? window.matchMedia(RESPONSIVE_BREAKPOINTS.tablet).matches
			: false;
	const isDesktop =
		typeof window !== 'undefined'
			? window.matchMedia(RESPONSIVE_BREAKPOINTS.desktop).matches
			: false;

	return {
		isMobile,
		isTablet,
		isDesktop,
		screenSize: isMobile ? 'mobile' : isTablet ? 'tablet' : 'desktop',
	};
};
