/* Game-like Globe Styles */

.globe-container {
  position: relative;
  overflow: hidden;
}

/* Locked country overlay effect */
.locked-country {
  position: relative;
}

.locked-country::after {
  content: '🔒';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
  opacity: 0.7;
  pointer-events: none;
  z-index: 10;
}

/* Unlocked country glow effect */
.unlocked-country {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
  }
  100% {
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.6);
  }
}

/* Game status indicator */
.game-status {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 15px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  z-index: 30;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.game-status.unlocked {
  background: rgba(76, 175, 80, 0.9);
  border-color: rgba(76, 175, 80, 0.5);
}

.game-status.locked {
  background: rgba(44, 44, 44, 0.9);
  border-color: rgba(255, 255, 255, 0.1);
}

/* Click indicator for countries */
.country-clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.country-clickable:hover {
  transform: scale(1.02);
  filter: brightness(1.2);
}

.country-clickable.locked:hover {
  filter: brightness(1.1);
  cursor: not-allowed;
}

/* Loading animation for rankings */
.ranking-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.ranking-loading .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Zoom level indicator */
.zoom-indicator {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
  z-index: 30;
  backdrop-filter: blur(10px);
}

/* Transition effects */
.globe-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.globe-transition.zooming {
  transform: scale(1.05);
}

/* City boundary highlight */
.city-boundary {
  stroke: #4CAF50;
  stroke-width: 2;
  stroke-dasharray: 5,5;
  animation: dash 2s linear infinite;
  fill: rgba(76, 175, 80, 0.1);
}

@keyframes dash {
  to {
    stroke-dashoffset: -10;
  }
}

/* City boundaries overlay */
.city-boundaries-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 15;
}

.city-boundary-item {
  position: absolute;
  pointer-events: auto;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid rgba(76, 175, 80, 0.6);
  background: rgba(76, 175, 80, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  min-width: 80px;
  text-align: center;
}

.city-boundary-item:hover {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.8);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.city-boundary-label {
  color: #4CAF50;
  font-weight: 600;
  font-size: 12px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Position city boundary items */
.city-boundary-item[data-district="kadikoy"] {
  top: 45%;
  left: 55%;
}

.city-boundary-item[data-district="besiktas"] {
  top: 35%;
  left: 50%;
}

.city-boundary-item[data-district="fatih"] {
  top: 40%;
  left: 48%;
}

.city-boundary-item[data-district="sisli"] {
  top: 30%;
  left: 52%;
}

/* Game instructions */
.game-instructions {
  position: absolute;
  bottom: 80px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  z-index: 30;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  max-width: 400px;
}

.game-instructions .highlight {
  color: #4CAF50;
  font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .game-status {
    top: 10px;
    left: 10px;
    font-size: 12px;
    padding: 8px 12px;
  }
  
  .zoom-indicator {
    bottom: 10px;
    right: 10px;
    font-size: 11px;
    padding: 6px 10px;
  }
  
  .game-instructions {
    bottom: 60px;
    font-size: 12px;
    padding: 12px 16px;
    max-width: 300px;
  }
} 