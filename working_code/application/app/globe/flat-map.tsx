/** @format */

'use client';

import POICard from '@/app/shared/cards/components/POICard';
import { BasePOI } from '@/app/shared/cards/components/types'; // FIXED: Import BasePOI from cards types
import { usePOIFetcher } from '@/app/shared/hooks/usePOIFetcher';
import { usePOIManager } from '@/app/shared/maps/components/POIManager';
import UnifiedMapContainer from '@/app/shared/maps/components/UnifiedMapContainer';
import L from 'leaflet';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { FaComments, FaHome, FaLocationArrow } from 'react-icons/fa';
// Use the POI type from the maps component to match usePOIManager expectations
interface POI {
	id: number;
	poi_type: string;
	poi_id?: number; // Optional for user POIs
	temp_id?: number; // For user temp POIs
	approved_id?: number; // For user approved POIs
	name: string;
	category: string;
	subcategory?: string; // Make optional to match API response
	city?: string; // Make optional to match API response
	district?: string; // Make optional to match API response
	country?: string;
	latitude: number;
	longitude: number;
	random_score?: number; // Make optional
	phone_number?: string;
	opening_hours?: string;
	description?: string; // Add description field
	user_rating_avg?: number; // Add rating fields
	user_rating_count?: number;
	distance_km?: number; // Add distance field
	is_favorite?: boolean; // Add favorite field
	type?: string; // Added for BasePOI compatibility
	neighborhood?: string; // Added for BasePOI compatibility
	address?: string; // Added for BasePOI compatibility
}

interface FlatMapProps {
	isFlattened: boolean;
	isClient: boolean;
	currentLocation: { lat: number; lng: number } | null;
	userLocation: { latitude: number; longitude: number } | null;
	onReturnToGlobe: () => void;
}

export default function FlatMap({
	isFlattened,
	isClient,
	currentLocation,
	userLocation,
	onReturnToGlobe,
}: FlatMapProps) {
	const router = useRouter();
	const [map, setMap] = useState<L.Map | null>(null);
	const [selectedPOI, setSelectedPOI] = useState<BasePOI | null>(null);
	const [showPOIInfo, setShowPOIInfo] = useState(false);
	const [mapCenter, setMapCenter] = useState<{
		lat: number;
		lng: number;
	} | null>(currentLocation);
	const [mapZoom, setMapZoom] = useState<number>(15);

	// Handle POI click
	// FIXED: Explicitly map POI fields to correct types for BasePOI
	const handlePOIClick = useCallback((poi: POI) => {
		const basePOI: BasePOI = {
			id: poi.id,
			name: String(poi.name),
			category: String(poi.category || ''),
			city: poi.city ? String(poi.city) : undefined,
			district: poi.district ? String(poi.district) : undefined,
			neighborhood: poi.neighborhood ? String(poi.neighborhood) : undefined,
			subcategory: poi.subcategory ? String(poi.subcategory) : undefined,
			latitude: typeof poi.latitude === 'number' ? poi.latitude : 0,
			longitude: typeof poi.longitude === 'number' ? poi.longitude : 0,
			// FIXED: Add POI identification fields that are required for UserInteractionButtons
			poi_type: poi.poi_type || 'official', // Default to 'official' if not specified
			poi_id: poi.poi_id, // For official POIs
			temp_id: poi.temp_id, // For user temp POIs
			approved_id: poi.approved_id, // For user approved POIs
			// Add other optional fields
			country: poi.country ? String(poi.country) : undefined,
			phone_number: poi.phone_number ? String(poi.phone_number) : undefined,
			opening_hours: poi.opening_hours ? String(poi.opening_hours) : undefined,
			description: poi.description ? String(poi.description) : undefined,
			user_rating_avg: poi.user_rating_avg,
			user_rating_count: poi.user_rating_count,
			distance_km: poi.distance_km,
			is_favorite: poi.is_favorite,
		};
		setSelectedPOI(basePOI);
		setShowPOIInfo(true);
	}, []);

	// Use optimized POI fetcher
	const {
		pois,
		isLoading: isPOILoading,
		fetchPOIs,
		loadingProgress,
	} = usePOIFetcher();

	// Use optimized POI manager
	const { updateMarkers, cleanup } = usePOIManager({
		map,
		pois,
		onPOIClick: handlePOIClick,
		userLocation,
	});

	// Navigate to POI profile
	const handleNavigateToPOI = useCallback(
		(poi: BasePOI) => {
			console.log('Navigating to POI:', poi);

			// The poi_type should be 'official', 'user_temp', or 'user_approved'
			// For POIs from the globe API, they should all be 'official'
			const poiType = String(poi.poi_type || 'official'); // Default to 'official' if not specified
			const poiId = poi.poi_id || poi.id;

			console.log('Navigation details:', { poiType, poiId, originalPoi: poi });

			// Validate POI type
			if (!['official', 'user_temp', 'user_approved'].includes(poiType)) {
				console.warn('Invalid POI type, defaulting to official:', poiType);
				if (poiId) {
					router.push(`/pois/official/${poiId}`);
				} else {
					alert('POI details not available - missing ID');
				}
				return;
			}

			if (poiId) {
				const url = `/pois/${poiType}/${poiId}`;
				console.log('Navigating to URL:', url);
				router.push(url);
			} else {
				console.warn('POI navigation failed: missing ID', poi);
				alert('POI details not available - missing ID');
			}
		},
		[router]
	);

	// Handle map ready
	const handleMapReady = useCallback((mapInstance: L.Map) => {
		setMap(mapInstance);
		console.log('Map ready');
	}, []);

	// Handle map bounds/zoom/center change
	const handleBoundsChange = useCallback(
		(bounds: L.LatLngBounds, center: L.LatLng, zoom: number) => {
			setMapCenter({ lat: center.lat, lng: center.lng });
			setMapZoom(zoom);
			fetchPOIs(bounds, zoom);
		},
		[fetchPOIs]
	);

	// Handle focus on user location
	const handleFocusUserLocation = useCallback(() => {
		if (!userLocation || !map) return;

		try {
			map.setView([userLocation.latitude, userLocation.longitude], 15, {
				animate: true,
				duration: 1.0,
			});
			setMapCenter({ lat: userLocation.latitude, lng: userLocation.longitude });
			setMapZoom(15);
		} catch (error) {
			console.error('Error focusing on user location:', error);
		}
	}, [userLocation, map]);

	// Update markers when POIs or map changes
	useEffect(() => {
		if (map) {
			updateMarkers();
		}
	}, [map, pois, userLocation, updateMarkers]);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			cleanup();
		};
	}, [cleanup]);

	if (!isClient) return null;
	if (!isFlattened) return null;

	return (
		<div
			className='fixed inset-0 w-full h-full z-50'
			style={{ overflow: 'hidden' }}>
			{/* Unified Map Container */}
			{mapCenter && (
				<UnifiedMapContainer
					mapId='flat-map'
					center={[mapCenter.lat, mapCenter.lng]}
					zoom={mapZoom}
					onMapReady={handleMapReady}
					onBoundsChange={handleBoundsChange}
					style={{
						position: 'absolute',
						top: 0,
						left: 0,
						right: 0,
						bottom: 0,
						overflow: 'hidden',
					}}
				/>
			)}

			{/* Debug panel - top left */}
			<div className='absolute top-4 left-4 z-50 bg-black/80 text-white p-3 rounded-lg text-xs font-mono'>
				<div>POIs: {pois.length}</div>
				<div>
					Center:{' '}
					{mapCenter
						? `${mapCenter.lat.toFixed(4)}, ${mapCenter.lng.toFixed(4)}`
						: 'None'}
				</div>
				<div>Zoom: {mapZoom}</div>
				<div>Loading: {isPOILoading ? 'Yes' : 'No'}</div>
				<div>Progress: {Math.round(loadingProgress)}%</div>
			</div>

			{/* Control and Navigation buttons - reorganized for better hierarchy */}
			<div className='absolute bottom-4 right-4 z-50 flex flex-col gap-2'>
				{/* Primary Control - Return to Globe */}
				<button
					onClick={onReturnToGlobe}
					className='px-4 py-3 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2'
					style={{
						background: 'linear-gradient(135deg, #33C2FF 0%, #80ED99 100%)',
						color: 'white',
						border: 'none',
					}}
					title='Return to Globe View'>
					<FaLocationArrow
						className='w-4 h-4'
						style={{ transform: 'rotate(180deg)' }}
					/>
					<span className='text-sm font-medium'>Return to Globe</span>
				</button>

				{/* Secondary Controls */}
				{userLocation && (
					<button
						onClick={handleFocusUserLocation}
						className='px-4 py-3 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2'
						style={{
							background: 'linear-gradient(135deg, #1e40af 0%, #059669 100%)',
							color: 'white',
						}}
						title='Focus on your location'>
						<FaLocationArrow className='w-4 h-4' />
						<span className='text-sm font-medium hidden sm:inline'>
							My Location
						</span>
					</button>
				)}

				{/* Navigation buttons - placed below primary controls */}
				<div className='flex flex-col gap-2 pt-2 border-t border-white/20'>
					<button
						onClick={() => router.push('/')}
						className='px-4 py-2 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2'
						style={{
							background: 'linear-gradient(135deg, #1e40af 0%, #059669 100%)',
							color: 'white',
						}}
						title='Home'>
						<FaHome className='w-4 h-4' />
						<span className='hidden sm:inline text-sm'>Home</span>
					</button>
					<button
						onClick={() => router.push('/chat')}
						className='px-4 py-2 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2'
						style={{
							background: 'linear-gradient(135deg, #7c3aed 0%, #1e40af 100%)',
							color: 'white',
						}}
						title='Chat'>
						<FaComments className='w-4 h-4' />
						<span className='hidden sm:inline text-sm'>Chat</span>
					</button>
					<button
						onClick={() => router.push('/pois')}
						className='px-4 py-2 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2'
						style={{
							background: 'linear-gradient(135deg, #26A69A 0%, #1e40af 100%)',
							color: 'white',
						}}
						title='Points of Interest'>
						<FaLocationArrow className='w-4 h-4' />
						<span className='hidden sm:inline text-sm'>POIs</span>
					</button>
				</div>
			</div>

			{/* Enhanced POI Card with Individual Interaction Loading */}
			{selectedPOI && (
				<POICard
					poi={{
						...selectedPOI,
						id:
							typeof selectedPOI.id === 'string'
								? Number(selectedPOI.id)
								: selectedPOI.id,
						category: selectedPOI.category ?? '', // Ensure string
						latitude: selectedPOI.latitude ?? 0, // Ensure number
						longitude: selectedPOI.longitude ?? 0, // Ensure number
					}}
					isVisible={showPOIInfo}
					onClose={() => setShowPOIInfo(false)}
					onNavigate={handleNavigateToPOI}
					showActions={true}
					variant='modal'
					useBatchLoading={false} // Use individual loading for flat-map
					useSimpleUI={true} // Use simple UI like OptimizedPOICard
					showPoiId={false} // Don't show POI ID in simple UI
					onLoadInteractions={() => {}} // Not needed for individual loading
					interactionData={null} // No pre-loaded data, will load individually
					disableInteractionAutoLoad={false} // Enable auto-loading for unified system
				/>
			)}
		</div>
	);
}
