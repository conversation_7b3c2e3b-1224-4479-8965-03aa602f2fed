/** @format */

'use client';

import { colors } from '@/app/colors';
import {
	LocationSetup,
	useLocationManager,
	useLocationSetup,
} from '@/app/shared/locationManager';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
	FaComments,
	FaInfo,
	FaLocationArrow,
	FaMapMarkerAlt,
	FaTimes,
} from 'react-icons/fa';
import FlatMap from './flat-map';
import Globe, { GlobeRef } from './globe';
import { GlobeNavContext } from './globe-context';
import InfoPanel from './info-panel';

// Global zoom constants - all functions use these values
const ZOOM_CONSTANTS = {
	INITIAL_ALTITUDE: 1.5,
	TRANSITION_THRESHOLD: 0.5, // Trigger transition to flat map
	MANUAL_RETURN_PROTECTION_THRESHOLD: 1.4, // Reset manual return protection at initial altitude
	SAFE_DISTANCE: 1.2, // Safe distance for manual return protection
} as const;

export default function GlobePage() {
	const router = useRouter();
	const globeRef = useRef<GlobeRef>(null);
	const [isClient, setIsClient] = useState(false);
	const [showInfo, setShowInfo] = useState(true);
	const [showSettings, setShowSettings] = useState(false);
	const [showNavButtons, setShowNavButtons] = useState(true);
	const [showBottomInstructions, setShowBottomInstructions] = useState(true);
	const [showLocationInfo, setShowLocationInfo] = useState(false);
	const [shouldFlashInfo, setShouldFlashInfo] = useState(false);
	const [flashCount, setFlashCount] = useState(0);
	const [showGlobeTitle, setShowGlobeTitle] = useState(true);
	const [isFlattened, setIsFlattened] = useState(false);
	const [currentLocation, setCurrentLocation] = useState<{
		lat: number;
		lng: number;
	} | null>(null);
	const [countriesData, setCountriesData] = useState<
		Array<Record<string, unknown>>
	>([]);
	const [userMarker, setUserMarker] = useState<Array<Record<string, unknown>>>(
		[]
	);
	const [currentZoom, setCurrentZoom] = useState<number>(
		ZOOM_CONSTANTS.INITIAL_ALTITUDE
	);
	const [isManuallyReturning, setIsManuallyReturning] = useState(false);
	const [manualReturnTime, setManualReturnTime] = useState<number>(0);
	const isManuallyReturningRef = useRef(false);
	const [isTransitioning, setIsTransitioning] = useState(false);
	const lastZoomChangeRef = useRef(Date.now());
	const [canAutoTransition, setCanAutoTransition] = useState(false);
	const hasMounted = useRef(false);
	const [userManuallyClickedInfo, setUserManuallyClickedInfo] = useState(false);

	// Use location manager hook
	const {
		location: userLocation,
		setupStatus,
		hasLoadedFromStorage,
	} = useLocationManager();

	// Integrate useLocationSetup
	const {
		showLocationSetup,
		isLocationSetupRequired,
		triggerLocationSetupIfNeeded,
		handleLocationSetupComplete,
	} = useLocationSetup();

	// Refs to store timer IDs for auto-hide
	const navButtonsTimerRef = useRef<NodeJS.Timeout | null>(null);
	const bottomInstructionsTimerRef = useRef<NodeJS.Timeout | null>(null);
	const globeTitleTimerRef = useRef<NodeJS.Timeout | null>(null);

	// Update user marker and current location when location changes
	useEffect(() => {
		if (userLocation) {
			setUserMarker([
				{
					lat: userLocation.latitude,
					lng: userLocation.longitude,
					size: 1.2,
					color: '#FF0000', // Bright red color for maximum visibility
				},
			]);

			// Set current location to user location
			setCurrentLocation({
				lat: userLocation.latitude,
				lng: userLocation.longitude,
			});
		} else if (hasLoadedFromStorage) {
			// Only clear markers if we've loaded from storage and confirmed there's no location
			setUserMarker([]);
			setCurrentLocation(null);
		}
	}, [userLocation, hasLoadedFromStorage]);

	// Handle location setup completion - force location update
	useEffect(() => {
		if (setupStatus.hasSetup && userLocation) {
			// Force update the current location when setup is completed
			setCurrentLocation({
				lat: userLocation.latitude,
				lng: userLocation.longitude,
			});

			// Update user marker
			setUserMarker([
				{
					lat: userLocation.latitude,
					lng: userLocation.longitude,
					size: 1.2,
					color: '#FF0000',
				},
			]);

			// Also focus on the user location
			if (globeRef.current) {
				setTimeout(() => {
					globeRef.current?.pointOfView(
						{
							lat: userLocation.latitude,
							lng: userLocation.longitude,
							altitude: 2,
						},
						1500
					);
				}, 500); // Small delay to ensure globe is ready
			}
		}
	}, [setupStatus.hasSetup, userLocation, setupStatus.setupTimestamp]);

	// Callback handlers for Globe component
	const handleZoomChange = (distance: number) => {
		console.log('📏 Zoom change:', {
			distance,
			isManuallyReturning,
			isFlattened,
			isTransitioning,
			isManuallyReturningRef: isManuallyReturningRef.current,
		});
		setCurrentZoom(distance);
		lastZoomChangeRef.current = Date.now();

		// Reset manual returning flag when user manually zooms out to a safe distance
		if (
			isManuallyReturningRef.current &&
			distance >= ZOOM_CONSTANTS.MANUAL_RETURN_PROTECTION_THRESHOLD &&
			!isFlattened
		) {
			console.log(
				'🔄 Manual return protection ended - user zoomed out to safe distance'
			);
			isManuallyReturningRef.current = false;
			setIsManuallyReturning(false);
			setManualReturnTime(0); // Reset the timestamp
			// Show a brief notification that auto-transition is available again
			setShowInfo(true);
			setUserManuallyClickedInfo(false);
			setTimeout(() => {
				setShowInfo(false);
			}, 2000);
		}
	};

	const handleLocationChange = (location: { lat: number; lng: number }) => {
		setCurrentLocation(location);
	};

	const handleTransitionTrigger = () => {
		console.log('🚀 Transition trigger called:', {
			isFlattened,
			isTransitioning,
			isManuallyReturning,
		});
		setIsTransitioning(true);

		setTimeout(() => {
			console.log('🚀 Direct transition completed');
			setIsFlattened(true);
			setIsTransitioning(false);
			setShowInfo(true);
			setShowGlobeTitle(true);
			setShouldFlashInfo(false);
			setFlashCount(0);
			setUserManuallyClickedInfo(false);
		}, 200);
	};

	const handleLocationInfoToggle = () => {
		setShowLocationInfo(!showLocationInfo);
	};

	// Simple and reliable transition logic
	useEffect(() => {
		if (!hasMounted.current) {
			hasMounted.current = true;
			console.log('⏳ Skipping first transition effect run');
			return;
		}
		if (
			!globeRef.current ||
			isFlattened ||
			isTransitioning ||
			isManuallyReturning
		)
			return;

		const checkForTransition = () => {
			const timeSinceLastChange = Date.now() - lastZoomChangeRef.current;

			// Only allow auto-transition if canAutoTransition is true and user is zoomed in close
			if (
				canAutoTransition &&
				timeSinceLastChange >= 80 &&
				currentZoom < ZOOM_CONSTANTS.TRANSITION_THRESHOLD
			) {
				console.log('🌍 Triggering transition at distance:', currentZoom);

				// Clear interval immediately to prevent multiple triggers
				clearInterval(interval);

				// Set transitioning state immediately
				setIsTransitioning(true);

				const lat = currentLocation?.lat || 0;
				const lng = currentLocation?.lng || 0;

				// Position globe for transition with smoother movement
				if (globeRef.current) {
					globeRef.current.pointOfView(
						{
							lat: lat,
							lng: lng,
							altitude: 2.0,
						},
						300
					); // Faster positioning
				}

				// Complete transition after positioning
				setTimeout(() => {
					console.log('🗺️ Completing transition to flat map');
					setIsFlattened(true);
					setIsTransitioning(false);
					setShowInfo(true);
					setShowGlobeTitle(true);
					setShouldFlashInfo(false);
					setFlashCount(0);
					setUserManuallyClickedInfo(false);
				}, 400); // Faster completion

				return; // Exit the effect early
			}
		};

		const interval = setInterval(checkForTransition, 50); // Check every 50ms for faster response
		return () => clearInterval(interval);
	}, [
		currentZoom,
		isFlattened,
		isManuallyReturning,
		isTransitioning,
		currentLocation,
		canAutoTransition,
	]);

	useEffect(() => {
		setIsClient(true);

		// Load countries data for borders - with fallback
		const loadCountriesData = async () => {
			try {
				// Try to fetch from external source first
				const response = await fetch(
					'https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson'
				);
				if (response.ok) {
					const data = await response.json();
					setCountriesData(data.features);
					return;
				}
			} catch (error) {
				console.warn(
					'External countries data fetch failed, using fallback:',
					error
				);
			}

			// Fallback: Create a minimal world outline
			try {
				const fallbackData = {
					type: 'FeatureCollection',
					features: [
						{
							type: 'Feature',
							properties: { NAME: 'World', name: 'World' },
							geometry: {
								type: 'Polygon',
								coordinates: [
									[
										[-180, -90],
										[180, -90],
										[180, 90],
										[-180, 90],
										[-180, -90],
									],
								],
							},
						},
					],
				};
				setCountriesData(fallbackData.features);
				console.log('✅ Using fallback countries data');
			} catch (fallbackError) {
				console.error('Failed to load any countries data:', fallbackError);
				setCountriesData([]);
			}
		};

		loadCountriesData();
	}, []);

	// Auto-hide UI elements after 2 seconds and trigger flash
	useEffect(() => {
		if (showInfo && !userManuallyClickedInfo) {
			const timer = setTimeout(() => {
				setShowInfo(false);
				setShouldFlashInfo(true);
				setFlashCount((prev) => prev + 1);
			}, 2000);

			return () => clearTimeout(timer);
		}
	}, [showInfo, userManuallyClickedInfo]);

	// Auto-hide navigation buttons after 3 seconds
	useEffect(() => {
		if (showNavButtons) {
			const timer = setTimeout(() => {
				setShowNavButtons(false);
			}, 3000);

			return () => clearTimeout(timer);
		}
	}, [showNavButtons]);

	// Auto-hide bottom instructions after 5 seconds
	useEffect(() => {
		if (showBottomInstructions) {
			const timer = setTimeout(() => {
				setShowBottomInstructions(false);
			}, 5000);

			return () => clearTimeout(timer);
		}
	}, [showBottomInstructions]);

	// Auto-hide globe title after 4 seconds
	useEffect(() => {
		if (showGlobeTitle) {
			const timer = setTimeout(() => {
				setShowGlobeTitle(false);
			}, 4000);

			return () => clearTimeout(timer);
		}
	}, [showGlobeTitle]);

	// Flash info panel when needed
	useEffect(() => {
		if (shouldFlashInfo && flashCount < 3) {
			const flashTimer = setTimeout(() => {
				setShowInfo(true);
				setTimeout(() => {
					setShowInfo(false);
					setShouldFlashInfo(false);
				}, 1000);
			}, 2000);

			return () => clearTimeout(flashTimer);
		}
	}, [shouldFlashInfo, flashCount]);

	// Enable auto-transition after initial setup
	useEffect(() => {
		if (userLocation && !isLocationSetupRequired) {
			const timer = setTimeout(() => {
				setCanAutoTransition(true);
			}, 1000); // 1 second delay after location is set

			return () => clearTimeout(timer);
		}
	}, [userLocation, isLocationSetupRequired]);

	// Cleanup function for component unmount
	useEffect(() => {
		return () => {
			// Clear any remaining timers
			if (navButtonsTimerRef.current) {
				clearTimeout(navButtonsTimerRef.current);
			}
			if (bottomInstructionsTimerRef.current) {
				clearTimeout(bottomInstructionsTimerRef.current);
			}
			if (globeTitleTimerRef.current) {
				clearTimeout(globeTitleTimerRef.current);
			}
		};
	}, []);

	// Handle click outside for settings dropdown
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			const target = event.target as Element;
			if (showSettings && !target.closest('.settings-dropdown')) {
				setShowSettings(false);
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		return () => document.removeEventListener('mousedown', handleClickOutside);
	}, [showSettings]);

	// Handle keyboard shortcuts
	useEffect(() => {
		const handleKeyDown = (e: KeyboardEvent) => {
			if (e.key === 'Escape') {
				if (isFlattened) {
					// If in flat map view, return to globe
					console.log('🔄 Escape key - returning to globe');
					isManuallyReturningRef.current = true;
					setManualReturnTime(Date.now());
					setIsManuallyReturning(true);
					setIsFlattened(false);
					// Reset UI elements to show when returning to globe
					setShowInfo(true);
					setShowNavButtons(true);
					setShowBottomInstructions(true);
					setShowGlobeTitle(true);
					setShouldFlashInfo(false);
					setFlashCount(0);
					setUserManuallyClickedInfo(false);
					if (globeRef.current) {
						const lat = currentLocation?.lat || 0;
						const lng = currentLocation?.lng || 0;
						// Add a small delay to ensure state is properly set before positioning
						setTimeout(() => {
							globeRef.current?.pointOfView(
								{
									lat: lat,
									lng: lng,
									altitude: ZOOM_CONSTANTS.INITIAL_ALTITUDE, // Use the same altitude as initial globe view for consistency
								},
								1000
							);
						}, 50);
					}
				} else {
					// If in globe view, go back to previous page
					router.back();
				}
			} else if (e.key === ' ' && isFlattened) {
				// Space key also returns to globe from flat map
				e.preventDefault();
				console.log('🔄 Space key - returning to globe');
				isManuallyReturningRef.current = true;
				setManualReturnTime(Date.now());
				setIsManuallyReturning(true);
				setIsFlattened(false);
				// Reset UI elements to show when returning to globe
				setShowInfo(true);
				setShowNavButtons(true);
				setShowBottomInstructions(true);
				setShowGlobeTitle(true);
				setShouldFlashInfo(false);
				setFlashCount(0);
				setUserManuallyClickedInfo(false);
				if (globeRef.current) {
					const lat = currentLocation?.lat || 0;
					const lng = currentLocation?.lng || 0;
					// Add a small delay to ensure state is properly set before positioning
					setTimeout(() => {
						globeRef.current?.pointOfView(
							{
								lat: lat,
								lng: lng,
								altitude: ZOOM_CONSTANTS.INITIAL_ALTITUDE, // Use the same altitude as initial globe view for consistency
							},
							1000
						);
					}, 50);
				}
			}
		};

		document.addEventListener('keydown', handleKeyDown);
		return () => document.removeEventListener('keydown', handleKeyDown);
	}, [router, isFlattened, currentLocation, isManuallyReturning]);

	const handleGoToUserLocation = useCallback(() => {
		if (userLocation) {
			if (isFlattened) {
				setCurrentLocation({
					lat: userLocation.latitude,
					lng: userLocation.longitude,
				});
				const iframe = document.querySelector(
					'iframe[title="2D Map View"]'
				) as HTMLIFrameElement;
				if (iframe) {
					const bbox = `${userLocation.longitude - 0.01},${
						userLocation.latitude - 0.01
					},${userLocation.longitude + 0.01},${userLocation.latitude + 0.01}`;
					iframe.src = `https://www.openstreetmap.org/export/embed.html?bbox=${bbox}&layer=mapnik&marker=${userLocation.latitude},${userLocation.longitude}`;
				}
			} else if (globeRef.current) {
				globeRef.current.pointOfView(
					{
						lat: userLocation.latitude,
						lng: userLocation.longitude,
						altitude: 2,
					},
					1500
				);
				setCurrentLocation({
					lat: userLocation.latitude,
					lng: userLocation.longitude,
				});
			}
		}
	}, [userLocation, isFlattened]);

	const handleToggleGlobeInfo = useCallback(() => {
		if (showInfo) {
			// If toggling off, clear all timers and immediately hide all
			if (navButtonsTimerRef.current) clearTimeout(navButtonsTimerRef.current);
			if (bottomInstructionsTimerRef.current)
				clearTimeout(bottomInstructionsTimerRef.current);
			if (globeTitleTimerRef.current) clearTimeout(globeTitleTimerRef.current);
			setShowInfo(false);
			setShowNavButtons(false);
			setShowBottomInstructions(false);
			setShowGlobeTitle(false);
			setShouldFlashInfo(false);
			setFlashCount(0);
			setUserManuallyClickedInfo(false);
		} else {
			setShowInfo(true);
			setShowNavButtons(true);
			setShowBottomInstructions(true);
			setShowGlobeTitle(true);
			setShouldFlashInfo(false);
			setFlashCount(0);
			setUserManuallyClickedInfo(true);
		}
	}, [showInfo]);

	// Manual transition trigger for when automatic doesn't work
	const handleManualTransition = () => {
		if (
			!isFlattened &&
			!isTransitioning &&
			currentZoom < ZOOM_CONSTANTS.MANUAL_RETURN_PROTECTION_THRESHOLD
		) {
			console.log('🔧 Manual transition triggered');
			setIsTransitioning(true);

			const lat = currentLocation?.lat || 0;
			const lng = currentLocation?.lng || 0;

			if (globeRef.current) {
				globeRef.current.pointOfView(
					{
						lat: lat,
						lng: lng,
						altitude: 2.2,
					},
					400
				);
			}

			// Single timer for manual transition
			setTimeout(() => {
				console.log('🔧 Manual transition completed');
				setIsFlattened(true);
				setIsTransitioning(false);
				setShowInfo(true);
				setShowGlobeTitle(true);
				setShouldFlashInfo(false);
				setFlashCount(0);
				setUserManuallyClickedInfo(false);
			}, 500);
		}
	};

	useEffect(() => {
		const timer = setTimeout(() => setCanAutoTransition(true), 3000);
		return () => clearTimeout(timer);
	}, []);

	// --- Stable handler refs for event listeners ---
	const goToUserLocationRef = useRef(handleGoToUserLocation);
	const toggleGlobeInfoRef = useRef(handleToggleGlobeInfo);
	useEffect(() => {
		goToUserLocationRef.current = handleGoToUserLocation;
		toggleGlobeInfoRef.current = handleToggleGlobeInfo;
	}, [handleGoToUserLocation, handleToggleGlobeInfo]);

	useEffect(() => {
		function handleGoToUserLocationEvent() {
			goToUserLocationRef.current();
		}
		function handleToggleGlobeInfoEvent() {
			toggleGlobeInfoRef.current();
		}
		window.addEventListener('goToUserLocation', handleGoToUserLocationEvent);
		window.addEventListener('toggleGlobeInfo', handleToggleGlobeInfoEvent);
		return () => {
			window.removeEventListener(
				'goToUserLocation',
				handleGoToUserLocationEvent
			);
			window.removeEventListener('toggleGlobeInfo', handleToggleGlobeInfoEvent);
		};
	}, []);

	// Place the prompt trigger in an effect
	useEffect(() => {
		triggerLocationSetupIfNeeded();
	}, [triggerLocationSetupIfNeeded]);

	// Provide focusOnUserLocation for nav bar and globe view
	const focusOnUserLocation = useCallback(() => {
		if (userLocation && globeRef.current) {
			globeRef.current.pointOfView(
				{
					lat: userLocation.latitude,
					lng: userLocation.longitude,
					altitude: 2,
				},
				1500
			);
			setCurrentLocation({
				lat: userLocation.latitude,
				lng: userLocation.longitude,
			});
		}
	}, [userLocation]);

	return (
		<GlobeNavContext.Provider value={{ focusOnUserLocation }}>
			<div
				className='h-screen w-full overflow-hidden'
				style={{
					background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`,
				}}>
				{/* Floating Globe Controls (like map controls) */}
				<div className='fixed top-8 left-8 z-40 flex flex-col gap-3 bg-white/90 backdrop-blur-md rounded-2xl shadow-lg px-4 py-3 border border-gray-200 items-center'>
					{/* Logo to Home */}
					<button
						onClick={() => router.push('/')}
						className='p-2 rounded-lg hover:bg-blue-100 transition-colors flex items-center justify-center'
						title='Home'
						style={{ background: 'none', border: 'none' }}>
						<Image
							src='/logo/192x192.png'
							alt='Home'
							width={36}
							height={36}
							priority
						/>
					</button>
					{/* Existing Go to User Location button */}
					<button
						onClick={() => {
							if (userLocation) {
								focusOnUserLocation();
							} else {
								alert('No location available. Please set your location.');
							}
						}}
						className={`p-2 rounded-lg transition-colors hover:bg-blue-100 text-blue-700 ${
							!userLocation ? 'opacity-50 cursor-not-allowed' : ''
						}`}
						title={
							userLocation ? 'Go to Your Location' : 'No location available'
						}>
						<FaLocationArrow className='w-5 h-5' />
					</button>
					{/* Info & Navigation button */}
					<button
						onClick={handleToggleGlobeInfo}
						className='p-2 rounded-lg transition-colors hover:bg-blue-100 text-blue-700'
						title='Info & Navigation'>
						<FaInfo className='w-5 h-5' />
					</button>
					{/* Chat Page button */}
					<button
						onClick={() => router.push('/chat')}
						className='p-2 rounded-lg transition-colors hover:bg-blue-100 text-blue-700'
						title='Chat'>
						<FaComments className='w-5 h-5' />
					</button>
					{/* POI Page button */}
					<button
						onClick={() => router.push('/pois')}
						className='p-2 rounded-lg transition-colors hover:bg-blue-100 text-blue-700'
						title='Places of Interest'>
						<FaMapMarkerAlt className='w-5 h-5' />
					</button>
				</div>
				{/* Interactive Globe */}
				<Globe
					ref={globeRef}
					isFlattened={isFlattened}
					isClient={isClient}
					countriesData={countriesData}
					userMarker={userMarker}
					currentZoom={currentZoom}
					currentLocation={currentLocation}
					userLocation={
						userLocation
							? {
									lat: userLocation.latitude,
									lng: userLocation.longitude,
							  }
							: null
					}
					onZoomChange={handleZoomChange}
					onLocationChange={handleLocationChange}
					onTransitionTrigger={handleTransitionTrigger}
					onLocationInfoToggle={handleLocationInfoToggle}
					isManuallyReturning={isManuallyReturning}
					isTransitioning={isTransitioning}
					showNavButtons={showNavButtons}
					manualReturnTime={manualReturnTime}
					isManuallyReturningRef={isManuallyReturningRef}
				/>

				{/* 2D Map View when flattened */}
				<FlatMap
					key={isFlattened ? 'flat-map-active' : 'flat-map-inactive'}
					isFlattened={isFlattened}
					isClient={isClient}
					currentLocation={currentLocation}
					userLocation={
						userLocation
							? {
									latitude: userLocation.latitude,
									longitude: userLocation.longitude,
							  }
							: null
					}
					onReturnToGlobe={() => {
						console.log('🔄 Return to Globe button clicked');
						setIsFlattened(false);
						setCurrentZoom(ZOOM_CONSTANTS.INITIAL_ALTITUDE); // Ensure zoom state is reset
						isManuallyReturningRef.current = true;
						// Reset UI elements
						setShowInfo(true);
						setShowNavButtons(true);
						setShowBottomInstructions(true);
						setShowGlobeTitle(true);
						setShouldFlashInfo(false);
						setFlashCount(0);
						setUserManuallyClickedInfo(false);
						// Position globe after a short delay
						setTimeout(() => {
							if (globeRef.current && currentLocation) {
								globeRef.current.pointOfView(
									{
										lat: currentLocation.lat,
										lng: currentLocation.lng,
										altitude: ZOOM_CONSTANTS.INITIAL_ALTITUDE,
									},
									1000
								);
							}
						}, 200);
					}}
				/>

				{/* Info Panel */}
				<InfoPanel
					showInfo={showInfo}
					setShowInfo={setShowInfo}
				/>

				{/* Globe Title - Bottom Center */}
				{showGlobeTitle && (
					<div className='absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20'>
						<div className='bg-white/90 backdrop-blur-sm rounded-xl px-6 py-3 shadow-lg'>
							<div className='flex items-center space-x-3'>
								<div className='w-3 h-3 bg-green-500 rounded-full animate-pulse'></div>
								<span className='text-slate-700 font-medium'>
									Interactive Globe - Global Explorer
								</span>
							</div>
						</div>
					</div>
				)}

				{/* Bottom Instructions - Only in Globe View */}
				{!isFlattened && showBottomInstructions && (
					<div className='absolute bottom-20 left-1/2 transform -translate-x-1/2 z-20'>
						<div className='bg-white/90 backdrop-blur-sm rounded-xl px-6 py-3 shadow-lg'>
							<div className='flex items-center gap-4'>
								<p className='text-slate-700 text-sm font-medium'>
									🌍 Explore the World • 🔍 Zoom close for 2D map • 📍 Discover
									locations
									{isManuallyReturning && (
										<span className='ml-2 text-orange-600'>
											🛡️ Auto-transition protected
										</span>
									)}
								</p>
								{currentZoom < 150 &&
									!isTransitioning &&
									!isManuallyReturning && (
										<button
											onClick={handleManualTransition}
											className='text-xs px-3 py-1 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors whitespace-nowrap'
											title='Switch to Map View'>
											🗺️ Map View
										</button>
									)}
							</div>
						</div>
					</div>
				)}

				{/* Location Info Panel - Only show when clicked */}
				{showLocationInfo && userLocation && (
					<div className='absolute bottom-20 left-6 z-20 animate-slide-down'>
						<div className='bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg max-w-xs'>
							<div className='text-sm text-slate-600'>
								<div className='flex items-center justify-between mb-3'>
									<div className='font-medium text-slate-800'>
										Your Location
									</div>
									<button
										onClick={() => setShowLocationInfo(false)}
										className='text-slate-500 hover:text-slate-700 transition-colors'>
										<FaTimes className='w-3 h-3' />
									</button>
								</div>

								<div className='mb-2 p-2 bg-red-50 rounded-lg'>
									<div className='font-medium text-red-800 text-xs mb-1'>
										📍 Coordinates
									</div>
									<div className='text-red-700'>
										{userLocation.latitude.toFixed(4)},{' '}
										{userLocation.longitude.toFixed(4)}
									</div>
								</div>

								<div className='space-y-1 text-xs'>
									<div>
										🎯 Accuracy: ±{Math.round(userLocation.accuracy || 0)}m
									</div>
									<div>
										⏰ Updated:{' '}
										{Math.floor((Date.now() - userLocation.timestamp) / 1000) <
										60
											? 'just now'
											: `${Math.floor(
													(Date.now() - userLocation.timestamp) / 60000
											  )}m ago`}
									</div>
								</div>
							</div>
						</div>
					</div>
				)}

				{/* Location Setup Modal/Overlay */}
				<LocationSetup
					isOpen={showLocationSetup}
					onComplete={handleLocationSetupComplete}
					pageContext='globe'
					isModal={false}
					setCurrentLocation={setCurrentLocation}
					title='Location Setup Required'
					subtitle='To explore the interactive globe, we need to know your location or you can set it manually.'
				/>
			</div>
		</GlobeNavContext.Provider>
	);
}
