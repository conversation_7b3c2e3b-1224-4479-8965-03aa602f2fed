#!/usr/bin/env node
/** @format */

/**
 * Debug Build Tool
 * Helps debug build issues by running various checks
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Debug Build Tool');
console.log('==================');

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
    console.error('❌ package.json not found. Run this from the application root.');
    process.exit(1);
}

// Function to run command and capture output
function runCommand(command, description) {
    console.log(`\n📋 ${description}`);
    console.log(`Command: ${command}`);
    console.log('─'.repeat(50));
    
    try {
        const output = execSync(command, { 
            encoding: 'utf8', 
            stdio: 'pipe',
            cwd: process.cwd()
        });
        console.log(output);
        return true;
    } catch (error) {
        console.error(`❌ Error: ${error.message}`);
        if (error.stdout) console.log('STDOUT:', error.stdout);
        if (error.stderr) console.error('STDERR:', error.stderr);
        return false;
    }
}

// Run debug checks
console.log('\n🔍 Running debug checks...\n');

const checks = [
    {
        command: 'npm run check-types:dev',
        description: 'TypeScript Type Checking (Development)'
    },
    {
        command: 'npm run lint',
        description: 'ESLint Code Quality Check'
    },
    {
        command: 'npm run build:all-errors',
        description: 'Webpack Build with Error Details'
    }
];

let allPassed = true;

for (const check of checks) {
    const passed = runCommand(check.command, check.description);
    if (!passed) {
        allPassed = false;
    }
}

console.log('\n' + '='.repeat(50));
if (allPassed) {
    console.log('✅ All checks passed! Build should work.');
} else {
    console.log('❌ Some checks failed. Review the errors above.');
}
console.log('='.repeat(50));
