local_llama3
/.venv/
.DS_Store
/cache/
__pycache__/
*/__pycache__/
.env
/venv/
node_modules
/backup/

# macOS system files
.DS_Store

# Node dependencies
node_modules/
/docs/spatial_db/
docs/setup/spatial_db/data_files

# # Environment variables
# .env
# .env.local
# .env.*.local

# Next.js build output
.next/
out/

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# TypeScript cache
*.tsbuildinfo

# VSCode settings
.vscode/

# Build artifacts and temporary files
/temp
/dist
build-errors.log
eslint-output.txt
tasks.txt

# Production builds
/build

# Environment files
.env*.local

# Cache directories
.cache/
node_modules/.cache/

# IDE files
.idea/
*.swp
*.swo

# OS generated files
Thumbs.db
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db